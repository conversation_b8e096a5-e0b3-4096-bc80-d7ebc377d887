package com.harman.legallib

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class LegalModel (

    @SerializedName("version")
    var version: String? = "",

    @SerializedName("eula")
    var eula: Eula? = null,

    @SerializedName("privacy")
    var privacy: Terms? = null

) : Serializable {
    override fun toString(): String {
        return "LegalModelV2(version=$version, eula=$eula, privacy=$privacy)"
    }
}

data class Eula (
    @SerializedName("android")
    var android: Terms? = null
) : Serializable {
    override fun toString(): String {
        return "Eula(android=$android)"
    }
}

data class Terms (
    @SerializedName("en")
    var en: String? = null,

    @SerializedName("zh")
    var cn: String? = null
) : Serializable {
    override fun toString(): String {
        return "Terms(en=$en, cn=$cn)"
    }
}