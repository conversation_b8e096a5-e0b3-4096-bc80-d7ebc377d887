package com.harman.legallib


object LegalConfig {
    enum class Language(var value: String) {
        EN("en"),
        CN("cn"),
        DE("de"),
        FR("fr")
    }

    enum class Type(var value: String) {
        EULA("eula"),
        PRIVACY("privacy")
    }

    private const val JSON_FILE_NAME="legal.json"
    //default version in local
    const val DEFAULT_LEGAL_VERSION = "2.0"

    //legal folder name in remote zip
    internal const val LEGAL_ZIP_FOLDER = "global"

    //accepted version by user
    internal const val ACCEPTED_VERSION = "accepted_version"

    //accepted version by user
    internal const val ACCEPTED_CONTENT_FROM_LOCAL_FILE = "accepted_content_from_local_file"

    //download version
    internal const val NEW_DOWNLOADED_VERSION = "new_downloaded_version"
    //host url
    var HOST_URL = ""
    //legal json url
    var LEGAL_URL = HOST_URL + JSON_FILE_NAME
}