package com.harman.legallib

import android.content.Context
import android.text.TextUtils
import android.util.Log
import com.android.volley.Request
import com.android.volley.RetryPolicy
import com.android.volley.VolleyError
import com.android.volley.toolbox.JsonObjectRequest
import com.android.volley.toolbox.Volley
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.harman.legallib.utils.HttpUtils
import com.harman.legallib.utils.NetworkUtil
import com.harman.legallib.utils.SharePreferenceHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.*
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 1. request remote server legal_global_json.
 * 2. md5/version compare if there is new version download file and unzip file
 * 3. save download status.
 */
object LegalManager {
    private val TAG = LegalManager::class.java.simpleName
    private var localeLegalVersion: String = LegalConfig.DEFAULT_LEGAL_VERSION
    private var acceptingVersion: String = localeLegalVersion
    private var acceptedVersion: String = ""
    private var isAcceptedFromLocalFile: Boolean = true
    private var inited = AtomicBoolean(false)

    private var legalModel: LegalModel? = null
    private val EULA_EN = LegalConfig.Type.EULA.value + "_android_en.txt"

    /**
     * Better to init in Application.
     * all below types will contain TYPE_PRIVACY_POLICY, so don't set LegalConfig.TYPE_PRIVACY_POLICY
     * @see [init]
     */
    @Deprecated("Deprecated method",
            ReplaceWith("init(context, url, LegalConfig.DEFAULT_LEGAL_VERSION)", "com.harman.legallib.LegalManager.init"))
    fun init(context: Context, url: String?) {
        init(context, url, LegalConfig.DEFAULT_LEGAL_VERSION)
    }

    /**
     * Initialize LegalManager by this method first.
     * Any method will not work before LegalManager has been initialized.
     * @param context context of application
     * @param url configuration URL
     * @param currentLegalVersion version of app local legal contents,
     * @see [LegalConfig.DEFAULT_LEGAL_VERSION]
     * latest version can be found in link
     *  *  https://appstorage-dev.onecloud.harman.com/general/legal/v2/legal.json or
     *  *  https://appstorage-stage.onecloud.harman.com/general/legal/v2/legal.json or
     *  *  https://appstorage.onecloud.harman.com/general/legal/v2/legal.json
     */
    fun init(context: Context, url: String?, currentLegalVersion: String) {
        this.localeLegalVersion = currentLegalVersion
        isAcceptedFromLocalFile = SharePreferenceHelper.getBoolean(context, LegalConfig.ACCEPTED_CONTENT_FROM_LOCAL_FILE, true)
        acceptedVersion = SharePreferenceHelper.getString(context, LegalConfig.ACCEPTED_VERSION, "")
        acceptingVersion = SharePreferenceHelper.getString(context, LegalConfig.NEW_DOWNLOADED_VERSION, "")
        if (TextUtils.isEmpty(acceptingVersion) && TextUtils.isEmpty((acceptedVersion))) {
            acceptingVersion = currentLegalVersion
        }

        if (inited.getAndSet(true)) {
            if (BuildConfig.DEBUG) { Log.d(TAG, "Don't init legalModel repeatedly") }
            return
        }

        if (!NetworkUtil.isNetworkConnected(context)) {
            if (BuildConfig.DEBUG) { Log.d(TAG, "Network disconnected") }
            return
        }

        var urlPath = url
        if (urlPath.isNullOrEmpty()) {
            urlPath = LegalConfig.LEGAL_URL
        } else {
            //update host url
            LegalConfig.HOST_URL = urlPath.substring(0, urlPath.lastIndexOf("/") + 1)
        }
        if (BuildConfig.DEBUG) { Log.d(TAG, "URL path: $urlPath, host: ${LegalConfig.HOST_URL}") }

        val jsonObjectRequest = JsonObjectRequest(Request.Method.GET, urlPath, null, { response ->
            if (BuildConfig.DEBUG) { Log.d(TAG, "Configuration: $response") }

            try {
                legalModel = Gson().fromJson(response.toString(), LegalModel::class.java)
            } catch (ex: JsonSyntaxException) {
                if (BuildConfig.DEBUG) { Log.e(TAG, "JsonSyntaxException", ex) }
            }

            legalModel?.also { doInitConfig(context, it) }
        }) {
            if (BuildConfig.DEBUG) { Log.e(TAG, "onErrorResponse", it) }
        }

        jsonObjectRequest.setShouldCache(true).retryPolicy = object : RetryPolicy {
            override fun getCurrentTimeout(): Int {
                return 20000
            }

            override fun getCurrentRetryCount(): Int {
                return 1
            }

            @Throws(VolleyError::class)
            override fun retry(error: VolleyError?) {
                Log.e(TAG, "${error?.stackTraceToString()}")
            }
        }
        Volley.newRequestQueue(context).add(jsonObjectRequest)
    }

    /**
     * Whether legal page should show or not according to the return value
     * When call this function, manager will think UI really showed
     * @param context
     * @return true legal files have update, UI should show legal page.
     *         false legal files don't have update, UI should not show legal page.
     */
    fun isNewVersionAvailable(context: Context): Boolean {
        if (!inited.get()) {
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "must call method init before")
            }
            return false
        }

        if (BuildConfig.DEBUG) {
            Log.d(TAG, "accepted version: $acceptedVersion, accepting version: $acceptingVersion ")
        }
        return if (!TextUtils.isEmpty(acceptedVersion) && !TextUtils.isEmpty(acceptingVersion) && acceptedVersion != acceptingVersion) {
            true
        } else {
            return TextUtils.isEmpty(acceptedVersion)
        }
    }

    /**
     * Please make sure user agreed all policies. Then call this function.
     */
    fun acceptNewVersion(context: Context) {
        if (!inited.get()) {
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "must call method init before")
            }
            return
        }

        if (BuildConfig.DEBUG) {
            Log.d(TAG, "isAcceptedFromLocalFile: $isAcceptedFromLocalFile, acceptNewVersion: $acceptingVersion, localeLegalVersion: $localeLegalVersion")
        }

        if (isAcceptedFromLocalFile && acceptingVersion != localeLegalVersion) {
            isAcceptedFromLocalFile = false
            SharePreferenceHelper.setBoolean(context, LegalConfig.ACCEPTED_CONTENT_FROM_LOCAL_FILE, false)
        }
        SharePreferenceHelper.setString(context, LegalConfig.ACCEPTED_VERSION, acceptingVersion)
        acceptedVersion = acceptingVersion
    }

    /**
     * Get file content with specified [LegalConfig.Type] and language [Locale]
     * @param type [LegalConfig.Type.EULA] or [LegalConfig.Type.PRIVACY]
     * @param language language parameter [Locale.getLanguage], this parameter will be processed as "en"
     * if specified parameter not supported in Legal Model.
     * @return privacy or EULA content with specified language [Locale].
     * @see [getLegalFileContent]
     */
    fun getLegalFileContent(context: Context, type: LegalConfig.Type, language: String): String {
        return getLegalFileContent(context, type, Locale(language, "", ""))
    }

    /**
     * Get file content with specified [LegalConfig.Type] and language [Locale]
     * @param type [LegalConfig.Type.EULA] or [LegalConfig.Type.PRIVACY]
     * @param locale language parameter [Locale], this parameter will be processed as [Locale.ENGLISH]
     * if specified parameter not supported in Legal Model.
     * @return privacy or EULA content with specified language [Locale].
     */
    fun getLegalFileContent(context: Context, type: LegalConfig.Type, locale: Locale): String {
        if (!inited.get()) {
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "must call method init before")
            }
            return ""
        }

        var lang = locale.language
        if (locale.language == Locale.SIMPLIFIED_CHINESE.language) {
            lang = "cn"
        }
        return doGetFileContent(context, type, lang)
    }

    /**
     * get file content to show in UI
     * @param type
     * @return file content to show in UI
     * @see [getLegalFileContent]
     */
    @Deprecated("Deprecated method")
    fun getLegalFileContent(context: Context, type: LegalConfig.Type?, language: LegalConfig.Language?): String {
        return doGetFileContent(context, type ?: LegalConfig.Type.EULA, (language ?: LegalConfig.Language.EN).value)
    }

    /**
     * read content from input stream
     */
    private fun readFile(inputStream: InputStream): String {
        val outputStream = ByteArrayOutputStream()
        val buf = ByteArray(1024)
        var len: Int = inputStream.read(buf)
        while (len != -1) {
            outputStream.write(buf, 0, len)
            len = inputStream.read(buf)
        }
        outputStream.close()
        inputStream.close()
        return outputStream.toString()
    }

    /**
     *  compare local version with remote version
     */
    private fun hasNewVersionOnLine(context: Context, item: LegalModel): Boolean {
//        val oldMD5: String = SharePreferenceHelper.getString(context, LegalConfig.LEGAL_MD5_NEW_DOWNLOADED, "")
//        if (oldMD5 != item.md5) return true
        val oldVersion = SharePreferenceHelper.getString(context, LegalConfig.NEW_DOWNLOADED_VERSION, localeLegalVersion)
        var versionOnLine = item.version ?: ""
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "hasNewVersion localVersion = $oldVersion, versionOnLine = $versionOnLine")
        }
        return !TextUtils.isEmpty(versionOnLine) && versionOnLine != oldVersion
    }

    /**
     * unzip legal content, this method can NOT be called in main thread.
     */
    private fun updateAndSave(context: Context, item: LegalModel): Boolean {
        val eulaPathEn = item.eula?.android?.en
        val path = LegalConfig.HOST_URL + eulaPathEn
        // file name like 4.0.1/
        val fileDir = File(getCacheDir(context) + File.separator + item.version)
        if (!fileDir.exists()) {
            if (BuildConfig.DEBUG) { Log.e(TAG, "updateAndSave mkdirs: ${fileDir.path}") }
            fileDir.mkdirs()
        }
        val fileName = fileDir.absolutePath + File.separator  + EULA_EN
        if (BuildConfig.DEBUG) { Log.e(TAG, "updateAndSave fileName: $fileName") }
        if (BuildConfig.DEBUG) { Log.e(TAG, "updateAndSave path: $path") }

        val file = HttpUtils.downloadFile(path, fileName)
        if (BuildConfig.DEBUG) { Log.e(TAG, "updateAndSave file: $file") }

        if (file != null) {
            return true
        }
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "download failed")
        }
        return false
    }


    private fun getCacheDir(context: Context): String? {
        var dirName = "" //Environment.getExternalStorageDirectory().toString();
        val files = context.getExternalFilesDirs(null)
        if (files != null && files.size > 0) {
            dirName = files[0].absolutePath
        }
        return dirName
    }

    private fun doGetPrivacyLink(lang: String): String {
        return if ("cn" == lang) {
            LegalConfig.HOST_URL + legalModel?.privacy?.cn
        } else {
            legalModel?.privacy?.en
        } ?: ""
    }
    /**
     * Read content from new downloaded files, accepted files or local assets files.
     * Priority is: Downloaded files > Accepted files > Local assets files.
     */
    private fun doGetFileContent(context: Context, type: LegalConfig.Type, lang: String): String {
        if (LegalConfig.Type.PRIVACY == type) {
            return doGetPrivacyLink(lang)
        }

        var fileName = EULA_EN
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "file name:${fileName}")
        }

        var version = SharePreferenceHelper.getString(context, LegalConfig.NEW_DOWNLOADED_VERSION, "")
        if (TextUtils.isEmpty(version) && !isAcceptedFromLocalFile) {
            version = acceptedVersion
        }

        var content: String? = null

        if (TextUtils.isEmpty(version)) { // No downloaded file, no local accepted file, local assets files will be applied
            var inputStream: InputStream? = null
            try {
                inputStream = context.assets.open(fileName)
            } catch (e: Exception) {
                // specified language not found, lang English will be applied.
                fileName = EULA_EN
                if (BuildConfig.DEBUG) {
                    Log.d(TAG, "file: $fileName, language $lang not supported in assets files! Language English will be applied!")
                    Log.d(TAG, "", e)
                }
            }

            if (inputStream == null) {
                try {
                    inputStream = context.assets.open(fileName)
                } catch (e: Exception) {
                }
            }
            inputStream?.also { content = readFile(inputStream) }
        } else {

            var dir = getCacheDir(context) + File.separator + version
            var file = File(dir, fileName)

            if (!file.exists()) { // apply English files.
                fileName = EULA_EN
                if (BuildConfig.DEBUG) {
                    Log.d(TAG, "language $lang not supported in accepted files! Language English will be applied!")
                }
                file = File(dir, fileName)
                if (BuildConfig.DEBUG) {
                    Log.d(TAG, "file name:${fileName}")
                }
            }
            try {
                content = readFile(FileInputStream(file.absolutePath))
            } catch (e: FileNotFoundException) {
                if (BuildConfig.DEBUG) {
                    Log.e(TAG, "", e)
                }
            }
        }
        return content ?: ""
    }

    private fun doInitConfig(context: Context, legalModel: LegalModel) {
        if (!hasNewVersionOnLine(context, legalModel)) {
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "updateAndSave has no new version or md5 equals")
            }
            return
        }

        CoroutineScope(Dispatchers.Default).launch {
            val saved = updateAndSave(context, legalModel)
            // if new version download and unzip
            if (saved) {
                legalModel.version?.also {
                    acceptingVersion = it
                    if (BuildConfig.DEBUG) { Log.d(TAG, "acceptingVersion $acceptingVersion") }
                    SharePreferenceHelper.setString(context, LegalConfig.NEW_DOWNLOADED_VERSION, it)
                }
            }
        }
    }
}
