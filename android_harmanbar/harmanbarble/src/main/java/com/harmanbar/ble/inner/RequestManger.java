package com.harmanbar.ble.inner;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.text.TextUtils;

import com.harmanbar.ble.utils.BLELog;
import com.harmanbar.clj.fastble.BleManager;
import com.harmanbar.clj.fastble.callback.BleWriteCallback;
import com.harmanbar.clj.fastble.data.BleDevice;
import com.harmanbar.clj.fastble.exception.BleException;
import com.harmanbar.clj.fastble.exception.OtherException;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RequestManger {
    private static final String TAG = "RequestManger";


    private static final int MSG_SPLIT_WRITE_NEXT = 0x33;
    private static final int MSG_ADD_DATA = 0x34;


    private static final int INTERVAL_BETWEEN_TWO_RESPONSE = 200;

    private HandlerThread mHandlerThread;
    private Handler mHandler;

    private Queue<byte[]> mDataQueue;
    private Map<String, BleWriteCallback> mWriteCallbacks = new HashMap<>();

    private int mTotalNum;

    boolean splitCompatible = false;//拆分可兼容
    private BleDevice mDevice;
    private String mServiceUUID = "";
    private String mCharacteristicUUID = "";

    public RequestManger(BleDevice device, String service_uuid, String write_uuid) {
        mDevice = device;
        mServiceUUID = service_uuid;
        mCharacteristicUUID = write_uuid;

        mDataQueue = new LinkedList<>();

        mHandlerThread = new HandlerThread("RequestManger");
        mHandlerThread.start();
        BLELog.i(TAG,"mHandlerThread.start()");
        mHandler = new Handler(mHandlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                if (msg.what == MSG_SPLIT_WRITE_NEXT) {
                    writeRequest();
                } else if (msg.what == MSG_ADD_DATA) {
                    byte[] data = (byte[]) msg.obj;


                    if (mDataQueue.size() == 0) {
                        mDataQueue.offer(data);

                        Message message = mHandler.obtainMessage(MSG_SPLIT_WRITE_NEXT);
                        mHandler.sendMessageDelayed(message, INTERVAL_BETWEEN_TWO_RESPONSE);
                    } else {
                        mDataQueue.offer(data);
                    }
                }
            }
        };
    }


    public void write(byte[] data, BleWriteCallback listener) {

        if (listener != null)
            mWriteCallbacks.put(String.valueOf(data.hashCode()), listener);

        Message message = mHandler.obtainMessage(MSG_ADD_DATA);
        message.obj = data;
        mHandler.sendMessage(message);

    }


    private void writeRequest() {
        if (mDataQueue == null)
            return;

        BLELog.i(getDeviceName(), "writeRequest  mDataQueue: " + mDataQueue);

        final byte[] data = mDataQueue.poll();
        if (data == null) {
            BLELog.e(getDeviceName(), "writeRequest data is empty");
            return;
        }

        final BleWriteCallback writeCallback = mWriteCallbacks.remove(String.valueOf(data.hashCode()));


        writeCharacteristic(data, true, false, BLEManager.getInstance().getIntervalBetweenTwoPackage(), new BleWriteCallback() {


            @Override
            public void onWriteFinished(int total) {
                BLELog.i(getDeviceName(), "onWriteFinished   " + data + "   mDataQueue: " + mDataQueue);

                if (mDataQueue.size() > 0) {
                    Message message = mHandler.obtainMessage(MSG_SPLIT_WRITE_NEXT);
                    mHandler.sendMessageDelayed(message, INTERVAL_BETWEEN_TWO_RESPONSE);
                }

            }

            @Override
            public void onWriteSuccess(int current, int total, byte[] justWrite) {
//                BLELog.i(getDeviceName(), data + "ble command onWriteSuccess current:" + current + "  total:" + total + "  justWrite:" + HexUtil.byte2HexStr(justWrite));
                if (writeCallback != null)
                    writeCallback.onWriteSuccess(current, total, justWrite);

            }

            @Override
            public void onWriteFailure(BleException exception) {
                BLELog.e(getDeviceName(), data + " onWriteFailure:" + exception.getDescription() + ", data");

                if (writeCallback != null)
                    writeCallback.onWriteFailure(exception);

                if (mDataQueue.size() > 0) {
                    Message message = mHandler.obtainMessage(MSG_SPLIT_WRITE_NEXT);
                    mHandler.sendMessageDelayed(message, INTERVAL_BETWEEN_TWO_RESPONSE);
                }
            }

        });


    }


    private void writeCharacteristic(byte[] data,
                                     boolean split,
                                     boolean sendNextWhenLastSuccess,
                                     long intervalBetweenTwoPackage,
                                     final BleWriteCallback callback) {

        if (mDevice == null
                || TextUtils.isEmpty(mServiceUUID)
                || TextUtils.isEmpty(mCharacteristicUUID)) {
            BLELog.e(getDeviceName(), "writeCharacteristic return");

            return;
        }


        if (callback == null) {
            throw new IllegalArgumentException("BleWriteCallback can not be Null!");
        }

        if (data == null) {
            BLELog.e(getDeviceName(), "data is Null!");
            callback.onWriteFailure(new OtherException("data is Null!"));
            return;
        }


        BleManager.getInstance().write(mDevice,
                mServiceUUID,
                mCharacteristicUUID,
                data,
                split,
                splitCompatible,
                sendNextWhenLastSuccess,
                intervalBetweenTwoPackage,
                callback
        );

    }


    private String getDeviceName() {
        if (this.mDevice == null) {
            return TAG;
        } else {
            return TAG + " [" + mDevice.getName() + "] ";
        }
    }

    public void release() {
        BLELog.i(TAG,"mHandlerThread.quit()");
        Logger.getLogger("").log(Level.ALL,"mHandlerThread.quit()",new Throwable());

        mHandler.removeMessages(MSG_ADD_DATA);
        mHandler.removeMessages(MSG_SPLIT_WRITE_NEXT);
        mHandler.removeCallbacksAndMessages(null);
        mHandlerThread.quit();
//        mHandler = null;
//        mHandlerThread = null;

    }
}
