package com.harmanbar.ble.listener;

import android.bluetooth.BluetoothGatt;

public abstract class SimpleBLEGattListener implements HBConnectBLEListener {
    public abstract void onConnected();
    public abstract void onConnectError(Exception e);
    @Override
    public void onFailed(int error, String msg) {

    }

    @Override
    public void onStartConnect() {

    }

    @Override
    public void onConnectFail(Exception exception) {
        onConnectError(exception);
    }

    @Override
    public void onConnectSuccess(BluetoothGatt gatt, int status) {
        onConnected();
    }

    @Override
    public void onDisConnected(boolean isActiveDisConnected, BluetoothGatt gatt, int status) {

    }
}
