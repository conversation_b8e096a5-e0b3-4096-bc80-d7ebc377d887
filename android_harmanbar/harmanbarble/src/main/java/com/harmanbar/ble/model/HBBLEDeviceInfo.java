package com.harmanbar.ble.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * {
 * 	"commonInfo": {
 * 		"name": "xxxx",
 * 		"uuid": "xxxx",
 * 		"battery": 0.81,
 * 		"charge_status": 0,
 * 		"protocolVersion": "1.0.2",
 * 		"wifi_5G": "True",
 * 		"firmware": "Linkplay.3.6.7620.0"
 *        }
 * }
 */
public class HBBLEDeviceInfo implements Serializable {

    private static final long serialVersionUID = 6188414031621130163L;
    @SerializedName(value = "error_code", alternate = "res")
    private int res ;

    @SerializedName(value = "commonInfo", alternate = "device_info")
    private HBCommonInfo commonInfo = new HBCommonInfo();

    @SerializedName(value = "wifiSTAInfo")
    private WiFiSTAInfo wifiSTAInfo = new WiFiSTAInfo();

    public WiFiSTAInfo getWifiSTAInfo() {
        return wifiSTAInfo;
    }

    public void setWifiSTAInfo(WiFiSTAInfo wifiSTAInfo) {
        this.wifiSTAInfo = wifiSTAInfo;
    }

    public HBCommonInfo getCommonInfo() {
        return commonInfo;
    }

    public void setCommonInfo(HBCommonInfo commonInfo) {
        this.commonInfo = commonInfo;
    }

    public int getRes() {
        return res;
    }

    public void setRes(int res) {
        this.res = res;
    }

    @Override
    public String toString() {
        return "HBBLEDeviceInfo{" +
                "res=" + res +
                ", commonInfo=" + commonInfo +
                ", wifiSTAInfo=" + wifiSTAInfo +
                '}';
    }
}
