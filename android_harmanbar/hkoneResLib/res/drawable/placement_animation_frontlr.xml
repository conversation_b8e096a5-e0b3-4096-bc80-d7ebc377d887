<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="351dp"
    android:height="351dp"
    android:viewportWidth="351"
    android:viewportHeight="351">
  <path
      android:pathData="M315.96,210.95L135.53,308.76C131.22,311.09 126.38,312.26 121.47,312.17C116.57,312.07 111.78,310.7 107.56,308.2L11.93,254.84C10.76,254.19 9.77,253.23 9.09,252.07C8.41,250.91 8.05,249.59 8.05,248.24C8.05,246.9 8.41,245.57 9.09,244.41C9.77,243.25 10.76,242.3 11.93,241.64L191.34,147.05C195.63,144.75 200.45,143.6 205.32,143.69C210.19,143.79 214.96,145.13 219.16,147.6L316.01,197.74C317.19,198.4 318.17,199.36 318.84,200.52C319.52,201.69 319.88,203.01 319.87,204.36C319.87,205.71 319.5,207.03 318.82,208.19C318.13,209.35 317.14,210.3 315.96,210.95Z"
      android:strokeAlpha="0.1"
      android:fillColor="#566394"
      android:fillAlpha="0.1"/>
  <path
      android:pathData="M292.11,172.84L281.57,178.97L239.75,154.08C239.62,153.99 239.5,153.88 239.43,153.74C239.35,153.6 239.31,153.44 239.31,153.28C239.31,153.12 239.35,152.96 239.43,152.82C239.5,152.68 239.62,152.56 239.75,152.48L246.65,148.46C247.34,148.05 248.11,147.84 248.9,147.84C249.7,147.84 250.47,148.05 251.15,148.46L292.11,172.84Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M241.45,155.07L291.8,185.23L286.85,209.12L238.36,187.14V156.82C238.36,156.46 238.62,153.87 238.86,153.88C239.22,153.88 241.14,154.88 241.45,155.07Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M238.47,168.14L103.47,240.32L146.91,265.55L281.67,189.93L238.47,168.14Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.93,254.96L137.39,261.11L91.79,234.6C91.65,234.52 91.54,234.4 91.46,234.26C91.39,234.12 91.35,233.96 91.35,233.8C91.35,233.64 91.39,233.48 91.46,233.34C91.54,233.2 91.65,233.09 91.79,233L98.69,228.98C99.38,228.58 100.15,228.37 100.94,228.37C101.74,228.37 102.51,228.58 103.19,228.98L147.93,254.96Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M289.84,229.59L146.2,308.29C145.88,308.5 145.5,308.6 145.12,308.59C144.74,308.57 144.37,308.44 144.06,308.21C143.76,307.98 143.53,307.66 143.42,307.29C143.3,306.93 143.3,306.54 143.41,306.17L160.86,249.6C162.39,244.68 165.8,240.58 170.36,238.18L309.27,161.98L291.01,228.2C290.92,228.5 290.77,228.78 290.57,229.02C290.37,229.26 290.12,229.45 289.84,229.59Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M308.1,164.02L170.34,238.22C165.79,240.63 162.38,244.74 160.86,249.66L144.97,308.7L92.77,280.09C92.01,279.64 91.4,278.98 91,278.19C90.61,277.4 90.45,276.52 90.54,275.64L91.33,234.17C91.44,233 91.74,234.38 92.72,234.97L137.33,260.23L144.38,236.76C145.93,231.79 149.39,227.62 153.99,225.17L289.66,152.06C290.89,151.36 292.31,151.08 293.71,151.25C295.12,151.42 296.43,152.04 297.45,153.02L308.79,160.74C309,160.99 309.16,161.28 309.24,161.59C309.32,161.91 309.33,162.24 309.27,162.55C309.2,162.87 309.06,163.17 308.86,163.43C308.65,163.68 308.39,163.89 308.1,164.02Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M341.55,195.42V249.9L338.68,247.78V196.94L341.55,195.42Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M322.65,184V239.37L325.79,236.81V182.33L322.65,184Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M322.65,239.37L319.78,237.26V185.49L322.65,184V239.37Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M344.69,188.13V247.33L341.55,249.9V195.42L338.68,196.94L164.56,289.09V336.43L161.44,339.01V285.68L164.56,284.02L344.69,188.13Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.8,281.6V325.73L141.64,328.29V279.85L144.8,281.6Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M321.4,176L344.69,188.11L161.44,285.68L138.79,273.76L321.4,176Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.44,285.68V339.01L158.59,336.89V289.17L144.8,281.6L141.64,279.85V328.29L138.79,326.17V273.76L139.4,274.08L141.64,275.25L144.8,276.92L159.47,284.64L161.44,285.68Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>

  <path
      android:pathData="M157.62,120.75V144.67L154.48,146.23V128.03L151.62,129.55L45.87,185.45V204.55L42.74,206.12V182.04L46.37,179.87L157.62,120.75Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M26.29,177.75V192.34L23.13,194.9L23.13,176L26.29,177.75Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.76,181.89V205.97L39.91,203.86V185.38L26.12,177.8L22.97,176.05V195.25L20.11,193.14V169.97L20.72,170.29L22.97,171.46L26.12,173.12L40.79,180.84L42.76,181.89Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.4,122.55V146.63L151.55,144.52V126.04L137.76,118.46L134.6,116.72V135.92L131.75,133.8V110.63L132.36,110.95L134.6,112.12L137.76,113.79L152.43,121.51L154.4,122.55Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.58,111.58V138.34L138.72,135.77V109.92L135.58,111.58Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.58,138.29L132.71,136.17V113.07L135.58,111.58V138.29Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.62,115.72V139.65L154.48,141.2L45.87,199.52L42.74,201.09V177.01L46.37,174.84L157.62,115.72Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.62,115.72V139.65L154.48,141.2L102.69,169.35L99.57,170.92V146.84L103.19,144.67L157.62,115.72Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M26.29,172.72V187.31L23.13,189.88L23.13,170.97L26.29,172.72Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.33,103.59L157.62,115.7L42.76,176.86L20.11,164.94L134.33,103.59Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#D6D6D6"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.76,176.86V200.94L20.11,188.11V164.94L20.72,165.26L22.97,166.43L26.12,168.1L40.79,175.82L42.76,176.86Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800912"
      android:fillColor="#F0F1F2"
      android:strokeColor="#8A8D9C"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M28.96,135.79L28.47,155.65C28.4,158.52 28.42,161.7 30.63,163.52C35.79,167.76 48.18,168.79 54.31,164.52C57.44,162.33 57.61,158.03 57.61,154.21V136.98C57.61,124.9 28.77,125.07 28.96,135.79Z"
      android:strokeWidth="2.83333"
      android:fillColor="#E7E9EB"
      android:strokeColor="#148FB1"/>
  <path
      android:pathData="M31.22,137.8L30.72,158.3C30.71,165.03 53.39,167.53 54.95,158.3V138.82C54.95,138.3 55.07,136.33 54.81,135.89C51.36,130.04 31.07,129.38 31.22,137.8Z"
      android:fillColor="#393839"/>
  <path
      android:pathData="M31.19,136.36a11.81,5.19 0,1 0,23.61 0a11.81,5.19 0,1 0,-23.61 0z"
      android:fillColor="#201C1E"/>
  <path
      android:pathData="M32.14,135.89a10.86,4.72 0,1 0,21.72 0a10.86,4.72 0,1 0,-21.72 0z"
      android:fillColor="#4D494D"/>
  <path
      android:pathData="M114.96,89.79L114.47,109.65C114.4,112.52 114.42,115.7 116.63,117.52C121.79,121.76 134.18,122.79 140.31,118.52C143.44,116.33 143.61,112.03 143.61,108.21V90.98C143.61,78.9 114.77,79.07 114.96,89.79Z"
      android:strokeWidth="2.83333"
      android:fillColor="#E7E9EB"
      android:strokeColor="#148FB1"/>
  <path
      android:pathData="M117.22,91.8L116.72,112.31C116.71,119.03 139.39,121.53 140.95,112.31V92.82C140.95,92.3 141.07,90.33 140.81,89.89C137.36,84.04 117.07,83.38 117.22,91.8Z"
      android:fillColor="#393839"/>
  <path
      android:pathData="M117.19,90.36a11.81,5.19 0,1 0,23.61 0a11.81,5.19 0,1 0,-23.61 0z"
      android:fillColor="#201C1E"/>
  <path
      android:pathData="M118.14,89.89a10.86,4.72 0,1 0,21.72 0a10.86,4.72 0,1 0,-21.72 0z"
      android:fillColor="#4D494D"/>
  <path
      android:pathData="M29.88,107.49L24.08,110.79L39.54,119.69L55,94.63L49.59,97.49L49.59,60L29.88,70.55L29.88,107.49Z"
      android:fillColor="#148FB1"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M115.88,62.49L110.08,65.79L125.54,74.69L141,49.63L135.59,52.49L135.59,15L115.88,25.55L115.88,62.49Z"
      android:fillColor="#148FB1"
      android:fillType="evenOdd"/>
</vector>
