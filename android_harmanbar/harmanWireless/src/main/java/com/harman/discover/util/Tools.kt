package com.harman.discover.util

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothManager
import android.content.Context
import android.os.Looper
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.annotation.MainThread
import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.harman.command.common.GeneralGattCommand
import com.harman.command.common.IGeneralCommand
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.MetaDataNotify
import com.harman.command.one.bean.OtaStatus
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SoundscapeV2Item
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.connect.stereoGroupInfo
import com.harman.discover.DeviceStore
import com.harman.discover.ParamExt
import com.harman.discover.bean.BaseDevice
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PortableDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.PartyLightBTDevice
import com.harman.discover.bean.bt.PortableBTDevice
import com.harman.discover.info.EnumDeviceProtocol
import com.harman.discover.info.EnumMediaSourceStatus
import com.harman.discover.info.EnumProductLine
import com.harman.discover.info.OneRole
import com.harman.discover.info.PartyBoxSecondaryInfo
import com.harman.discover.info.PortableBatteryLevel
import com.harman.discover.util.Tools.macAddressCRC
import com.harman.discover.util.Tools.secondaryDevice
import com.harman.discover.util.Tools.toCRC
import com.harman.log.Logger
import com.harmanbar.ble.model.HBApInfo
import com.harmanbar.ble.utils.CRC16
import com.harmanbar.ble.utils.GsonUtil
import com.harmanbar.ble.utils.HexUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.ProductCategory
import com.wifiaudio.action.lpmslib.newtidal.LPMSTidalAction
import com.wifiaudio.model.DeviceInfoExt
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DlnaPlayerStatus
import com.wifiaudio.service.WAConstants.IStorageMediumType
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout
import org.json.JSONObject
import org.teleal.cling.support.playqueue.callback.xml.IPlayQueueType
import java.io.FileInputStream
import java.io.IOException
import java.lang.Float.max
import java.lang.Float.min
import java.lang.Integer.max
import java.lang.Integer.min
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.util.Locale
import java.util.Queue
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import java.util.regex.Matcher
import java.util.regex.Pattern
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.math.abs
import kotlin.math.pow
import kotlin.random.Random


/**
 * Created by gerrardzhang on 2024/1/11.
 */
object Tools {

    fun ByteArray.extractBytes(start: Int, length: Int): ByteArray? {
        if (isEmpty() || start !in indices || length <= 0 || start + length > size) {
            return null
        }

        val bytes = ByteArray(length)
        System.arraycopy(this, start, bytes, 0, length)
        return bytes
    }

    /**
     * @param start - the start index (inclusive).
     * @param end - the end index (exclusive).
     * @param callback - been invoked if cut success.
     */
    inline fun String.safetySubString(start: Int, end: Int, callback: (String) -> Unit = {}): String? {
        if (start < 0 || end < 0 || end <= start || end > length) {
            return null
        }

        val result = substring(start, end)
        callback.invoke(result)
        return result
    }

    /**
     * @param start - the start index (inclusive).
     * @param end - the end index (exclusive).
     * @param callback - been invoked if cut success.
     */
    inline fun ByteArray.safetySubArray(start: Int, end: Int, callback: (ByteArray) -> Unit = {}): ByteArray? {
        if (start < 0 || end < 0 || end <= start || end > size) {
            return null
        }

        val len = end - start
        val result = ByteArray(len)
        System.arraycopy(this, start, result, 0, len)
        callback.invoke(result)
        return result
    }

    /**
     * Cut and map string as little endian. e.g. ABCD => CDAB
     * Will return null if the length of substring is not even after cut.
     *
     * @param start - the start index (inclusive).
     * @param end - the end index (exclusive).
     * @param callback - been invoked if cut success.
     */
    inline fun String.safetySubStringLittleEndian(start: Int, end: Int, callback: (String) -> Unit = {}): String? {
        if (start < 0 || end < 0 || end <= start || end > length || (end - start) % 2 != 0) {
            return null
        }

        val src = substring(start, end)
        if (src.length <= 2) {
            return src
        }

        val result = src.chunked(2).reversed().joinToString("") { it }

        callback.invoke(result)
        return result
    }

    fun PortableBatteryLevel.toPercentage(): Int = when (this) {
        PortableBatteryLevel.BATT_CRITICAL -> 0
        PortableBatteryLevel.BATT_LOW -> 10
        PortableBatteryLevel.BATT_LEVEL_0 -> 20
        PortableBatteryLevel.BATT_LEVEL_1 -> 40
        PortableBatteryLevel.BATT_LEVEL_2 -> 60
        PortableBatteryLevel.BATT_LEVEL_3 -> 80
        PortableBatteryLevel.BATT_LEVEL_4,
        PortableBatteryLevel.BATT_LEVEL_5 -> 100
    }

    internal fun parseBatteryInfo(builder: PartyBoxBTDevice.Builder, batteryHex: String) {
        val batteryData = batteryHex.toIntOrNull(16) ?: return

        if (FLAG_AC_CABLE_WITHOUT_BATTERY == batteryData) {
            builder.acWithoutBattery = true
            return
        } else {
            builder.acWithoutBattery = false
        }

        val batteryAndCharge = HexUtil.bytes2BinStr(byteArrayOf(batteryData.toByte()))
        batteryAndCharge.safetySubString(1, 8) { strBatteryPer ->
            builder.batteryLv = strBatteryPer.toIntOrNull(2)
        }

        batteryAndCharge.safetySubString(0, 1) { strCharging ->
            builder.charging = strCharging.toIntOrNull(2)
        }
    }

    internal fun parseBatteryInfo(builder: PartyLightBTDevice.Builder, batteryHex: String) {
        val batteryData = batteryHex.toIntOrNull(16) ?: return

        if (FLAG_AC_CABLE_WITHOUT_BATTERY == batteryData) {
            builder.acWithoutBattery = true
            return
        } else {
            builder.acWithoutBattery = false
        }

        val batteryAndCharge = HexUtil.bytes2BinStr(byteArrayOf(batteryData.toByte()))
        batteryAndCharge.safetySubString(1, 8) { strBatteryPer ->
            builder.batteryLv = strBatteryPer.toIntOrNull(2)
        }

        batteryAndCharge.safetySubString(0, 1) { strCharging ->
            builder.charging = strCharging.toIntOrNull(2)
        }
    }

    internal fun BaseBTDevice<*, *>.expired(thresholdMills: Long): Boolean {
        if (thresholdMills <= 0) {
            return false
        }

        return abs(System.currentTimeMillis() - this.lastTouchTime) > thresholdMills
    }

    /**
     * @Update 2.23. 0xF0: AC cable without battery
     */
    const val FLAG_AC_CABLE_WITHOUT_BATTERY = 0xF0

    fun Int?.formatBatteryLv(): Int = this?.let { lv ->
        when {
            lv < 0 -> 0
            lv > 100 -> 100
            else -> lv
        }
    } ?: 0

    /**
     * This value indicates whether the network cable is plugged in.
     */
    fun DeviceItem.isEthernet(): Boolean =
        this.devStatus?.apcli0 == "0.0.0.0"

    fun DeviceItem.setPlaybackDuration(playbackDuration: Int?) {
        this.deviceInfoDictionary?.put("playbackDuration", playbackDuration)
    }

    fun DeviceItem.getPlaybackDuration(): Int? =
        this.deviceInfoDictionary?.get("playbackDuration") as? Int

    fun DeviceItem.macAddress(): String? =
        this.deviceInfoDictionary?.get("BT_MAC") as? String

    fun DeviceItem.wlan0(): String? =
        this.deviceInfoDictionary?.get("MAC") as? String

    fun DeviceItem.hmCastVer(): String? =
        this.deviceInfoDictionary?.get("hm_cast_ver") as? String

    /**
     * @return CRC of MAC Address parse from [DeviceItem.deviceInfoDictionary]#BT_MAC in lower case.
     *
     * For saving calculate result, we save CRC of `BT_MAC` after [toCRC] execute at the first time
     * and reuse it later.
     */
    fun DeviceItem.macAddressCRC(): String? {
        val macAddress = macAddress()
        if (macAddress.isNullOrBlank()) {
            return null
        }

        return macAddress.toCRC()
    }

    /**
     * @return CRC of MAC Address in lower case.
     *
     * Parse from [DeviceItem.deviceInfoDictionary]#BT_MAC at the first time, save the calculate result
     * and reuse it for the next time.
     */
    fun DeviceItem.macAddressCRCWithCache(): String? {
        val macAddress = macAddress()
        if (macAddress.isNullOrBlank()) {
            return null
        }

        // Use MAC Address as one part of key for the case that `BT_MAC` changed.
        val crcCacheKey = "CRC_$macAddress"
        (this.deviceInfoDictionary[crcCacheKey] as? String).let { cacheCRC ->
            if (!cacheCRC.isNullOrBlank()) {
                return cacheCRC
            }
        }

        val crc = macAddressCRC()
        if (!crc.isNullOrBlank()) {
            this.deviceInfoDictionary[crcCacheKey] = crc
        }

        return crc
    }

    /**
     * @return Lower case CRC of MAC Address
     */
    fun String.toCRC(): String? {
        val bytesMacAddress = this.macAddress2Bytes() ?: run {
            return null
        }

        return CRC16.getCrc(bytesMacAddress)?.lowercase()
    }

    fun String.macAddress2Bytes(): ByteArray? {
        if (isNullOrBlank()) {
            return null
        }

        val byteList = this.split(":")
            .mapNotNull { hex ->
                hex.toIntOrNull(16)
            }.map { intHex ->
                intHex.toByte()
            }

        if (byteList.size != 6) {
            return null
        }

        return byteList.toByteArray()
    }

    /**
     * @return judge whether two device object represent the same one by comparing CRC of MAC address
     * spec for [OneBTDevice]
     */
    fun equalsMacAddressCRC(bleDevice: BaseBTDevice<*, *>, wifiDevice: DeviceItem): Boolean {
        if (bleDevice !is OneBTDevice) {
            return false
        }

        val bleCRC = bleDevice.macAddressCRC
        if (bleCRC.isNullOrBlank()) {
            return false
        }

        val wifiCRC = wifiDevice.macAddressCRCWithCache()
        if (wifiCRC.isNullOrBlank()) {
            return false
        }

        return bleCRC.equals(wifiCRC, true)
    }

    fun equalsMacAddressCRC(first: DeviceItem, second: DeviceItem): Boolean {
        val firstCRC = first.macAddressCRCWithCache()
        val secondCRC = second.macAddressCRCWithCache()

        return !firstCRC.isNullOrBlank() &&
                !secondCRC.isNullOrBlank() &&
                firstCRC.equals(secondCRC, true)
    }

    fun equalsMacAddressCRC(macAddress: String?, macAddressCRC: String?): Boolean {
        return equalsCRC(crc1 = macAddressCRC, crc2 = macAddress?.toCRC())
    }

    fun equalsCRC(crc1: String?, crc2: String?): Boolean {
        if (crc1.isNullOrBlank() && crc2.isNullOrBlank()) {
            return true
        }

        if (crc1.isNullOrBlank() || crc2.isNullOrBlank()) {
            return true
        }

        return crc1.equals(crc2, true)
    }

    /**
     * @return Diff list between [lasts] and [currents],
     * filter out new items in [currents] but not contains in [lasts],
     * and filter out removed items in [lasts] but not contains in [currents].
     *
     * Judgement: [DeviceItem.macAddressCRC]
     */
    fun diffChanges(lasts: List<DeviceItem>, currents: List<DeviceItem>): DiffResult {
        val newDevs = currents.filter { current ->
            lasts.all { last ->
                !equalsMacAddressCRC(last, current)
            }
        }

        val offlineDevs = lasts.filter { last ->
            currents.all { current ->
                !equalsMacAddressCRC(last, current)
            }
        }

        return DiffResult(newDevs = newDevs, offlineDevs = offlineDevs)
    }

    fun sameMacAddress(first: PartyBoxDevice?, second: PartyBoxDevice?): Boolean =
        sameMacAddress(first = first?.bleDevice, second = second?.bleDevice)

    fun sameMacAddress(first: PartyBoxBTDevice?, second: PartyBoxBTDevice?): Boolean =
        sameAddress(first = first?.macAddress, second = second?.macAddress)

    fun sameAddress(first: String?, second: String?): Boolean {
        if (first.isNullOrBlank() && second.isNullOrBlank()) {
            return false
        }

        if (first.isNullOrBlank() || second.isNullOrBlank()) {
            return false
        }

        return first.equals(second, true)
    }

    /**
     * @return related secondary device object.
     * by searching [DeviceStore.bleDevices] with [PartyBoxBTDevice.secondaryDevice]#[PartyBoxSecondaryInfo.macAddress]
     */
    fun PartyBoxBTDevice.secondaryDevice(): PartyBoxBTDevice? {
        val targetMacAddress = this.secondaryDevice()?.macAddress
        if (targetMacAddress.isNullOrBlank()) {
            return null
        }

        return DeviceStore.bleDevices.filterIsInstance<PartyBoxBTDevice>()
            .firstOrNull { device ->
                sameAddress(device.macAddress, targetMacAddress)
            }
    }

    inline fun <T> ReentrantLock.sync(block: () -> T): T {
        if (isMainThread()) {
            // don't block main thread and skip block running this time.
            if (!this.tryLock(5, TimeUnit.SECONDS)) {
                return block.invoke()
            }

            return try {
                block.invoke()
            } finally {
                this.unlock()
            }
        }

        this.lock()

        return try {
            block.invoke()
        } finally {
            this.unlock()
        }
    }

    fun isMainThread(): Boolean {
        return Looper.getMainLooper() == Looper.myLooper()
    }

    val fullCRC by lazy {
        CRC16.getCrcFromPhoneName().also {
            Logger.i(TAG, "fullCRC >>> $it")
        }
    }

    private val shortCRCLimit63 by lazy {
        CRC16.getCrcFromPhoneName(63).also {
            Logger.i(TAG, "shortCRCLimit63 >>> $it")
        }
    }

    private val shortCRCLimit31 by lazy {
        CRC16.getCrcFromPhoneName(31).also {
            Logger.i(TAG, "shortCRCLimit31 >>> $it")
        }
    }

    /**
     * @return compare [deviceNameCRC] with CRC calculated from phone name.
     * CRC calculated in three modes: full / limit 63 bytes / limit 31 bytes, been regarded as matched if any one pass.
     */
    fun isCRCMatch(deviceNameCRC: String?): Boolean {
        if (deviceNameCRC.isNullOrBlank()) {
            return false
        }

        if (!fullCRC.isNullOrBlank() && deviceNameCRC.equals(fullCRC, true)) {
            return true
        }

        if (!shortCRCLimit63.isNullOrBlank() && deviceNameCRC.equals(shortCRCLimit63, true)) {
            return true
        }

        if (!shortCRCLimit31.isNullOrBlank() && deviceNameCRC.equals(shortCRCLimit31, true)) {
            return true
        }

        return false
    }

    fun BluetoothDevice.macAddressCRC(): String? {
        val dummy = address
        if (dummy.isNullOrBlank()) {
            return null
        }

        val bytes = dummy.macAddress2Bytes() ?: return null
        return CRC16.getCrc(bytes)
    }

    fun Int.supportBrEdr(): Boolean {
        return 0 != and(EnumDeviceProtocol.PROTOCOL_GATT_BR_EDR.value)
    }

    fun Int.targetGattProtocol(): Int = if (supportBrEdr()) {
        BluetoothDevice.TRANSPORT_BREDR
    } else {
        BluetoothDevice.TRANSPORT_LE
    }

    fun BaseBTDevice<*, *>.supportBrEdr(): Boolean {
        return this.protocolFlags.supportBrEdr()
    }

    fun BaseBTDevice<*, *>.supportBLE(): Boolean {
        return 0 != this.protocolFlags.and(EnumDeviceProtocol.PROTOCOL_BLE.value)
    }

    fun BaseBTDevice<*, *>.isBrEdrOnline(): Boolean {
        return supportBrEdr() && isSPPConnected
    }

    // For API level 18 and above, get a reference to BluetoothAdapter through
    // BluetoothManager.
    fun Context.bluetoothAdapter(): BluetoothAdapter? =
        (this.getSystemService(Context.BLUETOOTH_SERVICE) as? BluetoothManager)?.adapter

    @SuppressLint("MissingPermission")
    fun BluetoothGatt.disconnectAndClose() {
        this.let { gatt ->
            gatt.disconnect()
            gatt.close()
        }
    }

    fun sameDevice(device1: Device?, device2: Device?): Boolean {
        return null != device1 && null != device2 && device1.UUID == device2.UUID
    }

    fun sameContent(device1: Device?, device2: Device?): Boolean {
        return null != device1 &&
                null != device2 &&
                sameContent(device1.bleDevice, device2.bleDevice) &&
                sameContent(device1.wifiDevice?.deviceItem, device2.wifiDevice?.deviceItem)
    }

    private fun sameContent(
        bleDevice1: BaseBTDevice<*, *>?,
        bleDevice2: BaseBTDevice<*, *>?
    ): Boolean {
        return (null == bleDevice1 && null == bleDevice2) ||
                (null != bleDevice1 && null != bleDevice2 && bleDevice1.sameContent(bleDevice2))
    }

    // TODO: Not a quite good compare
    private fun sameContent(
        wifiDevice1: DeviceItem?,
        wifiDevice2: DeviceItem?
    ): Boolean {
        return (null == wifiDevice1 && null == wifiDevice2) ||
                (null != wifiDevice1 && null != wifiDevice2 && wifiDevice1 == wifiDevice2)
    }

    fun ByteArray.isLongCommandLE(pid: String): Boolean {
        return !isPartyBoxCategory(pid) &&
                (size > 255 || (size > 1 && GattPacketFormat.LONG_BYTES_COMMAND_LE.contains(this[1])))
    }

    fun ByteArray.isLongCommandBE(pid: String): Boolean {
        return isPartyBoxCategory(pid) &&
                (size > 255 || (size > 1 && GattPacketFormat.LONG_BYTES_COMMAND_LE.contains(this[1])))
    }

    fun isPartyBoxCategory(pid: String?): Boolean {
        return AppConfigurationUtils.getCategory(pid)?.lowercase() == ProductCategory.PartyBox.category.lowercase()
    }

    fun isPartyLightCategory(pid: String?): Boolean {
        return AppConfigurationUtils.getCategory(pid)?.lowercase() == ProductCategory.PartyLight.category.lowercase()
    }

    fun needEqAlgorithm(pid: String?) = AppConfigurationUtils.needEqAlgorithm(pid)

    private val HEXADECIMAL_PATTERN: Pattern = Pattern.compile("\\p{XDigit}+")

    fun String.isHexadecimal(): Boolean {
        val matcher: Matcher = HEXADECIMAL_PATTERN.matcher(this)
        return matcher.matches()
    }

    fun <T> Queue<T>.offerAll(input: Queue<T>) {
        while (input.isNotEmpty()) {
            val next = input.poll()
            this.offer(next)
        }
    }

    fun <T> Queue<T>.offerAll(input: List<T>) {
        input.forEach {
            this.offer(it)
        }
    }

    fun HBApInfo.asString(): String {
        val sb = StringBuilder()

        this.aplist?.forEach { item ->
            sb.append(item).append("\n")
        }

        return sb.toString()
    }

    fun BaseBTDevice<*, *>.productLine(): EnumProductLine {
        return when (this) {
            is PartyBoxBTDevice -> EnumProductLine.PARTY_BOX
            is PortableBTDevice -> EnumProductLine.PORTABLE
            is OneBTDevice -> EnumProductLine.ONE
            is PartyLightBTDevice -> EnumProductLine.PARTY_LIGHT
            is PartyBandBTDevice -> EnumProductLine.BAND_BOX
            else -> EnumProductLine.UNKNOWN
        }
    }

    fun ByteArray.parseDfuCommand(): Byte? {
        return safetySubArray(1, 2)?.getOrNull(0)
    }

    fun Int.toBigEndian4Bytes(): ByteArray = ByteArray(4) { i ->
        shr((3 - i) * 8).toByte()
    }

    fun Int.toLittleEndian2Bytes(): ByteArray = ByteArray(2) { i ->
        shr(i * 8).toByte()
    }

    fun Int.toLittleEndian4Bytes(): ByteArray = ByteArray(4) { i ->
        shr(i * 8).toByte()
    }

    fun ByteArray.toLittleEndianInt(): Int {
        if (size != 4) {
            return 0
        }

        return (this[0].toInt() shl 24) or
                (this[1].toInt() and 0xff shl 16) or
                (this[2].toInt() and 0xff shl 8) or
                (this[3].toInt() and 0xff)
    }

    fun ByteArray.toBigEndianInt(): Int {
        if (size != 4) {
            return 0
        }

        return (this[3].toInt() and 0xff) or
                (this[2].toInt() and 0xff shl 8) or
                (this[1].toInt() and 0xff shl 16) or
                (this[0].toInt() shl 24)
    }

    /**
     * format "03.02.01" or "3.2.1" as 030201
     */
    fun String.dfuFwVersion2Bytes(): ByteArray {
        val versions = split('.')
        return ByteArray(versions.size) { i ->
            versions[i].toIntOrNull()?.toByte() ?: 0
        }
    }

    fun ByteArray?.hex(): String? {
        return if (null != this) HexUtil.byte2HexStr(this) else null
    }

    /**
     * Chunk version format string like 'AABBCC' to int group like [AA, BB, CC]
     */
    fun String.chunkVersionStringWithoutDots(): IntArray =
        this.chunked(2)
            .mapNotNull { subVersion ->
                subVersion.toIntOrNull(16)
            }.toIntArray()

    /**
     * Split version format string like '11.22.33' to int group like [11, 22, 33]
     */
    fun String.splitVersionStringWithDots(): IntArray =
        this.split('.')
            .mapNotNull { subVersion ->
                subVersion.toIntOrNull(10)
            }.toIntArray()

    fun IntArray.isGreaterThan(target: IntArray): Boolean {
        val lim = size.coerceAtMost(target.size)

        for (i in 0 until lim) {
            if (this[i] > target[i]) {
                return true
            }
        }

        return false
    }

    fun IntArray.isEqual(target: IntArray): Boolean {
        val lim = size.coerceAtMost(target.size)

        for (i in 0 until lim) {
            if (this[i] != target[i]) {
                return false
            }
        }

        return true
    }

    fun String.safetyFileLength(): Int = try {
        FileInputStream(this).use {
            it.available()
        }
    } catch (e: IOException) {
        -1
    }

    /**
     * Do repeat [repeatTimes] times if timeout happened while doing [onBlock].
     * The threshold time for each retry was [timeoutMills].
     * Will break and return null if non-TimeoutCancellationException happened.
     */
    suspend fun <T> repeatWithTimeout(
        repeatTimes: Int = 0,
        timeoutMills: Long,
        onBlock: suspend () -> T
    ): T? = repeatWithTimeout(
        repeatTimes = repeatTimes,
        timeoutMills = timeoutMills,
        onBlock = onBlock,
        onTimeout = null
    )

    suspend fun <T> repeatWithTimeout(
        repeatTimes: Int = 0,
        timeoutMills: Long,
        onBlock: suspend () -> T,
        onTimeout: (() -> Unit)?
    ): T? {
        var remainingTime = repeatTimes
        var output: T? = null

        do {
            val result = runCatching {
                if (timeoutMills > 0L) {
                    withTimeout(timeoutMills) {
                        onBlock.invoke()
                    }
                } else {
                    onBlock.invoke()
                }
            }

            if (result.isSuccess) {
                output = result.getOrNull()
                break
            } else if (result.exceptionOrNull() !is TimeoutCancellationException) {
                break
            } else if (result.exceptionOrNull() is TimeoutCancellationException &&
                remainingTime <= 0
            ) {
                onTimeout?.invoke()
            }
        } while (remainingTime-- > 0)

        return output
    }

    fun ByteArray?.isNullOrEmpty(): Boolean {
        return null == this || this.isEmpty()
    }

    /**
     * @return new offset for further array copy.
     */
    fun bytesCopy(src: ByteArray?, dst: ByteArray, dstPos: Int, len: Int): Int {
        src ?: return dstPos

        System.arraycopy(src, 0, dst, dstPos, len)
        return dstPos + len
    }

    fun byteCopy(src: Byte, dst: ByteArray, dstPos: Int, len: Int): Int {
        return bytesCopy(src = byteArrayOf(src), dst = dst, dstPos = dstPos, len = len)
    }

    fun Int.roundPercentage(): Int = min(100, max(0, this))

    fun Float.roundPercentage(): Int = min(100f, max(0f, this)).toInt()

    fun Int.roundInsideLimit(min: Int, max: Int): Int = min(max, max(min, this))

    fun Int.percentageToU32(): Int {
        return this.roundPercentage() * 32 / 100
    }

    fun Int.u32ToPercentage(): Int {
        return when {
            this <= 0 -> 0
            this >= 32 -> 100
            else -> (this.toFloat() / 32 * 100).toInt()
        }
    }

    fun String.isDLNAPlay(): Boolean =
        DlnaPlayerStatus.IPlayStatus.Playing.equals(this, true)

    fun String.isMediaSourcePlaying(): Boolean =
        EnumMediaSourceStatus.PLAYING.value.equals(this, true)

    fun <T> Continuation<T>.safeResume(t: T) = run {
        runCatching {
            this.resume(t)
        }
    }

    fun Float.round(decimals: Int = 2): Float {
        val gain = 10.0.pow(decimals.toDouble()).toInt()
        return (this * gain).toInt().toFloat() / gain
    }

    @MainThread
    fun <T : Device> MutableLiveData<MutableList<T>>.addOrUpdate(src: T) {
        val list = this.value ?: mutableListOf<T>()

        if (list.contains(src)) {
            list.firstOrNull { v ->
                v.UUID == src.UUID
            }?.addOrUpdate(src)
        } else {
            list.add(src)
        }

        this.value = list
    }

    @MainThread
    fun <T : Device> LiveData<MutableList<T>>.contains(src: T): Boolean {
        val list = this.value ?: return false
        return list.contains(src)
    }

    /**
     * @return true if found matched [BaseDevice] by UUID and update.
     */
    @MainThread
    fun <T : Device> MutableLiveData<MutableList<T>>.updateIfExists(src: T): Boolean {
        val list = this.value ?: return false

        return if (list.contains(src)) {
            list.firstOrNull { v ->
                v.UUID == src.UUID
            }?.addOrUpdate(src)

            this.value = list
            true
        } else {
            false
        }
    }

    /**
     * @return true if found matched [BaseDevice] by UUID and update.
     */
    @MainThread
    fun <T : Device?> MutableLiveData<T>.updateIfMatch(src: T): Boolean {
        val actual = this.value ?: return false

        return if (actual.UUID == src?.UUID) {
            actual.addOrUpdate(src)
            this.value = actual
            true
        } else {
            false
        }
    }

    @MainThread
    fun <T> MutableLiveData<List<T>?>.upsertIfMatch(new: T, matcher: (T) -> Boolean) {
        val origins = this.value?.toMutableList() ?: mutableListOf()

        origins.removeAll(matcher)
        origins.add(new)

        this.value = origins
    }

    @MainThread
    fun <T : Device> MutableLiveData<MutableList<T>>.removeIfExists(src: T) {
        val list = this.value ?: return

        list.remove(src)
        this.value = list
    }

    @MainThread
    fun <T : Device?> MutableLiveData<T>.removeIfMatch(src: T) {
        val actual = this.value ?: return

        if (actual.UUID == src?.UUID) {
            this.value = null
        }
    }

    @MainThread
    fun <T> MutableLiveData<List<T>?>.removeIfMatch(matcher: (T) -> Boolean) {
        val origins = this.value?.toMutableList() ?: mutableListOf()

        origins.removeAll(matcher)

        this.value = origins
    }

    fun String?.isGreaterOrEqual(compared: String?): Boolean {
        return greaterCompare(self = this, compared = compared, allowEqual = true)
    }

    fun String?.isVersionEqual(compared: String?): Boolean {
        return isGreaterOrEqual(compared) && !isGreaterThan(compared)
    }


    /**
     * Checks whether firmware update is available by comparing the liveVersion with currentVersion
     * @return True if [this] is greater than [compared] by comparing each num divided by dot.
     */
    fun String?.isGreaterThan(compared: String?): Boolean {
        return greaterCompare(self = this, compared = compared, allowEqual = false)
    }

    fun greaterCompare(self: String?, compared: String?, allowEqual: Boolean): Boolean {
        if (self.isNullOrBlank() || compared.isNullOrBlank()) {
            return false
        }

        val selfVerArray = self.split('.')
        val selfVerNumArray = try {
            selfVerArray.map { it.toInt() }.toTypedArray()
        } catch (e: NumberFormatException) {
            return false
        }

        val comparedVerArray = compared.split('.')
        val comparedVerNumArray = try {
            comparedVerArray.map { it.toInt() }.toTypedArray()
        } catch (e: NumberFormatException) {
            return false
        }

        val maxNumLength = comparedVerNumArray.size.coerceAtLeast(selfVerNumArray.size)

        (0 until maxNumLength).forEach { index ->
            val selfNum = selfVerNumArray.getOrNull(index)
            val comparedNum = comparedVerNumArray.getOrNull(index)
            if (null == selfNum && null == comparedNum) {
                return false
            }

            if (null == selfNum) {
                return allowEqual
            }

            if (null == comparedNum) {
                return allowEqual
            }

            if (selfNum < comparedNum) {
                return false
            }

            if (selfNum > comparedNum) {
                return true
            }
        }

        return allowEqual
    }

    fun isDifferent(self: String?, compared: String?): Boolean {
        if (self.isNullOrBlank() && compared.isNullOrBlank()) {
            return true
        }

        if (self.isNullOrBlank() || compared.isNullOrBlank()) {
            return false
        }

        val selfVerArray = self.split('.')
        val selfVerNumArray = try {
            selfVerArray.map { it.toInt() }.toTypedArray()
        } catch (e: NumberFormatException) {
            return self == compared
        }

        val comparedVerArray = compared.split('.')
        val comparedVerNumArray = try {
            comparedVerArray.map { it.toInt() }.toTypedArray()
        } catch (e: NumberFormatException) {
            return self == compared
        }

        val maxNumLength = comparedVerNumArray.size.coerceAtLeast(selfVerNumArray.size)

        (0 until maxNumLength).forEach { index ->
            val selfNum = selfVerNumArray.getOrNull(index)
            val comparedNum = comparedVerNumArray.getOrNull(index)
            if (null == selfNum && null == comparedNum) {
                return false
            }

            if (null == selfNum || null == comparedNum) {
                return true
            }

            if (selfNum != comparedNum) {
                return true
            }
        }

        return false
    }

    fun statusGreaterOrEqual(actual: OtaStatus?, target: OtaStatus?): Boolean {
        val prev = actual?.status ?: return true
        val next = target?.status ?: return false

        return next >= prev
    }

    fun Int.to4LenBytes(): ByteArray = ByteBuffer.allocate(4).also { bb ->
        bb.putInt(this)
    }.array()

    fun IGeneralCommand.targetCommand(vararg targets: Byte): Boolean {
        return targets.any { target ->
            target == this.commandID
        }
    }

    /**
     * @return bytes derived from hex string. Will throw exception if contains non-hex string or not even length.
     */
    @Throws
    fun String.decodeHex(): ByteArray {
        check(length % 2 == 0) { "Must have an even length" }

        return chunked(2)
            .map { it.toInt(16).toByte() }
            .toByteArray()
    }

    fun ByteArray.append(byte: Byte) = append(byteArrayOf(byte))

    fun ByteArray.append(bytes: ByteArray?): ByteArray {
        val src = this
        if (null == bytes || bytes.isEmpty()) {
            return src
        }

        val appended = ByteArray(src.size + bytes.size)
        System.arraycopy(src, 0, appended, 0, src.size)
        System.arraycopy(bytes, 0, appended, src.size, bytes.size)
        return appended
    }

    fun ByteArray.insert(index: Int, byte: Byte): ByteArray {
        val newArray = ByteArray(size + 1)
        for (i in 0 until index) {
            newArray[i] = this[i]
        }
        newArray[index] = byte
        for (i in index until size) {
            newArray[i + 1] = this[i]
        }
        return newArray
    }

    /**
     * @return a new byte array after append [subCommand], length and it's [payload]
     */
    fun ByteArray.appendSubCommand(subCommand: Byte, payload: ByteArray?): ByteArray {
        val src = this
        if (null == payload) {
            return src
        }

        if (payload.isEmpty()) {
            val appendPayload = ByteArray(src.size + 2)
            System.arraycopy(src, 0, appendPayload, 0, src.size)
            val pivot = src.size
            appendPayload[pivot] = subCommand
            appendPayload[pivot + 1] = 0x0
            return appendPayload
        }

        val appendPayload = ByteArray(src.size + 2 + payload.size)
        val pivot = src.size

        System.arraycopy(src, 0, appendPayload, 0, src.size)
        appendPayload[pivot] = subCommand
        appendPayload[pivot + 1] = payload.size.toByte()
        System.arraycopy(payload, 0, appendPayload, pivot + 2, payload.size)

        return appendPayload
    }

    /**
     * @return a new byte array after append [subCommand], length and it's [payload]
     */
    fun ByteArray.appendSubCommand(subCommand: Byte, payload: Byte): ByteArray {
        val src = this
        val appendPayload = ByteArray(src.size + 3)
        val pivot = src.size

        System.arraycopy(src, 0, appendPayload, 0, src.size)
        appendPayload[pivot] = subCommand
        appendPayload[pivot + 1] = 0x01
        appendPayload[pivot + 2] = payload

        return appendPayload
    }

    /**
     * @return string bytes as UTF-8 within [limitLength]
     */
    fun String.decodeUTF8WithLimit(limitLength: Int): ByteArray {
        if (isBlank()) {
            return byteArrayOf()
        }

        if (limitLength <= 0) {
            return toByteArray(Charsets.UTF_8)
        }

        return if (length > limitLength) {
            substring(0, limitLength)
        } else {
            this
        }.toByteArray(Charsets.UTF_8)
    }

    fun IGeneralCommand.putExtra(commandID: Byte, data: Any?) {
        (this as? GeneralGattCommand)?.setExtraInfo(commandID.toString(), data)
    }

    fun IGeneralCommand.hasExtra(commandID: Byte): Boolean {
        return null != (this as? GeneralGattCommand)?.getExtraInfo(commandID.toString())
    }

    /**
     * First 4 bytes of MD5 value of current timestamp and random value in [1, 10000].
     * Pseudo Code like:
     * md5ForString(currentTimeMillis.toString() + randomValue.toString(), UTF8).substring(0, 8).
     */
    fun geneGroupID(): String {
        val sb = StringBuilder()
        sb.append(System.currentTimeMillis())
        sb.append(Random.nextInt(1, 10000))

        var strMD5: String = md5ForString(sb.toString()) ?: ""
        if (strMD5.isBlank()) {
            strMD5 = System.currentTimeMillis().toString()
        }

        strMD5 = if (strMD5.length > MAX_GROUP_ID_BYTE_LENGTH) {
            strMD5.substring(0, MAX_GROUP_ID_BYTE_LENGTH)
        } else {
            strMD5
        }

        // Make sure first 2 chars will not be both '0'.
        if (strMD5.length >= 2 && "00" == strMD5.substring(0, 2)) {
            strMD5 = strMD5.replaceFirst("00", "01")
        }

        return strMD5
    }

    private const val MAX_GROUP_ID_BYTE_LENGTH = 8

    private val HEX_DIGITS = charArrayOf(
        '0', '1', '2', '3', '4', '5',
        '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'
    )

    fun md5ForString(content: String): String? {
        val macKeyWithoutColon = content.replace(":".toRegex(), "")

        return try {
            val md5 = MessageDigest.getInstance("MD5")
            val bytes = macKeyWithoutColon.uppercase(Locale.getDefault()).toByteArray(
                StandardCharsets.UTF_8
            )
            toHexString(md5.digest(bytes))
        } catch (e: Exception) {
            null
        }
    }

    private fun toHexString(b: ByteArray): String {
        val sb = java.lang.StringBuilder(b.size * 2)
        for (aB in b) {
            sb.append(HEX_DIGITS[aB.toInt() and 0xf0 ushr 4])
            sb.append(HEX_DIGITS[aB.toInt() and 0x0f])
        }
        return sb.toString()
    }

    fun Device.isSupportAuraCastNearby(): Boolean {
        val configSupportCrossAuraCast = pid?.let { AppConfigurationUtils.isSupportedCrossAuraCast(it) } ?: false
        return when (val device = this) {
            is OneDevice -> device.isAuraCastSupport && configSupportCrossAuraCast && !device.isAuraCastDisabled
                    && (OneRole.SINGLE == device.role)
                    && (device.bleConnectable() || device.isGattConnected || device.isBrEdrConnected || device.isWiFiOnline)

            is PartyBoxDevice -> device.isAuraCastSupport && configSupportCrossAuraCast && !device.isAuraCastDisabled
                    && (device.bleConnectable() || device.isGattConnected || device.isBrEdrConnected)

            is PortableDevice -> device.isAuraCastSupport && configSupportCrossAuraCast && !device.isAuraCastDisabled
                    && (device.bleConnectable() || device.isGattConnected || device.isBrEdrConnected)

            is PartyBandDevice -> device.isAuraCastSupport && configSupportCrossAuraCast

            else -> false
        }
    }

    /**
     * [OneDevice] : A2DP connected or (WiFi online and not in standby mode(not AuraCast disabled))
     * [PartyBoxDevice] : A2DP connected
     */
    fun Device.isConnectedInAuraCast(): Boolean = when (val device = this) {
        is OneDevice -> device.isA2DPConnected || (device.isWiFiOnline && !device.isAuraCastDisabled)
        is PartyBoxDevice -> device.isA2DPConnected
        else -> false
    }

    fun Device.supportAuraCast(): Boolean {
        val supportCrossAuraCast = pid?.let { AppConfigurationUtils.isSupportedCrossAuraCast(it) } ?: false
        return when (val device = this) {
            is OneDevice -> device.isAuraCastSupport &&
                    supportCrossAuraCast &&
                    !device.isAuraCastDisabled &&
                    OneRole.SINGLE == device.role &&
                    !device.isStandby

            is PartyBoxDevice -> device.isAuraCastSupport && supportCrossAuraCast && !device.isAuraCastDisabled
            is PortableDevice -> device.isAuraCastSupport && supportCrossAuraCast && !device.isAuraCastDisabled
            is PartyBandDevice -> device.isAuraCastSupport && supportCrossAuraCast
            else -> false
        }
    }

    fun OneDevice.getGroupMode(): GroupMode? {
        return groupInfoExt?.groupInfo?.groupMode?.let { GroupMode.stringToGroupMode(it) }
    }

    fun Device.bleConnectable(): Boolean {
        return when (val device = this) {
            is OneDevice -> device.bleDevice?.bleConnectable ?: false
            is PartyBoxDevice -> device.bleDevice?.bleConnectable ?: false
            is PortableDevice -> device.bleDevice?.bleConnectable ?: false
            else -> false
        }
    }

    fun Device.isNearby(): Boolean {
        return !isBroadcasterOn() && !isReceiverOn() && isSupportAuraCastNearby()
    }

    fun Device.supportMood(): Boolean {
        return pid?.let { AppConfigurationUtils.iSupportMood(it) } ?: false
    }


    /**
     * @return in priority of:
     * 1. A2DP connected & App supported model;
     * 2. A2DP connected;
     * 3. Any.
     */
    fun List<Device>.filterBroadcaster(): Device? {
        val devices = this

        val broadcasters = devices.filter { device ->
            device.isBroadcasterOn()
        }

        if (broadcasters.isEmpty()) {
            return null
        }

        return broadcasters.firstOrNull { broadcaster ->
            broadcaster.isA2DPConnected && AppConfigurationUtils.isSupportedDevice(pid = broadcaster.pid)
        } ?: broadcasters.firstOrNull { broadcaster ->
            broadcaster.isA2DPConnected
        } ?: broadcasters.getOrNull(0)
    }

    fun Device.isBroadcasterOn(): Boolean {
        return isAuraCastOn && isBroadcaster
    }

    fun Device.isReceiverOn(): Boolean {
        return isAuraCastOn && isReceiver
    }

    fun Device.isControllable(): Boolean {
        return isA2DPConnected || isWiFiOnline
    }

    fun <Target> MediatorLiveData<Target>.addSources(vararg sources: LiveData<*>, block: () -> Target?) {
        val mediator = this

        sources.forEach { source ->
            mediator.removeSource(source)
            mediator.addSource(source) {
                mediator.value = block.invoke()
            }
        }
    }

    fun <Target> MediatorLiveData<Target>.removeSources(vararg sources: LiveData<*>) {
        val mediator = this

        sources.forEach { source ->
            mediator.removeSource(source)
        }
    }

    fun <Target> LiveData<List<Target>?>.contains(target: Target): Boolean {
        return true == this.value?.any {
            it == target
        }
    }

    fun <Target> MutableLiveData<List<Target>?>.addIfAbsent(device: Target) {
        if (this.contains(device)) {
            return
        }

        val list = this.value?.toMutableList() ?: mutableListOf<Target>()
        list.add(device)
        this.value = list
    }

    fun <Target> MutableLiveData<List<Target>?>.remove(device: Target) {
        if (!this.contains(device)) {
            return
        }

        val list = this.value?.toMutableList() ?: return
        list.remove(device)
        this.value = list
    }

    fun Any.objectUniID(): Int = System.identityHashCode(this)

    fun threadName(): String {
        return Thread.currentThread().name
    }

    fun <Target> MutableLiveData<List<Target>?>.refresh() {
        val current = this.value ?: return

        this.value = current.toList()
    }

    fun <Target> MutableLiveData<List<Target>?>.updateWithoutReOrder(devices: List<Target>?) {
        if (devices.isNullOrEmpty()) {
            this.value = null
            return
        }

        val currentList = this.value
        if (currentList.isNullOrEmpty()) {
            this.value = devices
            return
        }

        val mutables = currentList.filter { current ->
            devices.contains(current)
        }.toMutableList()

        val appends = devices.filter { new ->
            !mutables.contains(new)
        }

        mutables.addAll(appends)
        this.value = mutables
    }

    fun Any.toJSONObj(): JSONObject {
        return JSONObject(GsonUtil.parseBeanToJson(this))
    }

    fun PartyBoxDevice.foundSecondaryDevice(): PartyBoxDevice? {
        val primary = this
        val groupID = primary.groupID
        if (groupID.isNullOrBlank()) {
            return null
        }

        return DeviceStore.partyBoxDevices.firstOrNull { secondary ->
            primary != secondary &&
                    primary.pid == secondary.pid &&
                    (primary.sameGroupID(secondary) || primary.matchSecondaryMacAddr(secondary))
        }
    }

    fun PartyBoxDevice.hasValidGroupId(): Boolean {
        val primary = this
        val groupID = primary.groupID
        Logger.d(TAG, "mapPartyBox >>> crc ${primary.macAddressCRC}, uuid:[${primary.UUID}], group info${primary.stereoGroupInfo()}")
        if (groupID.isNullOrBlank()) {
            return false
        }

        return groupID != "00000000" && groupID != "0000"
    }

    fun PartyBoxDevice.sameGroupID(coDevice: PartyBoxDevice?): Boolean {
        val primary = this

        val primaryGroupID = primary.groupID?.toCharArray()
        val coGroupID = coDevice?.groupID?.toCharArray()

        if (null == primaryGroupID || null == coGroupID || primaryGroupID.isEmpty() || coGroupID.isEmpty()) {
            return false
        }

        (primaryGroupID.indices).forEach { idx ->
            if (idx >= coGroupID.size) {
                return true
            }

            if (!primaryGroupID[idx].equals(coGroupID[idx], ignoreCase = true)) {
                return false
            }
        }

        return true
    }

    fun PartyBoxDevice.matchSecondaryMacAddr(coDevice: PartyBoxDevice?): Boolean {
        coDevice ?: return false

        val primary = this
        val secondaryMacAddr = primary.secondaryInfo?.macAddress
        if (secondaryMacAddr.isNullOrBlank()) {
            return false
        }

        return secondaryMacAddr.equals(coDevice.macAddress, true)
    }

    fun EditText.showSoftKeyboard() {
        val et = this
        if (et.requestFocus()) {
            val imm = et.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
            imm?.showSoftInput(et, InputMethodManager.SHOW_IMPLICIT)
        }
    }

    fun EditText.hideSoftKeyboard() {
        val et = this
        val imm = et.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(et.windowToken, 0)
    }

    fun Job?.isActive(): Boolean = this?.isActive ?: false

    fun Long.overThreshold(thresholdMills: Long, setter: ((Long) -> Unit)? = null): Boolean {
        val current = System.currentTimeMillis()

        return if (abs(current - this) >= thresholdMills) {
            setter?.invoke(current)
            true
        } else {
            false
        }
    }

    fun <T> List<T>?.printList(): String {
        val list = this ?: return ""

        return StringBuilder().also { sb ->
            list.forEachIndexed { index, item ->
                sb.append("[$index][$item]\n")
            }
        }.toString()
    }

    fun BooleanArray?.printArray(): String {
        val array = this ?: return ""

        return StringBuilder().also { sb ->
            array.forEachIndexed { index, item ->
                sb.append("[$index][$item] ")
            }
        }.toString()
    }

    fun IntArray?.printArray(): String {
        val array = this ?: return ""

        return StringBuilder().also { sb ->
            array.forEachIndexed { index, item ->
                sb.append("[$index][$item] ")
            }
        }.toString()
    }

    fun List<DeviceItem>?.printListMacAddressCRC(): String {
        val list = this ?: return ""

        return StringBuilder().also { sb ->
            list.forEachIndexed { index, item ->
                sb.append("[$index][${item.macAddressCRCWithCache()}] ")
            }
        }.toString()
    }


    fun List<SoundscapeV2Item>.update(req: SetSoundscapeV2ConfigRequest) {
        val items = this

        items.filter { item ->
            null != item.soundscapeId && item.soundscapeId == req.soundscapeId
        }.flatMap { item ->
            item.elementList ?: emptyList()
        }.filter { element ->
            null != element.id && element.id == req.elementId
        }.forEach { element ->
            element.value = req.elementValue
        }
    }

    fun same(
        source: List<Device>,
        compare: List<Device>
    ): Boolean {

        if (source.size != compare.size) {
            return false
        }

        source.forEachIndexed { index, sourceDevice ->
            val compareDevice = compare.getOrNull(index) ?: return false
            if (!sameDevice(sourceDevice, compareDevice)) {
                return false
            }
        }

        return true
    }

    /**
     * Returns a list containing all elements of the original collection and then all elements of the given [devices] collection.
     */
    operator fun Collection<Device>?.plus(devices: Iterable<Device>): List<Device> {
        val olds = this ?: return devices.toList()

        val noDuplicates = devices.filter { device ->
            olds.none { old ->
                device.UUID == old.UUID
            }
        }

        if (noDuplicates.isEmpty()) {
            return olds.toList()
        }

        val result = ArrayList<Device>(olds.size + noDuplicates.size)
        result.addAll(olds)
        result.addAll(noDuplicates)
        return result
    }

    fun Context.getStringFromResource(@StringRes res: Int?, default: String? = ""): String? {
        res ?: return default
        return resources?.getString(res) ?: default
    }

    fun String.truncateSafety(maxByteLength: Int): String {
        val name = this
        val byteArray = name.toByteArray()
        if (byteArray.size <= maxByteLength) {
            return name
        }

        var currentLength = 0
        val result = StringBuilder()

        for (char in name) {
            val charBytes = char.toString().toByteArray()
            if (currentLength + charBytes.size > maxByteLength) {
                break
            }

            result.append(char)
            currentLength += charBytes.size
        }

        return result.toString()
    }

    fun DeviceItem.toPid(): String? = project?.let {
        AppConfigurationUtils.getPidByModelName(it)
    }

    fun DeviceItem.colorStr(): String? {
        val deviceItem = this

        val status = deviceItem.devStatus?.deviceStatus?.replace("0x", "")
        if (status.isNullOrBlank()) {
            return null
        }

        return runCatching {
            JSONObject(status).getString("hm_dev_color").replace("0x", "")
        }.getOrNull()
    }

    fun MetaDataNotify.albumCoverUrl(): String? = when {
        !albumCoverLarge.isNullOrBlank() -> albumCoverLarge
        !albumCoverMedium.isNullOrBlank() -> albumCoverMedium
        !albumCoverSmall.isNullOrBlank() -> albumCoverSmall
        else -> null
    }

    fun JsonElement.safeAsJObj(): JsonObject? = if (isJsonObject) {
        runCatching {
            asJsonObject
        }.getOrNull()
    } else null

    fun JsonElement.safeAsJString(): String? = if (isJsonPrimitive) {
        runCatching {
            asString
        }.getOrNull()
    } else null

    fun JsonElement.safeToString(): String? = runCatching {
        toString()
    }.getOrNull()

    fun calculatePercentage(current: Long?, duration: Long?): Int {
        current ?: return 0
        duration ?: return 0

        if (current <= 0 || duration <= 0) {
            return 0
        }

        return if (current >= duration) {
            100
        } else {
            (current.toFloat() / duration * 100).toInt()
        }
    }

    /**
     * This flag is available only when medium source is not TV or HDMI
     */
    fun DeviceInfoExt.supportDolbyAtoms(): Boolean {
        if ("Dolby Atmos".equals(albumInfo?.actualQuality, true)) {
            return true
        }

        if ("Spatial Audio".equals(albumInfo?.actualQuality, true)) {
            return true
        }

        return "1" == albumInfo?.atmos
    }

    /**
     * This flag is available only when medium source is not TV or HDMI
     */
    fun DeviceInfoExt.supportDolbyAudio(): Boolean {
        return "Dolby Audio".equals(albumInfo?.actualQuality, true)
    }

    fun DeviceInfoExt.supportHD(): Boolean {
        return "HD" == albumInfo?.actualQuality
    }

    fun DeviceInfoExt.supportUHD(): Boolean {
        return "UHD" == albumInfo?.actualQuality
    }

    fun DeviceInfoExt.supportHiRes(): Boolean {
        return true == albumInfo?.actualQuality?.contains("HIRES") || qobuzSupportHiRes()
    }

    fun DeviceInfoExt.qobuzSource(): Boolean =
        true == dlnaTrackSource?.contains(IPlayQueueType.Qobuz, true)

    fun DeviceInfoExt.qobuzSupportHiRes(): Boolean {
        return qobuzSource() && (3 == albumInfo.quality || 4 == albumInfo.quality)
    }

    fun DeviceInfoExt.hiResSampleRate(): String {
        val sampleRate = albumInfo?.actualQuality?.split("_")?.last() ?: ""
        return if (sampleRate.endsWith("K") || sampleRate.endsWith("M")) sampleRate else ""
    }

    /**
     * quality[0]
     * actualQuality: CD_44.1K
     */
    fun DeviceInfoExt.cdQuality(): Boolean {
        return qobuzSource() && (2 == albumInfo.quality || true == albumInfo.actualQuality?.contains("CD"))
    }

    fun DeviceInfoExt.mp3Quality(): Boolean {
        return qobuzSource() && (0 == albumInfo.quality || 1 == albumInfo.quality)
    }

    fun DeviceInfoExt.tidalSource(): Boolean =
        true == dlnaTrackSource?.equals(IPlayQueueType.EXTTIDAL, true)

    fun DeviceItem.highQuality(): Boolean = tidal2HighQuality()

    fun DeviceItem.tidal2HighQuality(): Boolean {
        val deviceItem = this
        if (true != deviceItem.devInfoExt?.tidalSource()) {
            return false
        }

        return LPMSTidalAction.isTidal2(deviceItem, false) &&
                "LOSSLESS" == deviceItem.devInfoExt?.albumInfo?.actualQuality
    }

    fun DeviceInfoExt.metaDataInfo(): String = StringBuilder()
        .also { sb ->
            sb.append("mute[").append(dlnaDesireMute).append("]\n")
            sb.append("playStatus[").append(dlnaPlayStatus).append("]\n")
            sb.append("volume[").append(dlnaCurrentVolume).append("]\n")
            sb.append("title[").append(albumInfo?.title).append("]\n")
            sb.append("artist[").append(albumInfo?.artist).append("]\n")
            sb.append("albumUrl[").append(albumInfo?.albumArtURI).append("]\n")
            sb.append("medium[").append(dlnaPlayMedium).append("]\n")
            sb.append("trackSource[").append(dlnaTrackSource).append("]\n")
            sb.append("totalTime[").append(dlnaTotalTime).append("]\n")
            sb.append("tickTime[").append(dlnaTickTime).append("]\n")
            sb.append("atmos[").append(albumInfo?.atmos).append("]\n")
            sb.append("quality[").append(albumInfo?.quality).append("]\n")
            sb.append("actualQuality[").append(albumInfo?.actualQuality).append("]\n")
        }
        .toString()

    fun <Exts : ParamExt> compareLater(src1: Exts?, src2: Exts?): Exts? {
        src1 ?: return src2
        src2 ?: return src1

        return if (src1.timeStamp > src2.timeStamp) {
            src1
        } else {
            src2
        }
    }

    fun compareLatest(vararg exts: ParamExt?): ParamExt? {
        return exts.maxByOrNull { ext ->
            ext?.timeStamp ?: -1
        } ?: exts.getOrNull(0)
    }

    fun <T> MutableList<T>.addIfAbsent(t: T) {
        if (!contains(t)) {
            add(t)
        }
    }

    fun OneDevice.isGo(): Boolean {
        val device = this
        val groupInfo = device.groupInfoExt?.groupInfo?.groupInfo
        return groupInfo?.group?.id == device.wlan0MacWithoutColon()
    }

    fun OneDevice.isGc(): Boolean {
        val device = this
        val groupId = device.groupInfoExt?.groupInfo?.groupInfo?.group?.id
        return !groupId.isNullOrBlank() && groupId != device.wlan0MacWithoutColon()
    }

    fun OneDevice.findGo(): OneDevice? {
        val device = this
        if (device.isGo()) {
            return device
        } else if (device.isGc()) {
            val groupInfo = device.groupInfoExt?.groupInfo?.groupInfo
            val uuid = groupInfo?.members?.find { memberItem -> memberItem.id == groupInfo.group.id }?.crc
            if (uuid.isNullOrBlank()) {
                return null
            } else {
                return DeviceStore.findOne(uuid)
            }
        } else {
            return null
        }
    }

    fun OneDevice.containGc(mac: String?): Boolean {
        val device = this
        return !mac.isNullOrBlank() && device.groupInfoExt?.groupInfo?.groupInfo?.getGCIds()?.contains(mac) == true
    }

    fun OneDevice.hasWhatsNew(): Boolean {
        val device = this
        return (device.isWiFiOnline && !device.prevOneOsVer.isNullOrBlank() &&
                !device.oneOsVer.isNullOrBlank() &&
                compareVersion(device.prevOneOsVer!!, device.oneOsVer!!) < 0)
//        return false//need update device_abnormal on server
    }

    /**
     * Compares [version1] with [version2] for order.
     * @return
     * * zero if [version1] is equal to the [version2],
     * * a negative number if [version1] is less than [version2],
     * * or a positive number if [version1] is greater than [version2].
     */
    fun compareVersion(version1: String?, version2: String?): Int {
        if (version1 == version2) return 0
        if (version1.isNullOrBlank()) return -1
        if (version2.isNullOrBlank()) return 1

        return runCatching {
            val v1Parts = version1.split(".").map { it.ifEmpty { "0" }.toInt() }
            val v2Parts = version2.split(".").map { it.ifEmpty { "0" }.toInt() }
            val maxLength = maxOf(v1Parts.size, v2Parts.size)

            for (i in 0 until maxLength) {
                val cmp = v1Parts.getOrElse(i) { 0 }.compareTo(v2Parts.getOrElse(i) { 0 })
                if (cmp != 0) return@runCatching cmp
            }
            0
        }.getOrDefault(0)
    }

    fun OneDevice.wlan0MacWithoutColon(): String? {
        return wlan0Mac().removeColon()
    }

    fun String?.removeColon(): String? = this?.replace(":", "")

    fun OneDevice.wlan0Mac(): String? {
        var mac = wifiDevice?.deviceItem?.devStatus?.mac ?: offlineDummy?.devStatus?.mac
        if (mac.isNullOrBlank()) {
            mac = bleDevice?.deviceInfoExt?.deviceInfo?.wlan0Mac
        }
        return mac?.lowercase(Locale.getDefault())
    }

    fun ByteArray.toFloat(): Float? {
        return try {
            ByteBuffer.wrap(this).order(ByteOrder.LITTLE_ENDIAN).float
        } catch (e: Exception) {
            Logger.d(TAG, "toFloat warning: can't parse to float:${HexUtil.byte2HexStr(this)}")
            null
        }
    }

    fun ByteArray.toInt(): Int? {
        return try {
            ByteBuffer.wrap(this).order(ByteOrder.LITTLE_ENDIAN).int
        } catch (e: Exception) {
            Logger.d(TAG, "toInt warning: can't parse to float:${HexUtil.byte2HexStr(this)}")
            null
        }
    }

    fun Int.toBigEndianByteArray(): ByteArray {
        val bytes = ByteArray(4)
        bytes[0] = ((this shr 24) and 0xFF).toByte()
        bytes[1] = ((this shr 16) and 0xFF).toByte()
        bytes[2] = ((this shr 8) and 0xFF).toByte()
        bytes[3] = (this and 0xFF).toByte()
        return bytes
    }

    fun Float.toBigEndianByteArray(): ByteArray {
        val buffer = ByteBuffer.allocate(4).order(ByteOrder.BIG_ENDIAN)

        return try {
            // 将 Float 值放入 ByteBuffer 中
            buffer.putFloat(this)
            // 获取 ByteBuffer 中的字节数组
            buffer.array()
        } catch (e: Exception) {
            Logger.d(TAG, "toByteArray warning: can't parse to bytes:${this}")
            ByteArray(4)
        }
    }

    fun Int.toLittleEndianByteArray(): ByteArray {
        val bytes = ByteArray(4)
        bytes[0] = (this and 0xFF).toByte()
        bytes[1] = ((this shr 8) and 0xFF).toByte()
        bytes[2] = ((this shr 16) and 0xFF).toByte()
        bytes[3] = ((this shr 24) and 0xFF).toByte()
        return bytes
    }

    fun Float.toByteArray(): ByteArray {
        val buffer = ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN)

        return try {
            // 将 Float 值放入 ByteBuffer 中
            buffer.putFloat(this)
            // 获取 ByteBuffer 中的字节数组
            buffer.array()
        } catch (e: Exception) {
            Logger.d(TAG, "toByteArray warning: can't parse to bytes:${this}")
            ByteArray(4)
        }
    }

    fun String.isOnyx(): Boolean = when (this.lowercase()) {
        "210b" -> true
        else -> false
    }

    fun String.isHorizon3(): Boolean = when (this.lowercase()) {
        "2117" -> true
        else -> false
    }

    fun String.isSoundStick5Bt(): Boolean = when (this.lowercase()) {
        "2131" -> true
        else -> false
    }

    fun String.isAuraStudioBt(): Boolean = when (this.lowercase()) {
        "212d" -> true
        else -> false
    }

    fun String.isPartyBox120(): Boolean = when (this.lowercase()) {
        "20dd" -> true
        else -> false
    }

    fun String.isPartyBox320(): Boolean = when (this.lowercase()) {
        "20e2" -> true
        else -> false
    }


    fun isHDMISource(medium: String?): Boolean {
        return IStorageMediumType.HDMI.equals(medium, true)
    }

    fun isTVSource(medium: String?): Boolean {
        return IStorageMediumType.TV.equals(medium, true)
    }

    fun isHDMIorTVSource(medium: String?): Boolean {
        return isHDMISource(medium) || isTVSource(medium)
    }

    private const val TAG = "Tools"
}