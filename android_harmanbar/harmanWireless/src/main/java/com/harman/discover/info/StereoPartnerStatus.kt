package com.harman.discover.info

enum class StereoPartnerStatus (val value: Int, val desc: String){
    OFFLINE(0b0, "OFFLINE"),
    ONLINE(0b1, "ONLINE");

    companion object {
        @JvmStatic
        fun getByValue(status: Int?): StereoPartnerStatus = StereoPartnerStatus.values().find { value ->
            status == value.value
        } ?: OFFLINE

        @JvmStatic
        fun getByValue(status: String?): StereoPartnerStatus = if (status.isNullOrBlank()) {
            OFFLINE
        } else {
            getByValue(status.toIntOrNull(2))
        }
    }

}