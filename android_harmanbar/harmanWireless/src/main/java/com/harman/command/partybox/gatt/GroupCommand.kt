package com.harman.command.partybox.gatt

import com.harman.command.common.IGeneralCommand
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.PartyConnectStatus
import com.harman.discover.util.Tools.appendSubCommand
import com.harman.discover.util.Tools.isAuraStudioBt
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/5/31.
 *
 * 0xAA13 [length] 00 35010* 39010* 3B04******** 3A(0x10 max)**.....
 */
class GroupCommand(
    private val audioChannel: AudioChannel,
    private val connectStatus: PartyConnectStatus,
    private val bytesGroupID: ByteArray?,
    private val bytesGroupName: ByteArray?,
    private val groupID: String?,
    private val groupName: String?,
    private val secondaryDevice: PartyBoxDevice?
) : SetDevInfoCommand(
    assemblePayload(
        audioChannel = audioChannel,
        connectStatus = connectStatus,
        bytesGroupID = bytesGroupID,
        bytesGroupName = bytesGroupName,
        secondaryDevice = secondaryDevice
    )
) {

    override fun onNotify(device: BaseBTDevice<*, *>, receivedCommand: IGeneralCommand): Boolean {
        val result = super.onNotify(device, receivedCommand)

        if (result && device is PartyBoxBTDevice) {
            when (connectStatus) {
                PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING -> {
                    // set stereo group id and name if PartyConnectStatus changed to CONNECTING
                    device.groupIdentifier = groupID
                    device.stereoGroupName = groupName
                }
                PartyConnectStatus.PARTY_CONNECT_OFF -> {
                    // clear stereo group id and name if PartyConnectStatus changed to OFF
                    device.groupIdentifier = null
                    device.stereoGroupName = null
                    device.audioChannel = AudioChannel.NONE_CHANNEL
                    secondaryDevice?.bleDevice?.audioChannel = AudioChannel.NONE_CHANNEL
                }
                PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED -> {
                    device.audioChannel = audioChannel
                    secondaryDevice?.bleDevice?.audioChannel = audioChannel.opposite()
                }
                else -> {
                    // no impl
                }
            }
        }

        return result
    }

    companion object {

        private fun assemblePayload(
            audioChannel: AudioChannel,
            connectStatus: PartyConnectStatus,
            bytesGroupID: ByteArray?,
            bytesGroupName: ByteArray?,
            secondaryDevice:PartyBoxDevice?
        ): ByteArray {
//            var payload = byteArrayOf(GattPacketFormat.ACTIVE_CHANNEL_TOKEN_ID, 0x1, audioChannel.value.toByte())
//            payload = payload.appendSubCommand(GattPacketFormat.PARTY_CONNECT_MODE_TOKEN_ID, byteArrayOf(connectStatus.value.toByte()))
//            payload = payload.appendSubCommand(GattPacketFormat.TWS_STEREO_GROUP_ID, bytesGroupID)
//            payload = payload.appendSubCommand(GattPacketFormat.TWS_STEREO_GROUP_NAME, bytesGroupName)

            var payload = byteArrayOf()
            payload = payload.appendSubCommand(GattPacketFormat.TWS_STEREO_GROUP_ID, bytesGroupID)

            payload = payload.appendSubCommand(GattPacketFormat.PARTY_CONNECT_MODE_TOKEN_ID, byteArrayOf(connectStatus.value.toByte()))

            payload = payload.appendSubCommand(GattPacketFormat.ACTIVE_CHANNEL_TOKEN_ID, byteArrayOf(audioChannel.value.toByte()))

            payload = payload.appendSubCommand(GattPacketFormat.TWS_STEREO_GROUP_NAME, bytesGroupName)
            //in Auras studio 5 make stereo need exchange each other mac address
            secondaryDevice?.bleDevice?.macAddress?.let {
                val macString = it.replace(":", "")
                // do not check byte length
                if (macString.isNotEmpty()) {
                    payload = payload.appendSubCommand(GattPacketFormat.MAC_ADDRESS, HexUtil.hexStr2Bytes(macString))
                }
            }
            Logger.i(TAG, "assemblePayload() >>> ${HexUtil.byte2HexStr(payload)}")

            return payload
        }


        private const val TAG = "GroupCommand"
    }

}