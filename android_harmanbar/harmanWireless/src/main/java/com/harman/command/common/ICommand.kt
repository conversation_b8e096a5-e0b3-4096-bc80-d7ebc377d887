package com.harman.command.common

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/25.
 *
 * Define the interface of commands which worked on all platforms.
 */
interface ICommand<IDType> {

    /**
     * Command ID. Might be different original data type like Byte, Int.
     */
    var commandID: IDType

    /**
     * Get command payload data details
     * @return
     */
    val payload: ByteArray?

    /**
     * Payload length
     */
    val payloadLen: Int
        get() = payload?.size ?: 0

}