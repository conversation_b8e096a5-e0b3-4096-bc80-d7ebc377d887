package com.harman.command.one.command

import com.harman.command.one.EnumCommandMapping

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/25.
 */
class SetCastGroupCommand(private val payloadJson: String) : OneGattCommand() {

    override var commandID: Int = EnumCommandMapping.LP_SET_CAST_GROUP.bleCmd

    override val payload: ByteArray
        get() = payloadJson.toByteArray()

}