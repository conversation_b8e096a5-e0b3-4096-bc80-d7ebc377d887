<!DOCTYPE html>
<html>

<head>
	<title>JBL Portable APP</title>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,Chrome=1" />
	<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="../css/commonstyle.css" />

    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
        }
    </style>
</head>

<body class="body">
    <div class="app center">
        <img src="../res/icon/appicon/google-home-icon.png" class="app-icon-center"/>
        <div class="app-title">Google Home</div>
        <div class="app-desc" id="app-desc">Go to Google Home to unlock all features  of your JBL Bar 9.1</div>
    </div>

    <div class="download bottom" id="douwnload-button" onclick="downloadAppClick()">DOWNLOAD</div>
    

</body>

<script src="./string.js"></script>
<script type="text/javascript" language="javascript">

    window.onload = function() {

        let download = document.getElementById("douwnload-button")

        let installApp = installGoogleHome()

        if (installApp == "true") {
            download.innerText = getLocation("newStructure_redirection_Open")
        } else{
            download.innerText = getLocation("newStructure_redirection_download")
        }

        let desc = document.getElementById("app-desc")
        desc.innerText = getLocation("newStructure_redirection_gotoUnlockFeature").format("Google Home", "JBL Bar 9.1")
    }

    var os = function() {
         var ua = navigator.userAgent,
                 isWindowsPhone = /(?:Windows Phone)/.test(ua),
                 isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
                 isAndroid = /(?:Android)/.test(ua),
                 isFireFox = /(?:Firefox)/.test(ua),
                 isChrome = /(?:Chrome|CriOS)/.test(ua),
                 isIpad = /(?:iPad|Macintosh|PlayBook)/.test(ua),
                 isMac = /(?:Mac)/.test(ua),
                 isTablet = /(?:iPad|Macintosh|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox && /(?:Tablet)/.test(ua)),
                 isPhone = /(?:iPhone)/.test(ua) && !isTablet,
                 isWeixin = /(?:micromessenger)/.test(ua.toLowerCase()),
                 isPc = !isPhone && !isAndroid && !isSymbian && !isIpad,
                 isiOSQQ = ((isPhone || isIpad) && / QQ/i.test(navigator.userAgent)),
                 isAndroidQQ = (isAndroid && /MQQBrowser/i.test(navigator.userAgent) && /QQ/i.test((navigator.userAgent).split('MQQBrowser')));
         return {
             isTablet: isTablet,
             isPhone: isPhone,
             isAndroid: isAndroid,
             isPc: isPc,
             isIpad: isIpad,
             isMac: isMac,
             isWeixin : isWeixin,
             isiOSQQ : isiOSQQ,
             isAndroidQQ : isAndroidQQ
         };
     }();
     function downloadAppClick()  
    {  
        var iOSUrl = ``;  // iOS
        var androidUrl = `https://play.google.com/store/apps/details?id=com.google.android.apps.chromecast.app`;
       
        startDownload(iOSUrl, androidUrl)
    } 
   
    function startDownload(iOSDownloadUrl, androidDownloadUrl)  
    {  
        // if (os.isPhone || os.isIpad) {  
        //     window.open(iOSDownloadUrl)
        // } 
        // else 
        if (os.isAndroid) {
            if (!os.isWeixin) {
                window.open(androidDownloadUrl)
            }
               
        } 
    }  

    function installGoogleHome() {
        var enable = "false";
        var reg = new RegExp("(^|&)" + "install" + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            enable = unescape(r[2]);
        }
        return enable
    }
    
</script>

</html>