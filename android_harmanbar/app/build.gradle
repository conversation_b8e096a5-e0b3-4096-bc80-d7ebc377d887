import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'kotlin-parcelize'
}

repositories {
    maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
    maven { url "https://jitpack.io" }
    mavenCentral()
    google()

    flatDir {
        dirs 'libs', '../harmanWireless/libs'
    }
}

static def generateVersionCode() {
    LocalDateTime now = LocalDateTime.now()
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
    def versionCode = now.format(formatter) + (int) ((now.getHour() * 60 + now.getMinute()) / 15)
    print("versionCode: " +versionCode)
    return versionCode.toInteger()
}

// Get file name for .apk and .aab
static def getFileName(config, prefix, ext) {
    def SEP = "_"
    def version = config.versionName
    def date = new Date()
    def formattedDate = date.format('yyyy-MM-dd-HH-mm', TimeZone.getTimeZone("Asia/Shanghai"))
    def newName = prefix + SEP + version + SEP + formattedDate + ext
    return newName
}

static def getPropertyValue(project,key) {
    Properties localProperties = new Properties()
    File file = project.rootProject.file('local.properties')
    if(file.exists()){
        localProperties.load(file.newDataInputStream())
        return localProperties.get(key, 'false')
    }else{
        return 'false'
    }

}

def ext = rootProject.ext.android
def dependence = rootProject.ext.dependencies

def applicationIdHkOne = "com.harmankardon.oneapp"
def versionNameHkOne = "2.1.11"


def applicationIdJblOne = "com.jbl.oneapp"
def versionNameJblOne = "2.0.0.27"

android {
    dataBinding {
        enabled = true
    }

    buildFeatures {
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.3.1'
    }

    compileSdk rootProject.compileSdkVersion
    ndkVersion ext.ndkVersion

    defaultConfig {
        applicationId applicationIdHkOne
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        versionCode generateVersionCode()
        versionName versionNameHkOne

        multiDexEnabled = true

        buildConfigField "boolean", "ENABLE_LOCAL_FLAG", getPropertyValue(project,'ENABLE_LOCAL_FLAG')
        buildConfigField("String", "commitId", "\"${ext.commitId}\"")
        buildConfigField("String", "RATING_CONFIG_KEY", "\"${ext.ratingInAppConfig}\"")
        buildConfigField "String", "JBL_PORTABLE_APP_URL", "\"https://storage.harman.com/release/portable/appqrcode/jblportableglobal.html\""
        buildConfigField "String", "JBL_PARTYBOX_APP_URL", "\"https://storage.harman.com/release/jblpartybox/appqrcode/jblpartyboxglobal.html\""
        buildConfigField "String", "JBL_BAR_SETUP_APP_URL", "\"https://play.google.com/store/apps/details?id=com.wifiaudio.harmanbar\""
        buildConfigField "String", "GOOGLE_HOME_APP_URL", "\"https://play.google.com/store/apps/details?id=com.google.android.apps.chromecast.app\""
        buildConfigField "boolean", "DEBUG_ACTIVITY", getPropertyValue(project,'DEBUG_ACTIVITY')


        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
        }

        externalNativeBuild {
            cmake {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [rxhttp_rxjava: 'rxjava2']
            }
        }
    }

    signingConfigs {

        release {
            v1SigningEnabled true
            v2SigningEnabled true
            storeFile file("../../third_party_app.jks")
            storePassword "ThirdPartyApp@szharman"
            keyAlias "harman international industries"
            keyPassword "ThirdPartyApp@szharman"
        }

        debug {
            v1SigningEnabled true
            v2SigningEnabled true
            storeFile file('../../HarmanKeystoreDebug.jks')
            storePassword 'Harman@szsw'
            keyPassword 'Harman@szsw'
            keyAlias = 'harman international industries'
        }
    }

    lintOptions {
        abortOnError false
        disable 'MissingTranslation'
        disable 'ExtraTranslation'
        disable 'NotSibling'
    }

    compileOptions {
        targetCompatibility JavaVersion.VERSION_17
        sourceCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
        suppressWarnings = true
    }

    packagingOptions {
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'META-INF/proguard/coroutines.pro'
    }

    buildTypes {

        debug {
            minifyEnabled false
            // firebase crashlytics设置是否要自动上传Mapping文件
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
            signingConfig signingConfigs.debug
            applicationIdSuffix '.debug'
            buildConfigField "boolean", "IS_RELEASE", "false"
            buildConfigField "String", "APP_SERVER_HOST", "\"https://appstorage-dev.onecloud.harman.com\""
            buildConfigField "String", "ONE_CLOUDSERVER_HOST", "\"https://staging.onecloudssodev.com/apps/v1/\""
        }

        staging {
            minifyEnabled false
            initWith debug
            matchingFallbacks = ['debug']

            // firebase crashlytics设置是否要自动上传Mapping文件
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
            signingConfig signingConfigs.debug
            applicationIdSuffix '.debug'
            buildConfigField "boolean", "IS_RELEASE", "false"

            buildConfigField "String", "APP_SERVER_HOST", "\"https://appstorage-stage.onecloud.harman.com\""
            buildConfigField "String", "ONE_CLOUDSERVER_HOST", "\"https://staging.onecloudssodev.com/apps/v1/\""
        }

        release {
            minifyEnabled false
            shrinkResources false //移除无用的资源文件要和minifyEnabled配合使用; 不能为true, 部分drawable并未直接引用
            zipAlignEnabled true //优化zipalign(内存映射优化)
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'test_proguard.pro'

            // firebase crashlytics设置是否要自动上传Mapping文件
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
            buildConfigField "boolean", "IS_RELEASE", "true"
            buildConfigField "boolean", "ENABLE_LOCAL_FLAG", "false"
            buildConfigField "String", "APP_SERVER_HOST", "\"https://appstorage.onecloud.harman.com\""
            buildConfigField "String", "ONE_CLOUDSERVER_HOST", "\"https://apis.onecloud.harman.com/apps/v1/\""
            signingConfig signingConfigs.release//不要忘了要在release的时候加入签名配置信息
            buildConfigField "boolean", "DEBUG_ACTIVITY", "false"
        }
    }

    // 所有风格必须属于指定的风格维度，即产品风格组。
    flavorDimensions "version"

    bundle {
        language.enableSplit = false
    }

    android.applicationVariants.all { variant ->
        def date = new Date()
        def formatDate = date.format('yyyy-MM-dd-HH-mm', TimeZone.getTimeZone("Asia/Shanghai"))
        variant.outputs.all {
            outputFileName = "${variant.name}_${variant.versionName}_${formatDate}.apk"
        }
        rootProject.versionName = versionName
    }

    tasks.whenTaskAdded { task ->
        if (task.name == "bundleRelease" || task.name == "bundleHkoneRelease") {
            // change default app bundle name
            setProperty("archivesBaseName", getFileName(productFlavors.hkone, "hkoneRelease", ""))
        } else if (task.name == "bundleJbloneRelease") {
            setProperty("archivesBaseName", getFileName(productFlavors.jblone, "jbloneRelease", ""))
        }
    }
    productFlavors {
        hkone {
            dimension "version"
            missingDimensionStrategy("remoteConfigModule", "remoteCofig")
            applicationId applicationIdHkOne
            versionCode Long.valueOf(new Date().format("yyyyMMddHH", TimeZone.getTimeZone("Asia/Shanghai"))).intValue()
            versionName versionNameHkOne
            manifestPlaceholders = [
                    applicationName : "@string/applicationName",
            ]
            rootProject.remoteConfigFlavor = "hk_one"
        }
        jblone {
            dimension "version"
            missingDimensionStrategy("remoteConfigModule", "remoteCofig")
            applicationId applicationIdJblOne
            versionCode Long.valueOf(new Date().format("yyyyMMddHH", TimeZone.getTimeZone("Asia/Shanghai"))).intValue()
            versionName versionNameJblOne
            manifestPlaceholders = [
                    applicationName : "@string/applicationName",
            ]
            rootProject.remoteConfigFlavor = "jbl_one"
        }
    }
    sourceSets {
        def hkoneReleaseAssetsDir='../hkoneResLib/src/main/assets'
        def hkoneDebugAssetsDir='../hkoneResLib/src/debug/assets'
        def jbloneReleaseAssetsDir='../jbloneResLib/src/main/assets'
        def jbloneDebugAssetsDir='../jbloneResLib/src/debug/assets'

        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            resources.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
//            res.srcDirs = ['res']
            jniLibs.srcDirs = ['libs']
//            assets.srcDirs = ['assets']
        }
        //replace res by diff flavor
        hkoneDebug.res.srcDirs = ['res', '../hkoneResLib/res','../hkoneResLib/src/debug/res']
        hkoneDebug.assets.srcDirs= ['assets',hkoneDebugAssetsDir]
        hkoneRelease.res.srcDirs = ['res', '../hkoneResLib/res','../hkoneResLib/src/release/res']
        hkoneRelease.assets.srcDirs= ['assets',hkoneReleaseAssetsDir]
        hkoneStaging.res.srcDirs = ['res', '../hkoneResLib/res','../hkoneResLib/src/debug/res']
        hkoneStaging.assets.srcDirs= ['assets',hkoneDebugAssetsDir]


        jbloneDebug.res.srcDirs = ['res', '../jbloneResLib/res','../jbloneResLib/src/debug/res']
        jbloneDebug.assets.srcDirs= ['assets',jbloneDebugAssetsDir]
        jbloneRelease.res.srcDirs = ['res', '../jbloneResLib/res','../jbloneResLib/src/release/res']
        jbloneRelease.assets.srcDirs= ['assets',jbloneReleaseAssetsDir]
        jbloneStaging.res.srcDirs = ['res', '../jbloneResLib/res','../jbloneResLib/src/debug/res']
        jbloneStaging.assets.srcDirs= ['assets',jbloneDebugAssetsDir]


        debug.setRoot('build-types/debug')
        staging.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }
    namespace 'com.harman.bar.app'
}


dependencies {
    implementation fileTree(include: '*.jar', dir: 'libs')
    implementation dependence.rxandroid
    implementation dependence.multidex
    implementation dependence.okhttp3
    implementation dependence.eventbus
    implementation dependence.universal_image_loader
    implementation dependence.gson
    implementation dependence.design
    implementation dependence.autofittextview
    implementation dependence.baseAdapteHelper
    implementation dependence.awsCore
    implementation dependence.awsIot
    implementation dependence.rxjava
    implementation dependence.rxandroid2
    implementation dependence.rxrelay2
    implementation dependence.kotlinjdk
    implementation dependence.kotlinstdlib
    implementation dependence.constraintlayout
    implementation dependence.pdfview
    implementation dependence.glide
    implementation dependence.customtabs
    implementation dependence.mmkv
    implementation dependence.appcompat
    implementation dependence.wheelPicker
    implementation dependence.okhttpLoggingInterceptor
    implementation dependence.retrofit2
    implementation dependence.retrofit2converter
    implementation dependence.retrofit2adapter

    implementation 'com.thanosfisherman.wifiutils:wifiutils:1.6.1'
    implementation 'com.g00fy2:versioncompare:1.3.7'

    implementation 'com.blankj:utilcodex:1.31.1'
//    implementation 'com.jaeger.statusbarutil:library:1.5.1'
    implementation 'com.ljx.rxlife2:rxlife-rxjava:2.0.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'com.king.view:circleprogressview:1.1.2'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation files('libs/login-with-amazon-sdk.jar')
    api 'com.google.android.gms:play-services-location:21.3.0'
    implementation project(':appConfiguration')
    def rxhttp_version = "2.6.5"
    api "com.github.liujingxing.rxhttp:rxhttp:$rxhttp_version"
    api "com.github.liujingxing.rxhttp:converter-simplexml:$rxhttp_version"
//    kapt "com.github.liujingxing.rxhttp:rxhttp-compiler:$rxhttp_version" //生成RxHttp类

    debugImplementation "com.github.chuckerteam.chucker:library-no-op:3.5.2"
    releaseImplementation "com.github.chuckerteam.chucker:library-no-op:3.5.2"

    implementation "androidx.fragment:fragment-ktx:1.8.7"
    def lifecycle_version = "2.5.1"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
//    implementation 'androidx.lifecycle:lifecycle-extensions:$lifecycle_version'
    implementation "androidx.lifecycle:lifecycle-process:$lifecycle_version"
    implementation "androidx.media:media:1.7.0"

    implementation "androidx.datastore:datastore-preferences:1.1.6"
    implementation 'com.github.promeg:tinypinyin:2.0.3'

    //duer_end;

    //implementation project(':linkplay3rdlib')
    implementation(name: 'linkplay3rdlib-release', ext: 'aar')
    //implementation project(path: ':linkplaydnssd')
    implementation(name: 'linkplaydnssd-release', ext: 'aar')
    //implementation project(path: ':linkplayssdp')
    implementation(name: 'linkplayssdp-release', ext: 'aar')
    implementation(name: 'linkplaysdk-release-35bd3d9b', ext: 'aar')

    implementation(name: 'libprintlogs-release', ext: 'aar')
//    implementation(name: 'statisticslibrary', ext: 'aar')
    implementation(name: 'niosdk-release', ext: 'aar')
    implementation(name: 'lpmscommonui', ext: 'aar')
    implementation(name: 'LPMSTidal', ext: 'aar')
    implementation(name: 'LPMSTidalUI', ext: 'aar')
    implementation(name: 'LPMSAmazonMusic', ext: 'aar')
    implementation(name: 'LPMSAmazonMusicUI', ext: 'aar')
    implementation(name: 'LPMSCalmRadio', ext: 'aar')
    implementation(name: 'LPMSCalmRadioUI', ext: 'aar')
    implementation(name: 'LPMSTuneIn', ext: 'aar')
    implementation(name: 'LPMSTuneInUI', ext: 'aar')
    implementation(name: 'LPMSDBKit', ext: 'aar')
    implementation(name: 'amazonmusic-library-release', ext: 'aar')
    implementation(name: 'tuneinlibrary-release', ext: 'aar')

    //new_xmly_end;
    implementation dependence.retrofit2
    implementation dependence.retrofit2converter
    implementation dependence.retrofit2adapter
    implementation dependence.aws_android_sdk_s3

    implementation(name: 'LPMDPKit', ext: 'aar')
    implementation(name: 'bubblepop-release', ext: 'aar')

    implementation 'com.github.andriydruk:rx2dnssd:0.9.13'
    implementation 'org.greenrobot:greendao:3.2.2'
//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.6.1'
//    debugImplementation 'com.squareup.leakcanary:leakcanary-support-fragment:1.6.1'

    //inject class when api<28 && debug==true
    debugImplementation 'com.github.DonaldDu:FixUnhandledEvent:1.0'
    implementation 'androidx.palette:palette-ktx:1.0.0'


    implementation platform('com.google.firebase:firebase-bom:32.0.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-config-ktx:21.1.2'
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.firebase:firebase-messaging:23.0.8'
    implementation 'com.google.firebase:firebase-config:21.1.2'
    implementation 'com.google.android.play:review-ktx:2.0.1'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'

//    implementation(name: 'harmanbarble-release', ext: 'aar')
    implementation 'com.airbnb.android:lottie:6.1.0'
    implementation 'com.github.xiaohaozi9825:BubbleView:1.01'
    implementation 'com.wang.avi:library:2.1.3'
    implementation 'androidx.work:work-runtime-ktx:2.7.1'
    implementation project(':legalLib')

    jbloneImplementation project(':jbloneResLib')
    hkoneImplementation project(':hkoneResLib')
    implementation project(':harmanbarble')
    implementation project(':appLog')
    implementation project(':ratingInApp')
    implementation project(':harmanWireless')
    implementation project(':db')
    implementation project(':encrypt')

    // debugImplementation because LeakCanary should only run in debug builds.

//    if(getPropertyValue(project,'ENABLE_LOCAL_FLAG') == 'true'){
//        debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.10'
//    }
    // TODO testImplementation and androidTestImplementation not working under flavor

    implementation 'junit:junit:4.13.2'
    implementation 'androidx.test.ext:junit:1.1.3'
    implementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation 'io.mockk:mockk:1.11.0'

    implementation "com.github.mcxtzhang:SwipeDelMenuLayout:V1.2.1"

    //-------------------- jetpack compose dependencies start --------------------
//    def composeBom = platform('androidx.compose:compose-bom:2023.08.00')
//    implementation composeBom
//    androidTestImplementation composeBom
//    // Material Design 3
//    implementation 'androidx.compose.material3:material3'
//
//    // Android Studio Preview support
//    implementation 'androidx.compose.ui:ui-tooling-preview'
//    debugImplementation 'androidx.compose.ui:ui-tooling'
//
//    // UI Tests
//    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
//    debugImplementation 'androidx.compose.ui:ui-test-manifest'
//
//    // Optional - Integration with activities
//    implementation 'androidx.activity:activity-compose:1.8.0'
//    // Optional - Integration with ViewModels
//    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:$lifecycle_version"
//    // Optional - Integration with LiveData
//    implementation 'androidx.compose.runtime:runtime-livedata'
//    implementation "androidx.constraintlayout:constraintlayout-compose:1.0.1"
    //-------------------- jetpack compose dependencies end --------------------
}