<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/width_60">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rb_select"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/width_10"
            android:fontFamily="@font/poppins_regular"
            android:singleLine="true"
            android:textColor="@color/fg_primary"
            android:textSize="@dimen/font_12"
            tools:ignore="SpUsage"
            tools:text="Test" />

        <TextView
            android:id="@+id/tv_sub_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/width_10"
            android:alpha="0.5"
            android:fontFamily="@font/poppins_regular"
            android:singleLine="true"
            android:textColor="@color/fg_primary"
            android:textSize="@dimen/font_12"
            tools:ignore="SpUsage"
            tools:text="Test" />
    </LinearLayout>

    <ImageView
        android:id="@+id/rb_select"
        android:layout_width="@dimen/width_20"
        android:layout_height="@dimen/width_20"
        android:layout_marginEnd="@dimen/width_10"
        android:focusable="false"
        android:src="@drawable/shape_security_selected"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/feature_sp1" />

</androidx.constraintlayout.widget.ConstraintLayout>