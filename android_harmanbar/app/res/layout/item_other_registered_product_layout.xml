<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="74dp"
    android:padding="16dp">

    <ImageView
        android:id="@+id/iv_product_image"
        android:layout_width="52dp"
        android:layout_height="38dp"
        android:contentDescription="@null"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Text_Body_Strong"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:gravity="start"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toTopOf="@+id/tv_title_desc"
        app:layout_constraintStart_toEndOf="@+id/iv_product_image"
        app:layout_constraintEnd_toStartOf="@+id/iv_arrow"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="JBL PartyBox Ultimate" />

    <TextView
        android:id="@+id/tv_title_desc"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textColor="@color/fg_secondary"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="RD9879889A89089P" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:minWidth="24dp"
        android:minHeight="24dp"
        android:src="@drawable/ic_arrow_forward_square"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>