<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/launchflow_launchimage_001_an"
    android:clickable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/width_55"
        android:background="@color/header_background">

        <Button
            android:id="@+id/veasy_link_prev"
            style="@style/Text_Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:background="@null"
            android:drawableStart="@drawable/global_deviceaddflow_back_default"
            android:gravity="start|center"
            android:minWidth="@dimen/width_60"
            android:minHeight="0dp"
            android:paddingStart="@dimen/width_5"
            android:text=""
            android:textColor="@color/fg_primary"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/vtxt_label1"
            style="@style/Text_Body_Regular"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/width_60"
            android:layout_toStartOf="@+id/veasy_link_next"
            android:layout_toEndOf="@+id/veasy_link_prev"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="@dimen/width_60"
            android:singleLine="true"
            android:text="ENTER ZIP FOR WEATHER UPDATES"
            android:textColor="@color/fg_primary" />

    </LinearLayout>

    <ImageView
        android:id="@+id/enterzip_sc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />

    <EditText
        android:id="@+id/enterzip_edit"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/enterzip_sc"
        android:layout_marginStart="@dimen/width_20"
        android:layout_marginEnd="@dimen/width_20"
        android:layout_marginBottom="@dimen/width_30"
        android:background="@color/transparent"
        android:gravity="center"
        android:hint="Please Enter Zip Code"
        android:paddingBottom="@dimen/width_10"
        android:textColor="@color/fg_primary"
        android:textColorHint="@color/fg_secondary"
        android:textSize="@dimen/font_20" />

    <TextView
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="@dimen/width_1"
        android:layout_alignBottom="@+id/enterzip_edit"
        android:layout_centerInParent="true"
        android:layout_marginStart="@dimen/width_20"
        android:layout_marginEnd="@dimen/width_20"
        android:background="@color/enterzip_tranwhiteline" />

    <Button
        android:id="@+id/enterzip_btn"
        style="@style/Text_Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/enterzip_sc"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/width_70"
        android:background="@drawable/btn_background_zipcodebtn"
        android:text="COMPLETE SETUP"
        android:textColor="@color/color_txt_zipcodebtn" />

    <Button
        android:id="@+id/skip_btn"
        style="@style/Text_Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/enterzip_btn"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/width_20"
        android:background="@drawable/btn_background_zipcodebtn"
        android:text="SKIP"
        android:textColor="@color/color_txt_zipcodebtn"
        android:visibility="visible" />

</RelativeLayout>
