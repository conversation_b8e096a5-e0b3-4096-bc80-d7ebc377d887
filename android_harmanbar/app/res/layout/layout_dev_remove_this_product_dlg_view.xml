<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_alignParentBottom="true"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="340dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_ble_bg">

        <include
            android:id="@+id/vheader"
            layout="@layout/item_ble_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_label_1"
            style="@style/Text_Body_Regular"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:text=""
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toTopOf="@+id/btn_restore"
            app:layout_constraintTop_toBottomOf="@+id/vheader"
            app:layout_constraintVertical_bias="0" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_end="48dp" />

        <com.harman.widget.RoundCornerButton
            android:id="@+id/btn_restore"
            style="@style/Text_Button"
            android:layout_width="@dimen/width_255"
            android:layout_height="@dimen/width_48"
            android:layout_marginBottom="10dp"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="RESTORE"
            android:textAllCaps="true"
            app:layout_constraintBottom_toTopOf="@+id/btn_cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_goneMarginBottom="0dp" />

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Text_Button"
            android:layout_width="@dimen/width_255"
            android:layout_height="@dimen/width_48"
            android:layout_marginTop="@dimen/width_10"
            android:background="@null"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="CANCEL"
            android:textAllCaps="true"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toTopOf="@+id/guideline"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>