<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:clickable="true"
    android:fitsSystemWindows="true"
    android:focusable="true">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="44dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_snooze"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/shape_round_corner_12_bg_m1"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@+id/appbar">

        <TextView
            android:id="@+id/tv_snooze"
            style="@style/Text_Body_Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:text="@string/snooze"
            android:textColor="@color/fg_primary"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/snooze_switch"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Snooze" />

        <Switch
            android:id="@+id/snooze_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:minWidth="@dimen/width_44"
            android:minHeight="@dimen/width_24"
            android:thumb="@drawable/switch_thumb"
            android:track="@drawable/switch_track"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_snooze"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

        <TextView
            android:id="@+id/tv_snooze_desc"
            style="@style/Text_Caption_1_Regular"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/short_press_the_product_knob_will_trigger_something"
            android:textColor="@color/fg_secondary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@+id/tv_snooze"
            app:layout_constraintRight_toRightOf="@+id/tv_snooze"
            app:layout_constraintTop_toBottomOf="@+id/tv_snooze"
            tools:text="Short press the product knob will trigger a 9 minutes snooze. The long press can stop the alarm." />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>