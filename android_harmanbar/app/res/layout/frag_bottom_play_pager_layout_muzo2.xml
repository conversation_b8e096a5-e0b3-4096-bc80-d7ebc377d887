<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.views.view.slideuplayout.SlidingUpPanelLayout
        android:id="@+id/sliding_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/global_backgound_an"
        android:gravity="bottom">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/vshadow"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/launchflow_launchimage_001_an" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/icon_black_a65"
                android:scaleType="fitXY"
                android:visibility="visible" />

            <include
                android:id="@+id/item_ct_header"
                layout="@layout/item_ct_header" />

            <!--
                        <FrameLayout
                            android:id="@+id/vfrag"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_above="@+id/vfooter"
                            android:layout_below="@id/item_ct_header"
                            android:background="@color/content_bg"></FrameLayout>
            -->

            <RelativeLayout
                android:id="@+id/loading_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/vfooter"
                android:layout_below="@id/item_ct_header"
                android:layout_marginTop="@dimen/width_50"
                android:background="#7F000000"
                android:visibility="gone">

                <include
                    android:id="@+id/vloading"
                    layout="@layout/dlg_custom_loading"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"></include>
            </RelativeLayout>


            <include
                layout="@layout/frag_bottom_player_item_layout_muzo2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/vfooter"
                android:layout_below="@id/item_ct_header" />
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/content_bg"
            android:orientation="vertical"
            android:visibility="visible">

            <FrameLayout
                android:id="@+id/vplayctrl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/content_bg"></FrameLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </FrameLayout>
    </com.views.view.slideuplayout.SlidingUpPanelLayout>

    <!-- <com.menucontroller.menuviewpager.MenuNoneTouchView -->
    <!-- android:id="@+id/vnone_touch_view" -->
    <!-- android:layout_width="match_parent" -->
    <!-- android:layout_height="match_parent" -->
    <!-- android:visibility="visible" /> -->
    <ImageView
        android:id="@+id/iv_empty_click"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/empty_backgound"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/tunein_main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"></FrameLayout>\

</FrameLayout>