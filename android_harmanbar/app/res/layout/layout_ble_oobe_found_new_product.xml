<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.oobe.ble.BLEOOBEDialog" />

        <variable
            name="viewModel"
            type="com.harman.oobe.ble.BLEOOBEViewModel" />

        <variable
            name="flow"
            type="com.harman.oobe.ble.flow.IOOBEFlow" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/oobe_dialog_height"
        android:background="@drawable/radius_large_bg_card"
        android:paddingTop="@dimen/dimen_24dp"
        android:paddingBottom="@dimen/dimen_32dp">

        <TextView
            android:id="@+id/tv_speaker_name"
            style="@style/Text_Brand_Title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dimen_24dp"
            android:textAllCaps="true"
            android:textColor="@color/fg_primary"
            android:text="@{dialog.deviceName}"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="#Speaker Name" />

        <ImageView
            android:id="@+id/iv_device"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="@dimen/dimen_255dp"
            android:layout_marginTop="@dimen/dimen_36dp"
            android:scaleType="centerInside"
            app:deviceImgForce="@{dialog.device}"
            app:layout_constraintBottom_toTopOf="@+id/layout_control"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_speaker_name"
            tools:src="@drawable/ic_device_fg_disable" />

        <com.harman.widget.ComponentButtonDoubleMini
            android:id="@+id/layout_control"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_48dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_device"
            app:negativeButtonText="@string/harmanbar_jbl_LATER"
            app:positiveButtonText="@string/harmanbar_jbl_CONNECT"
            app:setOnNegativeButtonClickListener="@{() -> dialog.onNegativeButtonClick()}"
            app:setOnPositiveButtonClickListener="@{() -> dialog.onPositiveButtonClick()}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>