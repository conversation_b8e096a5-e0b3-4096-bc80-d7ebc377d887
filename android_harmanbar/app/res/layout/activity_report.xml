<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <EditText
        android:id="@+id/report_text"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/linearLayout1"
        android:layout_alignParentStart="true"
        android:ems="10"
        android:inputType="textMultiLine"
        android:textColor="@color/fg_inverse" />

    <LinearLayout
        android:id="@+id/linearLayout1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:padding="8dip">

        <Button
            android:id="@+id/send"
            style="@style/Text_Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dip"
            android:layout_weight="1"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:onClick="send"
            android:text="send"
            android:textColor="@color/fg_inverse" />

        <Button
            android:id="@+id/cancel"
            style="@style/Text_Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dip"
            android:layout_weight="1"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:onClick="send"
            android:text="cancel"
            android:textColor="@color/fg_inverse" />
    </LinearLayout>

</RelativeLayout>