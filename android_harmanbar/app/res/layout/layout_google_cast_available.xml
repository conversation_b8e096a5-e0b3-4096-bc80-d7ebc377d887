<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="iGoogleCast"
            type="com.harman.streaming.google.IGoogleCastDialog" />

        <import type="com.harman.oobe.wifi.flow.ChromeCastUIState" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:background="@color/bg_card">

        <TextView
            android:id="@+id/tv_title_1"
            style="@style/Text_Body_Regular"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_30dp"
            android:text="@string/you_can_cast_music_from_your_favourite_mobile_apps_to_your_device"
            android:textColor="@color/fg_primary" />

        <TextView
            android:id="@+id/tv_title_2"
            style="@style/Text_Caption_1_Regular"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:textColor="@color/fg_secondary"
            app:decoOobeChromecastAvailableTitle2="@{iGoogleCast}"
            tools:text="Your use of Google Cast is subject to the Google Terms of Service. The Google Privacy Policy describes how your data is handled by Google Cast. " />

        <TextView
            android:id="@+id/tv_title_3"
            style="@style/Text_Caption_1_Regular"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/fg_secondary"
            app:decoOobeChromecastAvailableTitle3="@{iGoogleCast}"
            app:isVisible="@{iGoogleCast.enableLaterVisible}"
            tools:text="Tap here to enable later." />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_done"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="@dimen/dimen_48dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dimen_50dp"
            android:layout_marginBottom="@dimen/dimen_32dp"
            android:onClick="@{() -> iGoogleCast.onEnableServiceClick()}"
            app:btn_type="btn_regular"
            app:setTextResource="@{iGoogleCast.enableBtnTextRes}"
            tools:btn_text="ENABLE" />
    </LinearLayout>

</layout>
