<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_alignParentBottom="true"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:minHeight="500dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/vheader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:minWidth="32dp"
                android:minHeight="32dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_title" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text_Title_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:textColor="@color/fg_primary"
                app:layout_constraintStart_toEndOf="@+id/iv_back"
                app:layout_constraintEnd_toStartOf="@+id/iv_close"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginLeft="16dp"
                tools:text="Title Name" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:minWidth="32dp"
                android:minHeight="32dp"
                android:src="@drawable/component_svg_icon_close"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toTopOf="@+id/btn_ok"
            app:layout_constraintTop_toBottomOf="@+id/vheader">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/iv_title_image"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:src="@drawable/svg_icon_chromecast_builtin"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_info1"
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="24dp"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintTop_toBottomOf="@+id/iv_title_image" />

                <TextView
                    android:id="@+id/tv_info2"
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="16dp"
                    android:text=""
                    android:textColor="@color/fg_primary"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/tv_info1" />

                <TextView
                    android:id="@+id/tv_info3"
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="10dp"
                    android:textColor="@color/fg_primary"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_info2"
                    app:layout_constraintVertical_bias="0" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/width_10"
                    android:layout_marginBottom="@dimen/width_10"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_info2">

                    <ImageView
                        android:id="@+id/img_logo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:src="@null" />

                </RelativeLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_end="48dp" />

        <Button
            android:id="@+id/btn_ok"
            style="@style/Text_Button"
            android:layout_width="@dimen/width_255"
            android:layout_height="@dimen/width_48"
            android:layout_marginBottom="10dp"
            android:background="@drawable/btn_background_bg_s1"
            android:gravity="center"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text=""
            android:textColor="@color/fg_inverse"
            app:layout_constraintBottom_toTopOf="@+id/btn_cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_goneMarginBottom="0dp" />

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Text_Button"
            android:layout_width="@dimen/width_255"
            android:layout_height="@dimen/width_48"
            android:background="@null"
            android:gravity="center"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text=""
            android:textColor="@color/fg_primary"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/guideline"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>