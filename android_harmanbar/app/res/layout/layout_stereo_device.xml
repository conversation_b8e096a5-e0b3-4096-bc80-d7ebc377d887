<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.harman.ota.partybox.PartyBoxOtaViewModel" />

        <variable
            name="activity"
            type="com.harman.ota.partybox.PartyBoxOtaActivity" />

    </data>

    <RelativeLayout
        android:id="@+id/vsong_info_topbox_harmanbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"


        android:orientation="vertical">


        <ImageView
            android:id="@+id/img_device"
            isVisible="@{!activity.showStereoLayout}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:scaleType="centerInside"
            app:deviceImgForce="@{viewModel.device}"
            app:layout_constraintHeight_min="@dimen/dimen_180dp"
            app:layout_constraintWidth_min="@dimen/dimen_255dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/two_speaker_layout"
            isVisible="@{activity.showStereoLayout}"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_180dp">

            <ImageView
                android:id="@+id/speaker_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/barrier_right"
                app:layout_constraintStart_toStartOf="@id/barrier_right"
                app:layout_constraintTop_toTopOf="parent"
                app:loadImage="@{activity.rightImgUrl}"
                tools:src="@mipmap/party_box_ultimate" />

            <ImageView
                android:id="@+id/speaker_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/barrier_left"
                app:layout_constraintStart_toStartOf="@id/barrier_left"
                app:layout_constraintTop_toTopOf="parent"
                app:loadImage="@{activity.leftImgUrl}"
                tools:src="@mipmap/party_box_ultimate" />


            <View
                android:id="@+id/barrier_left"
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:layout_marginEnd="@{activity.offset}"
                android:background="@color/transparent"
                app:layout_constraintEnd_toStartOf="@+id/guideline_v" />

            <View
                android:id="@+id/barrier_right"
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:layout_marginStart="@{activity.offset}"
                android:background="@color/transparent"
                app:layout_constraintStart_toEndOf="@+id/guideline_v" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_v"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </RelativeLayout>

</layout>