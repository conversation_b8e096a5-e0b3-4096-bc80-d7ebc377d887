<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/width_104"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="@dimen/width_104"
        android:layout_height="@dimen/width_104"
        android:padding="@dimen/width_3">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_album_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:background="@null"
                android:scaleType="fitCenter" />

            <ImageView
                android:id="@+id/iv_album_cover_float"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:background="@color/bg_surface"
                android:scaleType="fitCenter" />

            <TextView
                android:id="@+id/tv_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/iv_source"
            android:layout_width="@dimen/width_24"
            android:layout_height="@dimen/width_24"
            android:layout_marginStart="@dimen/width_3"
            android:layout_marginTop="@dimen/width_3"
            android:background="@null"
            android:scaleType="fitCenter"
            android:visibility="gone" />

    </RelativeLayout>

    <com.wifiaudio.view.custom_view.MarqueeTextView
        android:id="@+id/vpreset_title"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="@dimen/width_104"
        android:layout_height="@dimen/width_30"
        android:layout_gravity="center_vertical"
        android:focusable="true"
        android:gravity="center_vertical"
        android:padding="@dimen/width_3"
        android:singleLine="true"
        android:text="fafadfadfssfs"
        android:textColor="@color/fg_primary"
        android:textSize="@dimen/font_12" />

</LinearLayout>