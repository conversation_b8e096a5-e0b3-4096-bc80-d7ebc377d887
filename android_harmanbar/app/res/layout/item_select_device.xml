<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="listener"
            type="com.harman.multichannel.ISelectDeviceClickListener" />

        <variable
            name="viewModel"
            type="com.harman.multichannel.MultichannelViewModel" />

        <variable
            name="model"
            type="com.harman.multichannel.SelectDeviceUIModel" />

        <variable
            name="dialog"
            type="com.harman.multichannel.SelectDeviceDialog" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{() -> dialog.onDeviceItemClick( model)}"
        android:paddingHorizontal="24dp"
        android:paddingVertical="16dp"
        app:isEntryPoint="@{model.device == viewModel.entryPoint}">

        <ImageView
            android:id="@+id/dev_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:loadImage="@{model.imageUrl}" />

        <TextView
            android:id="@+id/dev_name"
            style="@style/Text_Body_Regular"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:maxLines="3"
            android:text="@{model.deviceName}"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/dev_icon"
            app:layout_constraintEnd_toStartOf="@+id/dev_select"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/dev_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/svg_icon_unselect_ring"

            app:currentDevice="@{model.device}"
            app:entryPoint="@{viewModel.entryPoint}"
            app:layout_constraintBottom_toBottomOf="parent"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:selectedDevices="@{dialog.selectedDevices}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>