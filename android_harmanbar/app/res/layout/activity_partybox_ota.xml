<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.harman.ota.partybox.ViewStyle" />

        <variable
            name="viewModel"
            type="com.harman.ota.partybox.PartyBoxOtaViewModel" />

        <variable
            name="activity"
            type="com.harman.ota.partybox.PartyBoxOtaActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onBackPressed()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/select_icon_arrow_l"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.Ready ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/software_update"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_device"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_70dp"
            app:layout_constraintTop_toBottomOf="@id/layout_title_bar">



            <include
                android:id="@+id/layout_stereo_device"
                layout="@layout/layout_stereo_device"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"


                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_min="@dimen/dimen_180dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_min="@dimen/dimen_255dp"
                app:maskVisible="@{activity.viewStyle}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <ImageView
                android:id="@+id/iv_alert"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.5"
                android:src="@drawable/ic_round_warn_fg_primary"
                app:alertVisible="@{activity.viewStyle}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/layout_view_style"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_device">

            <include
                android:id="@+id/layout_partybox_ota_ready"
                layout="@layout/layout_partybox_ota_ready"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.Ready ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <include
                layout="@layout/layout_partybox_ota_trans"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.Trans ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <include
                layout="@layout/layout_partybox_ota_install"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.Install ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <include
                layout="@layout/layout_partybox_ota_success"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.Success ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <include
                layout="@layout/layout_partybox_ota_trans_err"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.TransError ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <include
                layout="@layout/layout_partybox_ota_update_fail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.UpdateFail ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

            <include
                layout="@layout/layout_partybox_ota_reconnect_err"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{activity.viewStyle instanceof ViewStyle.ReconnectError ? View.VISIBLE : View.GONE}"
                app:activity="@{activity}"
                app:viewModel="@{viewModel}" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>