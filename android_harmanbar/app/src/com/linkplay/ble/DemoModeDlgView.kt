package com.linkplay.ble

import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.harman.bar.app.R
import com.wifiaudio.utils.ViewUtil

open class DemoModeDlgView(activity: FragmentActivity) : BLESetupBaseView(activity) {

    init {
        init(R.layout.layout_demo_mode_dlg_view)
    }

    override fun updateLayoutUI(rootView: View?) {
        super.updateLayoutUI(rootView)

        var ivClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        ivClose?.apply {
//            var iconDraw = SkinResourcesUtils.getMipMapDrawable("close")
//            setImageDrawable(iconDraw)

            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                dismissDialog()

                listener?.onCancel()
            }
        }

        var tvTitle: TextView? = rootView?.findViewById(R.id.ble_title)
        tvTitle?.apply {
            text = "Please put the DUT into demo mode first."
            text = "Please have the DUT exit demo mode first."
            setPadding(0, 0, 0, 0)
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

        }



        var btnNext: Button? = rootView?.findViewById(R.id.btn_ok)
        btnNext?.apply {

            text = SkinResourcesUtils.getString("harmanbar_devicelist_Confirm")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                dismissDialog()

                listener?.onConfirm(null)
            }
        }


    }

    override fun updateDlgContainerBG(rootView: View?) {
//        super.updateDlgContainerBG(rootView)
    }
}