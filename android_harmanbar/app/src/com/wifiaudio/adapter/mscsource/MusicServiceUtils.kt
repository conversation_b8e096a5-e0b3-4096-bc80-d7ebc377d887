package com.wifiaudio.adapter.mscsource

import com.harman.bar.app.R
import com.harman.bean.MusicServiceItem
import com.skin.SkinResourcesUtils
import com.wifiaudio.model.local_music.MainItem
import com.wifiaudio.view.alarm.bean.ItemAlarmSource

object MusicServiceUtils {
    @JvmStatic
    fun buildServiceItemFromMainItem(item: MainItem): MusicServiceItem {
        val serviceItem = MusicServiceItem()
        serviceItem.bEnable = item.bEnable
        serviceItem.bOpen = item.bOpen
        serviceItem.bVisible = item.bVisible
        serviceItem.Key = item.Key
        serviceItem.isGroupOptionType = item.isGroupOptionType
        serviceItem.Name = item.Name

        when {
            item.Key == 18 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_tidal
                serviceItem.subIconID = R.drawable.svg_dolby_atmos_image
                serviceItem.desc = SkinResourcesUtils.getString("tidal_music_service_desc")
            }

            item.Key == 19 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_amazon
                serviceItem.desc = SkinResourcesUtils.getString("amazon_music_service_desc")
                serviceItem.subIconID = R.drawable.svg_dolby_atmos_image
            }

            item.Key == 9 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_tunein
                serviceItem.desc = SkinResourcesUtils.getString("Listen_to_free_internet_radio__news__sports_music__audiobooks_and_podcasts")
            }

            item.Key == 35 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_calm_radio
                serviceItem.desc = SkinResourcesUtils.getString("calm_radio_music_service_desc")
            }

            item.Key == 23 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_napster
                serviceItem.desc = SkinResourcesUtils.getString("napster_music_service_desc")
            }

            item.Key == 27 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_qobuz
                serviceItem.desc = SkinResourcesUtils.getString("qobuz_music_service_desc")
            }

            item.Key == 17 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_iheart_radio
                serviceItem.desc = SkinResourcesUtils.getString("iheart_radio_music_service_desc")
            }

            item.Name == "vTuner" -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_vtuner
                serviceItem.desc = SkinResourcesUtils.getString("vtuner_desc")
            }

            item.Key == 34 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_sound_machine
                serviceItem.desc = SkinResourcesUtils.getString("sound_machine_music_service_desc")
            }
        }

        return serviceItem
    }

    @JvmStatic
    fun buildServiceItemFromAlarmItem(item: ItemAlarmSource): MusicServiceItem {
        val serviceItem = MusicServiceItem()
        //serviceItem.bEnable = item.bEnable;
        serviceItem.bOpen = item.enable_type == 1
        //serviceItem.bVisible = item.bVisible;
        serviceItem.Key = item.key
        //serviceItem.isGroupOptionType = item.isGroupOptionType;
        serviceItem.Name = item.name

        when {
            item.key == 18 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_tidal
                serviceItem.subIconID = R.drawable.svg_dolby_atmos_image
                serviceItem.desc = SkinResourcesUtils.getString("tidal_music_service_desc")
            }

            item.key == 19 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_amazon
                serviceItem.desc = SkinResourcesUtils.getString("amazon_music_service_desc")
                serviceItem.subIconID = R.drawable.svg_dolby_atmos_image
            }

            item.key == 9 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_tunein
                serviceItem.desc = SkinResourcesUtils.getString("Listen_to_free_internet_radio__news__sports_music__audiobooks_and_podcasts")
            }

            item.key == 35 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_calm_radio
                serviceItem.desc = SkinResourcesUtils.getString("calm_radio_music_service_desc")
            }

            item.key == 23 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_napster
                serviceItem.desc = SkinResourcesUtils.getString("napster_music_service_desc")
            }

            item.key == 27 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_qobuz
                serviceItem.desc = SkinResourcesUtils.getString("qobuz_music_service_desc")
            }

            item.key == 17 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_iheart_radio
                serviceItem.desc = SkinResourcesUtils.getString("iheart_radio_music_service_desc")
            }

            item.name == "vTuner" -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_vtuner
                serviceItem.desc = SkinResourcesUtils.getString("vtuner_desc")
            }

            item.key == 34 -> {
                serviceItem.icon_ID = R.drawable.icon_music_service_sound_machine
                serviceItem.desc = SkinResourcesUtils.getString("sound_machine_music_service_desc")
            }
        }

        return serviceItem
    }
}