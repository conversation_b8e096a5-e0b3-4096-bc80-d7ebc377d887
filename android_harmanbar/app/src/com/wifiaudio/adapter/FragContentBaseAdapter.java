package com.wifiaudio.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.harman.bar.app.R;
import com.wifiaudio.app.WAApplication;
import com.wifiaudio.model.AlbumInfo;
import com.wifiaudio.model.DeviceInfoExt;
import com.wifiaudio.utils.FontManager;

import org.teleal.cling.model.ModelUtil;

import java.util.ArrayList;
import java.util.List;

public class FragContentBaseAdapter extends BaseAdapter {

	private Context context;
	private List<AlbumInfo> currList = new ArrayList<AlbumInfo>();

	public FragContentBaseAdapter(Context context) {
		super();
		this.context = context;
	}

	public List<AlbumInfo> getCurrList() {
		return currList;
	}

	public void setCurrList(List<AlbumInfo> currList) {
		this.currList = currList;
	}

	@Override
	public int getCount() {
		// TODO Auto-generated method stub
		return currList == null ? 0 : currList.size();
	}

	@Override
	public Object getItem(int position) {
		// TODO Auto-generated method stub
		return position;
	}

	@Override
	public long getItemId(int position) {
		// TODO Auto-generated method stub
		return position;
	}

	@Override
	public View getView(final int position, View view, ViewGroup parent) {
		// TODO Auto-generated method stub
		HolderView hv = null;
		if (view == null) {
			hv = new HolderView();
			view = LayoutInflater.from(context).inflate(
					R.layout.item_msc_search_ttpod, null);

			hv.vimgAlbum = (ImageView) view.findViewById(R.id.vsong_img);
			hv.vduration = (TextView) view.findViewById(R.id.vsong_duration);
			hv.vmore = (ImageView) view.findViewById(R.id.vmore);
			hv.vsingername = (TextView) view
					.findViewById(R.id.vsong_singername);
			hv.vsongname = (TextView) view.findViewById(R.id.vsong_name);
			hv.vcontainer = view;

			view.setTag(hv);
			FontManager.changeFonts((ViewGroup) view);
		} else {
			hv = (HolderView) view.getTag();
		}

		AlbumInfo item = this.currList.get(position);

		hv.vimgAlbum.setVisibility(View.GONE);
		hv.vsongname.setText(item.title);
		hv.vsingername.setText(item.artist + "-" + item.album);

		if (hv.vsongname != null && item.bitrate >= 128) {
			Drawable drawHQ = context.getResources().getDrawable(
					com.wifiaudio.R.drawable.icon_music_hq);
			drawHQ.setBounds(0, 0, drawHQ.getMinimumWidth(),
					drawHQ.getMinimumHeight());
			hv.vsongname.setCompoundDrawables(null, null, drawHQ, null);
		} else {
			hv.vsongname.setCompoundDrawables(null, null, null, null);
		}

		if (hv.vduration != null) {
			hv.vduration.setText(ModelUtil.toTimeString(item.duration / 1000));
		}

		hv.vmore.setEnabled(true);
		hv.vmore.setImageResource(R.drawable.select_icon_search_more);

		hv.vmore.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View arg0) {
				// TODO Auto-generated method stub

				if (onAlbumItemClickListener != null) {
					onAlbumItemClickListener.onItemClick(position,
							getCurrList());
				}
			}
		});

		if (WAApplication.me.CurrentDevice != null) {
			DeviceInfoExt deviceInfoExt = WAApplication.me.CurrentDevice.devInfoExt;

			int iColor = WAApplication.me.getResources().getColor(R.color.song_title_fg);

			if (deviceInfoExt.albumInfo.title.equals(item.title)
					&& deviceInfoExt.albumInfo.album.equals(item.album)
					&& deviceInfoExt.albumInfo.artist.equals(item.artist)) {
				hv.vsongname.setTextColor(iColor);
			} else {

				hv.vsongname.setTextColor(context.getResources().getColor(R.color.fg_primary));
			}
		}

		hv.vcontainer.setOnClickListener(new OnClickListener() {

			@Override
			public void onClick(View arg0) {
				// TODO Auto-generated method stub
				if (onAdapterItemClickListener != null) {
					onAdapterItemClickListener.onAdapterItemClick(position,
							getCurrList());
				}
			}
		});

		return view;
	}

	static class HolderView {

		View vcontainer;
		ImageView vimgAlbum;
		ImageView vmore;
		TextView vsongname;
		TextView vsingername;
		TextView vduration;
	}

	OnAlbumItemClickListener onAlbumItemClickListener;

	public void setOnAlbumItemClickListener(
			OnAlbumItemClickListener onAlbumItemClickListener) {
		this.onAlbumItemClickListener = onAlbumItemClickListener;
	}

	public interface OnAlbumItemClickListener {
		void onItemClick(int pos, List<AlbumInfo> albumInfos);
	}

	OnAdapterItemClickListener onAdapterItemClickListener;

	public void setOnAdapterItemClickListener(
			OnAdapterItemClickListener onAdapterItemClickListener) {
		this.onAdapterItemClickListener = onAdapterItemClickListener;
	}

	public interface OnAdapterItemClickListener {
		void onAdapterItemClick(int pos, List<AlbumInfo> albumInfos);
	}

}
