package com.wifiaudio.view.pagesmsccontent.amazon;

import com.wifiaudio.model.DeviceItem;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/12/23.
 */

public class DataInfo implements Serializable{
    private static final long serialVersionUID = -8718205180364364947L;
    public static final int TYPE_SOURCE = 0;
    public static final int TYPE_OOBE = 1;
    public static final int TYPE_SETTING = 2;

    public int frameId = 0;//源视图
    public DeviceItem deviceItem; //当前操作的设备项
    public int type = 0;//入口 0:左侧音源, 1:代表OOBE 2:设置
}
