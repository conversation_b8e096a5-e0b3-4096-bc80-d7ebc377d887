package com.wifiaudio.view.pagesmsccontent.menu_settings.lwa;

import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.harman.BaseViewModel;
import com.harman.WirelessConnector;
import com.harman.bean.AuthDevice;
import com.harman.bean.VoiceLanguage;
import com.harmanbar.ble.utils.GsonUtil;
import com.wifiaudio.action.DeviceSettingActionCallback;
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.AlexaVoiceAssistantFragment;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.Objects;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import rxhttp.wrapper.param.RxHttp;

public class LoginWithAmazonViewModel extends BaseViewModel {
     VoiceLanguage voiceLanguage;

    public void requestLanguageFromAmazon(String deviceSerialNumber, String productId) {
        String url = "https://alexa-voice-service-setup.amazon.com/device-locale?deviceSerialNumber=%s&productId=%s";
        Observable<String> requestObservable = RxHttp.get(url, deviceSerialNumber, productId).asString();
        requestObservable.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).
                subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        add(d);
                    }

                    @Override
                    public void onNext(String s) {
                        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " requestLanguageFromAmazon:onNext" + s);
                        try {
                            JSONObject jsonObject = new JSONObject(s);
                            JSONArray array = jsonObject.optJSONArray("locales");
                            StringBuffer buffer = new StringBuffer();
                            for (int i = 0; i < array.length(); i++) {
                                buffer.append((String) array.get(i));
                                if (i != array.length() - 1) buffer.append(",");
                            }
                            if (buffer.length() > 0) {
                                voiceLanguage = new VoiceLanguage(buffer.toString());
                                Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " requestLanguageFromAmazon:voiceLanguage:" + voiceLanguage.getLanguage() + voiceLanguage.getCountries());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " requestLanguageFromAmazon:onError" + e.toString());
                    }

                    @Override
                    public void onComplete() {
                        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " requestLanguageFromAmazon:onComplete");
                    }
                });
    }

    public VoiceLanguage getVoiceLanguage() {
        return voiceLanguage;
    }

    public boolean isGetVoiceLanguageSuccess(){
        return voiceLanguage != null && !voiceLanguage.isEmpty();
    }
    /**
     *  use after requestLanguageFromAmazon
     * @param authDevice
     */
    public void setDeviceLanguage(AuthDevice authDevice) {
        if (voiceLanguage == null || voiceLanguage.isEmpty()) {
            Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " setDeviceLanguage:onError:" + voiceLanguage);
            return;
        }
        createSetVoiceLanguageObservable(voiceLanguage, authDevice).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).
                subscribe(new Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        add(d);
                    }

                    @Override
                    public void onNext(Boolean aBoolean) {
                        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " setDeviceLanguage:onNext" + aBoolean);
                    }

                    @Override
                    public void onError(Throwable e) {
                        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " setDeviceLanguage:onError" + e.toString());
                    }

                    @Override
                    public void onComplete() {
                        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " setDeviceLanguage:onComplete" );
                    }
                });
    }


    private Observable<Boolean> createSetVoiceLanguageObservable(VoiceLanguage voiceLanguage, AuthDevice authDevice) {
        Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " createSetVoiceLanguageObservable:voiceLanguage:" + voiceLanguage.toString());
        return Observable.create(emitter -> WirelessConnector.setVoiceLanguage(authDevice, voiceLanguage, new DeviceSettingActionCallback() {
            @Override
            public void onSuccess(String content) {
                Log.d(AlexaVoiceAssistantFragment.class.getSimpleName(), " createSetVoiceLanguageObservable:content:" + content);
                if (TextUtils.isEmpty(content)) emitter.onError(new Exception("content is empty"));

                try {
                    JSONObject jsonObject = new JSONObject(content);
                    boolean success = TextUtils.equals("0", jsonObject.optString("error_code"));
                    emitter.onNext(success);
                    emitter.onComplete();
                } catch (Exception e) {
                    e.printStackTrace();
                    emitter.onError(e);
                }
            }

            @Override
            public void onFailure(Throwable e) {
                emitter.onError(e);
            }
        }));
    }
}
