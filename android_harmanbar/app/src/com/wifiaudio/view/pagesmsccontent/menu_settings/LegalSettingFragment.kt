package com.wifiaudio.view.pagesmsccontent.menu_settings

import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.harman.bar.app.R
import com.harman.bar.app.databinding.FragmentLegalSettingBinding
import com.harman.isHKOneApp
import com.harman.legallib.LegalConfig
import com.harman.legallib.LegalManager
import com.harman.log.Logger
import com.linkplay.privacy.FragPrivacyDetailsTxT
import com.linkplay.privacy.PrivacyWebActivity
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.utils.AssetsProjectConfigParse
import com.wifiaudio.utils.NavigationBarColorHelper
import com.wifiaudio.view.pagesmsccontent.ContainerActivity
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import java.util.Locale

class LegalSettingFragment : FragTabBackBase() {

    var mAdapter: MenuContentLegalAdapter? = null

    var dataInfo: DataFragInfo? = null
    var currList: ArrayList<ContentSettingItem> = ArrayList()

    private lateinit var binding: FragmentLegalSettingBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = viewBinding(inflater = inflater, container = container)
        Logger.i(TAG, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        activity?.let {
            NavigationBarColorHelper.setNavigationBarColor(it)
        }
    }

    private fun viewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): FragmentLegalSettingBinding =
        FragmentLegalSettingBinding.inflate(inflater, container, false).also { binding ->
            binding.fragment = this@LegalSettingFragment
            binding.lifecycleOwner = this@LegalSettingFragment
        }

    override fun initView() {
        super.initView()

        initDatas()
        mAdapter = MenuContentLegalAdapter(activity)
        mAdapter?.setCurrList(currList)

        binding.recycleView.apply {
            adapter = mAdapter
        }

        initRecordNumber()
    }

    private fun initRecordNumber() {
        val selectLan: String = LocaleLanConfigUtil.getDefaultAppLan()
        Logger.d(TAG, "selectLan: $selectLan")
        if (!selectLan.equals("ZH",ignoreCase = true)) {
            binding.layoutRecordNumber.isVisible = false
            return
        }

        binding.layoutRecordNumber.isVisible = true
        binding.tvRecordNumber.text = if (isHKOneApp()) {
            ICPManager.HK_ONE_ICP_NUMBER
        } else {
            ICPManager.JBL_ONE_ICP_NUMBER
        }

        binding.layoutRecordNumber.setOnClickListener {
            try {
                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(ICPManager.ICP_URL))
                startActivity(browserIntent)
            } catch (_: ActivityNotFoundException) {
            }
        }
    }

    private fun initDatas() {

        if (currList == null)
            currList = ArrayList()
        else
            currList.clear()

        var item = ContentSettingItem()
        item.title = getString(R.string.harmanbar_jbl_End_User_License_Agreement)
        item.sub_desc = ""
        item.bvisibleMore = true
        item.icon_more_name = "ic_arrow_forward_fg_primary"
        item.item_type = SETTING_ITEM_TYPE.OPTION_1
        currList.add(item)

        item = ContentSettingItem()
        item.title = getString(R.string.harmanbar_jbl_Harman_Privacy_Statement)
        item.sub_desc = ""
        item.bvisibleMore = true
        item.icon_more_name = "ic_arrow_forward_fg_primary"
        item.item_type = SETTING_ITEM_TYPE.OPTION_2
        currList.add(item)

        item = ContentSettingItem()
        item.title = getString(R.string.harmanbar_jbl_Open_Source_Licenses)
        item.sub_desc = ""
        item.bvisibleMore = true
        item.icon_more_name = "ic_arrow_forward_fg_primary"
        item.item_type = SETTING_ITEM_TYPE.OPTION_3
        currList.add(item)
    }

    override fun bindSlots() {
        super.bindSlots()


        mAdapter?.setOnItemClickListener { pos, item ->

            when (item.item_type){
                SETTING_ITEM_TYPE.OPTION_1 -> {

                    val fragment = FragPrivacyDetailsTxT()
                    fragment.titleMutableData.value = item.title
                    fragment.strInfo2Name = LegalConfig.Type.EULA.name
                    dataInfo?.frameId?.let { FragTabUtils.replaceFrag(activity, it, fragment, true) }
                }

                SETTING_ITEM_TYPE.OPTION_2 ->{

                    val selectLan = LocaleLanConfigUtil.getDefaultAppLan()
                    val locale = Locale(selectLan, "", "")
                    val url = activity?.let { it1 -> LegalManager.getLegalFileContent(it1, LegalConfig.Type.PRIVACY, locale) }
                    if (TextUtils.isEmpty(url)) return@setOnItemClickListener
                    val intent = Intent(context, PrivacyWebActivity::class.java)
                    Logger.d(TAG, "Legal url: $url")
                    intent.putExtra("url", url)
                    startActivity(intent)
                }
                SETTING_ITEM_TYPE.OPTION_3 ->{
                    var fragment = FragPrivacyDetailsTxT()
                    var fileName = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_open_source_licenses_txt")

                    if(TextUtils.isEmpty(fileName))
                        return@setOnItemClickListener

                    fragment.titleMutableData.value = item.title
                    fragment.strInfo2Name = fileName
                    dataInfo?.frameId?.let { FragTabUtils.replaceFrag(activity, it, fragment, true) }
                }
                else -> {}
            }
        }
    }

    override fun initUtils() {
        super.initUtils()

    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()

        if (activity is ContainerActivity)
            activity?.finish()
        else
            FragTabUtils.popBack(activity)
    }

    private val TAG = "LegalSettingFragment"
}