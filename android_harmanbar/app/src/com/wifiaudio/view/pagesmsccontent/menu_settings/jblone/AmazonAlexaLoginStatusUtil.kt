package com.wifiaudio.view.pagesmsccontent.menu_settings.jblone

import com.wifiaudio.utils.devmanager.LPDeviceManagerUtil
import com.wifiaudio.view.pagesmsccontent.menu_settings.ContentSettingItem

object AmazonAlexaLoginStatusUtil {

    private var loginDeviceList: java.util.ArrayList<ContentSettingItem> =
        java.util.ArrayList()//登录的列表
    private var nologinDeviceList: java.util.ArrayList<ContentSettingItem> =
        java.util.ArrayList()//未登录的列表

    fun clearAll() {

        loginDeviceList?.clear()
        nologinDeviceList?.clear()
    }

    public fun isEmptyLoginList(): Boolean{
        return loginDeviceList == null || loginDeviceList.size == 0
    }

    public fun isEmptyNoLoginList(): Boolean{
        return nologinDeviceList == null || nologinDeviceList.size == 0
    }

    public fun getLoginList(): ArrayList<ContentSettingItem>{
        return loginDeviceList
    }

    public fun getNoLoginList(): ArrayList<ContentSettingItem>{
        return nologinDeviceList
    }

    fun addLoginItem(item: ContentSettingItem?) {

        if (item != null) {
            loginDeviceList?.add(item)
        }
    }

    fun getLoginItem(uuid: String): ContentSettingItem? {
        return loginDeviceList.find {
            com.wifiaudio.utils.devmanager.LPDeviceManagerUtil.getInstance().isSameDevice(it.uuid, uuid)
        }
    }

    fun getNoLoginItem(uuid: String): ContentSettingItem? {

        return nologinDeviceList.find {
            com.wifiaudio.utils.devmanager.LPDeviceManagerUtil.getInstance().isSameDevice(it.uuid, uuid)
        }
    }

    fun removeLoginItem(item: ContentSettingItem?){

        if (item != null) {
            loginDeviceList?.remove(item)
        }
    }

    fun addNoLoginItem(item: ContentSettingItem?){

        if (item != null) {
            nologinDeviceList?.add(item)
        }
    }

    fun removeNoLoginItem(item: ContentSettingItem?){

        if (item != null) {
            nologinDeviceList?.remove(item)
        }
    }
}