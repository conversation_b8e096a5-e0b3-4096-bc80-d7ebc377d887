package com.wifiaudio.view.pagesmsccontent;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.skin.SkinResourcesUtils;
import com.harman.bar.app.R;
import com.wifiaudio.action.PrivacyPolicyStatus;
import com.wifiaudio.action.log.print_log.LogsUtil;

import config.LogTags;


/**
 * Created by Administrator on 2016/8/8.
 */
public class PrivacyPolicyActivity extends Activity {

    private TextView vAccept;
    private TextView vPrivacyPolicy;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_privacy_policy);
        LogsUtil.i(LogTags.UI, "page=" + getClass().getSimpleName() + ":onCreate");
        vAccept = (TextView) findViewById(R.id.accept);
        vPrivacyPolicy = (TextView) findViewById(R.id.privacy_policy);


        vAccept.setText(SkinResourcesUtils.getString("Accept"));
        vPrivacyPolicy.setText(SkinResourcesUtils.getString("PRIVACY POLICY:\n\n\n" +
                "                                    When using the app your personal data will be collected and used by Technaxx Germany GmbH &amp; Co. KG. Type and scope of data collection can be found in the declaration, which can be accessed at any time in the app.\n\n"));

        vAccept.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                PrivacyPolicyStatus.savePrivacyPolicyStatus(true);
                finish();
            }
        });
    }


    @Override
    public void onBackPressed() {

    }
}
