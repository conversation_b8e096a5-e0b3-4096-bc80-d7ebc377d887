package com.wifiaudio.view.pagesmsccontent;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.skin.SkinResourcesUtils;
import com.harman.bar.app.R;
import com.wifiaudio.app.IInitView;

/**
 * Created by Administrator on 2016/8/8.
 */
public class ImpressumAndDeclarationInfo extends Activity implements IInitView {
    public static final int IMPRESSUM = 0X01;
    public static final int DECLARATION = 0X02;
    private int type = -1;

    private Button vback;
    private TextView vtitle, title, detail;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Intent intent = getIntent();
        type = intent.getIntExtra("type", IMPRESSUM);
        if (type == IMPRESSUM) {
            setContentView(R.layout.layout_impressum);
        } else if (type == DECLARATION) {
            setContentView(R.layout.layout_declaration);
        }

        initView();
        bindSlots();
        initUtils();
    }

    @Override
    public void initView() {
        vback = (Button) findViewById(R.id.vback);
        vtitle = (TextView) findViewById(R.id.vtitle);
        title = (TextView) findViewById(R.id.title);
        detail = (TextView) findViewById(R.id.detail);

        if (type == IMPRESSUM) {
            vtitle.setText(SkinResourcesUtils.getString("setting_IMPRESSUM_001"));
            title.setText(SkinResourcesUtils.getString("setting_IMPRESSUM_001"));
            detail.setText(SkinResourcesUtils.getString("setting_IMPRESSUM_002"));
        } else if (type == DECLARATION) {
            vtitle.setText(SkinResourcesUtils.getString("setting_DECLARATION_001"));
            detail.setText(SkinResourcesUtils.getString("setting_DECLARATION_002"));

        }
    }

    @Override
    public void bindSlots() {
        vback.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    public void initUtils() {

    }
}
