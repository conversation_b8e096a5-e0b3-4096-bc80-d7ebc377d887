package com.wifiaudio.view.pagesmsccontent.easylink.ble_link_3.model

import com.wifiaudio.view.pagesmsccontent.easylink.ble_link_3.BLE3BluetoothItem

class SetupSpeakerItem(
    title: String?,
    srcName: String,
    isSelected: Boolean = false
) {
    var title: String? = ""
    var srcName: String? = ""
    var isSelected: Boolean = false
    var pid: Int = 0
    var BTDevice: BLE3BluetoothItem? = null
    var bFromOffline:Boolean = false //是否从离线流程过来

    init {
        this.title = title
        this.srcName = srcName
        this.isSelected = isSelected
        this.pid = pid
        this.bFromOffline = bFromOffline
    }

    override fun toString(): String {
        return "SetupSpeakerItem(title=$title, srcName=$srcName, isSelected=$isSelected, pid=$pid, BTDevice=$BTDevice, bFromOffline=$bFromOffline)"
    }

}
