package com.wifiaudio.view.pagesdevcenter.devicesetting.calibration

import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Html
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.skin.SkinResourcesID
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.harman.bar.app.R
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.ViewUtil
import config.GlobalUIConfig
import config.LogTags

open class ChromecastLaterSetupView(activity: FragmentActivity) {

    var activity: FragmentActivity? = null
    var listener: IOnClickEventListener? = null

    init {
        this.activity = activity
    }

    fun createView(): View?{
        val itemView = LayoutInflater.from(activity?.applicationContext).inflate(R.layout.layout_oobe_chromecast_builtin, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        return itemView
    }

    fun getTitle(): String?{
        return SkinResourcesUtils.getString( "Chromecast_built_in_for_music_streaming_and_multi_room_audio" )
        //return SkinResourcesUtils.getString("jbl_Chromecast_Built_In_Available")
    }

    fun updateLayoutUI(rootView: View?) {
        var containerView = rootView?.findViewById<View>(R.id.container)
        containerView?.background = null
        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            visibility=View.GONE
            setOnClickListener {
                listener?.onConfirm(null)
            }
        }
        rootView?.findViewById<View>(R.id.iv_title_image)?.visibility = View.VISIBLE

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {
            visibility = View.VISIBLE
            text = SkinResourcesUtils.getString("To_enable_Chromecast_for_music_streaming_and_multi_room_audio_later_select_Product_Settings_from_product_card_to_continue")
            movementMethod = LinkMovementMethod.getInstance()
        }

        var ivLogo: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivLogo?.apply {
            visibility = View.GONE
//            setImageDrawable(SkinResourcesUtils.getDrawable("icon_chromecast_setup_ready"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {

            text = SkinResourcesUtils.getString("jbl_GOT_IT")

            /*var tintDrawable: Drawable? =
                    SkinResourcesUtils.getTintDrawable("btn_background_bg_s1", GlobalUIConfig.color_btn_normal, "btn_background_bg_s1", GlobalUIConfig.color_btn_press)
            background = tintDrawable*/

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            setOnClickListener {
                listener?.onConfirm(null)
            }
        }
        var btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)
        btnCancel?.apply {
            visibility = View.INVISIBLE
        }
    }

    interface IOnClickEventListener{
        fun onConfirm(any: Any?)
        fun onCancel()
    }

    companion object {
        val TAG: String = ChromecastLaterSetupView::class.java.simpleName
    }
}