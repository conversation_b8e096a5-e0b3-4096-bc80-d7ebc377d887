package com.wifiaudio.view.pagesdevcenter.devicesetting.calibration

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.harman.bar.app.R
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.utils.AssetsProjectConfigParse
import com.wifiaudio.utils.GsonParseUtil
import com.wifiaudio.utils.StringSpannableUtils
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLC4aPermissionStatus
import config.LogTags


open class ChromecastBuiltInAvailableView(activity: FragmentActivity) {

    var activity: FragmentActivity? = null
    var listener: IOnClickEventListener? = null
    var dataInfo: DataFragInfo? = null

    init {
        this.activity = activity
    }

    fun createView(): View? {
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        return LayoutInflater.from(activity?.applicationContext).inflate(R.layout.layout_chromecast_built_in_available_enable_service, null)
        /*val root: RelativeLayout? = activity?.findViewById(R.id.content_layout)
        return LayoutInflater.from(activity?.applicationContext).inflate(R.layout.layout_chromecast_built_in_available_enable_service, root,false)*/
    }

    fun getTitle(): String? {
        return SkinResourcesUtils.getString("jbl_Chromecast_Built_In_Available")
    }

    fun updateLayoutUI(rootView: View?) {

        val tvTitle: TextView? = rootView?.findViewById(R.id.ble_title)
        tvTitle?.apply {
            text = getTitle()
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        val tvInfo1: TextView? = rootView?.findViewById(R.id.tv_label_1)
        tvInfo1?.apply {
            text = SkinResourcesUtils.getString("jbl_stream_music_from_hundreds_of_apps_to_your_products_via_chromecast_built_in")
            alpha = 0.85f
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        val tvInfo2: TextView? = rootView?.findViewById(R.id.tv_label_2)
        tvInfo2?.apply {
            val strHighLabel1 = SkinResourcesUtils.getString("jbl___Google_Terms_Of_Service__")
            val strHighLabel2 = SkinResourcesUtils.getString("jbl___Privacy_Policy__")
            val strInfoLabel = SkinResourcesUtils.getString("jbl_Agree_with___Google_Terms_Of_Service___and___Privacy_Policy___to_start_using_Chromecast_built_in_")
            val stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setUnderlineText(true)
            stringUtil.setTextHighlight(
                arrayOf(strHighLabel1, strHighLabel2),
                WAApplication.me.getColor(R.color.fg_activate)
            )
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            stringUtil.setSpannableStrings {

                if (it == 0) {
                    val html = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_terms_url")
                    if (TextUtils.isEmpty(html)) return@setSpannableStrings
                    val intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    val url = Uri.parse(html)
                    intent.data = url
                    activity?.startActivity(intent)

                } else if (it == 1) {
                    val html = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_privacy_url")
                    if (TextUtils.isEmpty(html)) return@setSpannableStrings
                    val intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    val url = Uri.parse(html)
                    intent.data = url
                    activity?.startActivity(intent)

                }
            }

            text = stringUtil.spannableString
            highlightColor = Color.TRANSPARENT
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            alpha = 0.85f
            movementMethod = LinkMovementMethod.getInstance()
        }

        val tvInfo3: TextView? = rootView?.findViewById(R.id.tv_label_3)
        tvInfo3?.apply {
            visibility = View.VISIBLE
            val strHighLabel = SkinResourcesUtils.getString("text_here")
            val strInfoLabel = SkinResourcesUtils.getString("tap_here_to_enable_later")
            val linkColor = WAApplication.me.getColor(R.color.fg_activate)

            val stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setTextHighlight(strHighLabel, linkColor)
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))
            stringUtil.setSpannableString {
                listener?.onCancel()
            }
            text = stringUtil.spannableString
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            alpha = 0.8f
            movementMethod = LinkMovementMethod.getInstance()
        }

        val ivLogo: ImageView? = rootView?.findViewById(R.id.iv_logo)
        ivLogo?.apply {
            setImageDrawable(SkinResourcesUtils.getDrawable("svg_icon_chromecast_builtin"))
        }

        val btnNext: Button? = rootView?.findViewById(R.id.btn_enable)
        btnNext?.apply {
            text = SkinResourcesUtils.getString("jbl_ENABLE_SERVICE")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            /* val tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                 "btn_background_bg_s1",
                 GlobalUIConfig.color_btn_normal,
                 "btn_background_bg_s1",
                 GlobalUIConfig.color_btn_press
             )
             background = tintDrawable*/


            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                tvInfo3?.apply {
                    visibility = View.GONE
                }
                clickApprove()
            }
        }

        val btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)
        btnCancel?.apply {
            visibility = View.GONE
            text = SkinResourcesUtils.getString("jbl_LATER")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            background = null
            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                listener?.onCancel()
            }
        }
    }

    private fun clickApprove() {

        if (dataInfo?.deviceItem == null) {
            listener?.onCancel()
            return
        }

        WAApplication.me.showProgDlg(activity, (15 * 1000).toLong(), SkinResourcesUtils.getString(""))

        DeviceCustomerSettingAction.makeDeviceSetC4aPermissionStatus(dataInfo?.deviceItem?.IP, "1", object : DeviceSettingActionCallback {

            override fun onSuccess(content: String?) {

                WAApplication.me.showProgDlg(activity, false, null)
                LogsUtil.i(LogTags.Device, "$TAG setC4aPermissionStatus:Success: $content")
                val status = content?.let {
                    GsonParseUtil.instance.fromJson(it, JBLC4aPermissionStatus::class.java)
                }

                if (TextUtils.equals(status?.error_code, "0")) {
                    listener?.onConfirm(null)
                } else {
                    WAApplication.me.toastMessage(activity, true, SkinResourcesUtils.getString("jbl_Fail"))
                }
            }

            override fun onFailure(e: Throwable?) {
                WAApplication.me.showProgDlg(activity, false, null)
                WAApplication.me.toastMessage(activity, true, SkinResourcesUtils.getString("jbl_Fail"))
                LogsUtil.i(LogTags.Device, "$TAG  setC4aPermissionStatus:Failed: ${e?.localizedMessage}")
            }
        })
    }

    interface IOnClickEventListener {
        fun onConfirm(any: Any?)
        fun onCancel()
    }

    companion object {
        val TAG: String = ChromecastBuiltInAvailableView::class.java.simpleName
    }
}