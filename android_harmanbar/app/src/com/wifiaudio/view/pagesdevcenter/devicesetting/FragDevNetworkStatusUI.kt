package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.harman.bar.app.R
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.utils.WifiResultsUtil
import com.wifiaudio.view.pagesmsccontent.ContainerActivity
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.GlobalUIConfig
import config.LogTags

class FragDevNetworkStatusUI : FragDevSettingBaseUI() {

    var vback: Button? = null
    var vTitle: TextView? = null
    var tv_label_1: TextView? = null
    var recyclerView: RecyclerView? = null
    var currList: ArrayList<DevOptionItem> = ArrayList()
    var mAdapter: DevNetworkStatusUIAdapter? = null

    var dataInfo: DataFragInfo? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        cview = inflater.inflate(R.layout.frag_device_network_status, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun initView() {
        super.initView()

        recyclerView = cview.findViewById(R.id.recycle_view)
        tv_label_1 = cview.findViewById(R.id.tv_label_1)
        vback = cview.findViewById(R.id.vback)
        vTitle = cview.findViewById(R.id.vtitle)

        tv_label_1?.apply {
            text = SkinResourcesUtils.getString("jbl_Network_Control")
            setTextColor(GlobalUIConfig.color_info_normal)
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)

        }

        initStatusList()

        mAdapter = DevNetworkStatusUIAdapter()
        mAdapter?.setCurrList(currList)
        recyclerView?.apply {
            adapter = mAdapter
        }
    }

    private fun initStatusList() {

        if (currList == null)
            currList = java.util.ArrayList()

        currList.clear()

        var item = DevOptionItem()
        item.icon_Name = ""
        item.title = SkinResourcesUtils.getString("jbl_Home_Network")
        item.id = IDS.ID_NONE
        currList.add(item)

        item = DevOptionItem()
        item.hasIcon = false
        item.title = SkinResourcesUtils.getString("jbl_Wi_Fi_Strength")
        item.id = IDS.ID_1
        item.hasMore = true
        item.subTitle = "${WifiResultsUtil.getRSSIDB(dataInfo?.deviceItem?.devStatus?.rssi)}%"
        item.strBGName = JBLDataUtil.TYPE_HEADER
        item.more_Name = "component_svg_icon_arrow_forward_square"
        currList.add(item)

        item = DevOptionItem()
        item.hasIcon = false
        item.title = SkinResourcesUtils.getString("jbl_IP_Address")
        item.id = IDS.ID_2
        item.hasMore = false
        item.subTitle = dataInfo?.deviceItem?.IP
        item.strBGName = JBLDataUtil.TYPE_CENTER
        currList.add(item)

        item = DevOptionItem()
        item.hasIcon = false
        item.title = SkinResourcesUtils.getString("jbl_MAC_Address")
        item.id = IDS.ID_3
        item.hasMore = false
        item.subTitle = dataInfo?.deviceItem?.devStatus?.mac
        item.strBGName = JBLDataUtil.TYPE_BOTTOM
        currList.add(item)

        item = DevOptionItem()
        item.hasIcon = false
        item.title = SkinResourcesUtils.getString("jbl_Change_Network")
        item.id = IDS.ID_4
        item.hasMore = false
        item.subTitle = ""
        item.bClickItem = false
        item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
        currList.add(item)
    }

    override fun bindSlots() {
        super.bindSlots()

        vback?.setOnClickListener {
            onBackKeyDown()
        }

        mAdapter?.setIOnItemClickListener(object : DevNetworkStatusUIAdapter.IOnItemClickListener {
            override fun onItemClick(position: Int, item: DevOptionItem?) {

                if (item?.id == IDS.ID_4) {

                    var vfrag = FragDevNetworkManagementUI()
                    vfrag.dataInfo = dataInfo
                    dataInfo?.let { it1 ->
                        FragTabUtils.addFrag(
                            activity,
                            it1.frameId,
                            vfrag,
                            true
                        )
                    }
                }
            }
        })
    }

    override fun initUtils() {
        super.initUtils()

        updateTheme()
    }

    private fun updateTheme() {
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        if (activity == null)
            return

        if (activity is ContainerActivity) {
            activity?.finish()
        } else {
            FragTabUtils.popBack(activity)
        }
    }
}