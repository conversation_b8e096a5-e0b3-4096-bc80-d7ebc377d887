package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.albuminfo.AlbumMetadataUpdater
import com.wifiaudio.model.albuminfo.MessageAlbumObject
import com.wifiaudio.model.albuminfo.MessageAlbumType
import com.wifiaudio.model.playviewmore.PlayMoreSleepTimerItem
import com.wifiaudio.utils.DeviceProjectParseUtil
import com.wifiaudio.utils.ScreenUtil
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.GlobalUIConfig
import config.LogTags
import java.util.*

class FragDevActionsUI : FragDevSettingBaseUI() {

    var vback: Button? = null
    var vTitle: TextView? = null
    var tv_label_1: TextView? = null
    var tv_label_2: TextView? = null
    var recyclerView: RecyclerView? = null
    var btn_ok: Button? = null
    var dataInfo: DataFragInfo? = null

    var currList: ArrayList<DevOptionItem> = ArrayList()
    var mAdapter: DevActionsUIAdapter? = null
    var smartConfigItem: DevSmartBtnConfigItem? = DevSmartBtnConfigItem()//获取线上的值
    var localSmartConfigItem: DevSmartBtnConfigItem? =
        DevSmartBtnConfigItem()//本地编辑的item，退出页面不保存时，不需要赋值

    private var bOperateItemTimer: Boolean = false//是否操作了timer
    private var bOperateItemVolume: Boolean = false//是否操作了volume
    private var bOperateItemAtmos: Boolean = false//是否操作了Atmos

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        cview = inflater.inflate(R.layout.frag_device_actions, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        AlbumMetadataUpdater.me().addObserver(this)

        return cview
    }

    override fun onDestroy() {
        super.onDestroy()
        AlbumMetadataUpdater.me().deleteObserver(this)
    }

    override fun initView() {
        super.initView()

        recyclerView = cview.findViewById(R.id.recycle_view)
        tv_label_1 = cview.findViewById(R.id.tv_label_1)
        tv_label_2 = cview.findViewById(R.id.tv_label_2)
        vback = cview.findViewById(R.id.vback)
        vTitle = cview.findViewById(R.id.vtitle)
        btn_ok = cview.findViewById(R.id.btn_ok)

        tv_label_1?.apply {
            text = SkinResourcesUtils.getString("jbl_Speaker_actions")
            setTextColor(GlobalUIConfig.color_info_normal)
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)

        }

        tv_label_2?.apply {
            text =
                SkinResourcesUtils.getString("jbl_Below_settings_would_be_triggered_while_you_press_the_Moment_button_on_your_speaker_")
            setTextColor(GlobalUIConfig.color_info_normal)
            alpha = 0.45f
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        }

        initCurrListData()

        mAdapter = DevActionsUIAdapter()
        mAdapter?.setCurrList(currList)
        smartConfigItem?.let { mAdapter?.setSmartConfigItem(it) }
        recyclerView?.apply {
            adapter = mAdapter
        }
    }

    override fun bindSlots() {
        super.bindSlots()

        vback?.setOnClickListener {

            onBackKeyDown()
        }

        btn_ok?.setOnClickListener {
            //目前现退出

            if(bOperateItemVolume) {
                smartConfigItem?.bCustomSmartConfig = true
                smartConfigItem?.smart_config?.volume = localSmartConfigItem?.smart_config?.volume!!
            }

            if(bOperateItemAtmos) {
                smartConfigItem?.bCustomSmartConfig = true
                smartConfigItem?.smart_config?.atmos = localSmartConfigItem?.smart_config?.atmos!!
            }

            if(bOperateItemTimer) {
                smartConfigItem?.bCustomSmartConfig = true
                smartConfigItem?.smart_config?.timer = localSmartConfigItem?.smart_config?.timer!!
            }

            AlbumMetadataUpdater.me().notifyAlarmContextChanged(smartConfigItem)

            FragTabUtils.popBack(activity)
        }

        mAdapter?.setOnItemClickListener(object : DevActionsUIAdapter.IOnItemClickListener {
            override fun onItemClick(position: Int, item: DevOptionItem) {

                if (item == null)
                    return

                when (item.id) {
                    IDS.ID_1 -> {

                        var vfrag = FragDevSleepTimerUI()
                        vfrag.blurBitmap = ScreenUtil.captureScreen(activity)
                        vfrag.bSendSleepTime = false
                        vfrag.dataInfo = dataInfo
                        dataInfo?.let {
                            FragTabUtils.addFrag(
                                activity,
                                it.frameId,
                                vfrag,
                                true
                            )
                        }
                    }
                    else -> {}
                }
            }

            override fun onSetVolumeLevel(volume: Int) {

                bOperateItemVolume = true
                localSmartConfigItem?.smart_config?.volume?.volume_level = volume
            }

            override fun onSetAtmosLevel(level: String) {
                bOperateItemAtmos = true
                localSmartConfigItem?.smart_config?.atmos?.atmos_level = level
            }

            override fun onSetAtmosStatus(status: Boolean) {
                bOperateItemAtmos = true
                localSmartConfigItem?.smart_config?.atmos?.status =
                    if (status) JBLDataUtil.STATUS_ON else JBLDataUtil.STATUS_OFF
            }
        })
    }

    override fun initUtils() {
        super.initUtils()

        btn_ok?.apply {

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                WAApplication.mResources.getDimension(R.dimen.font_14)
            )
            val tintDrawable = SkinResourcesUtils.getTintDrawable(
                "btn_background_bg_s1", GlobalUIConfig.color_btn_normal,
                "btn_background_bg_s1", GlobalUIConfig.color_btn_press
            )

            text = SkinResourcesUtils.getString("jbl_SAVE")
            background = tintDrawable
            setTextColor(GlobalUIConfig.color_solid_btn_font_color)
        }
    }

    private fun initCurrListData() {

        if (currList == null)
            currList = ArrayList()

        currList.clear()

        var item = DevOptionItem()
        item.hasIcon = false
        item.title = SkinResourcesUtils.getString("jbl_Auto_Off")
        item.id = IDS.ID_1
        item.hasMore = true
        item.more_Name = "component_svg_icon_arrow_forward_square"
        item.subTitle = smartConfigItem?.smart_config?.timer?.sleep_timer.toString()
        currList.add(item)

        item = DevOptionItem()
        item.hasIcon = false
        item.id = IDS.ID_2
        item.title = SkinResourcesUtils.getString("jbl_Volume_Level")
        item.subTitle = smartConfigItem?.smart_config?.volume?.volume_level.toString()
        item.hasMore = false
        currList.add(item)

        item = DevOptionItem()
        item.hasIcon = false

        if (DeviceImageUtil.isJBL700(dataInfo?.deviceItem?.devStatus?.project)
            || DeviceProjectParseUtil.JBLOneProject.isJBL800(dataInfo?.deviceItem?.devStatus?.project)
            || DeviceProjectParseUtil.JBLOneProject.isJBL1000(dataInfo?.deviceItem?.devStatus?.project)
            || DeviceProjectParseUtil.JBLOneProject.isJBL1300(dataInfo?.deviceItem?.devStatus?.project)

        ) {
            item.title = SkinResourcesUtils.getString("jbl_Dolby_Atmos_Level")
            item.hasMore = true
        } else {
            item.title = SkinResourcesUtils.getString("jbl_Dolby_Atmos")
            item.hasMore = false
        }
        item.id = IDS.ID_3
        currList.add(item)
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()

        FragTabUtils.popBack(activity)
    }

    private fun updateTimer() {

        if (currList == null)
            return

        for (i in 0 until currList.size) {
            var item = currList[i]
            if (item.id == IDS.ID_1) {
                item.subTitle = localSmartConfigItem?.smart_config?.timer?.sleep_timer.toString()
                mAdapter?.apply {
                    notifyItemChanged(i)
                }
                break
            }
        }
    }

    override fun update(observable: Observable?, data: Any?) {
        super.update(observable, data)

        if (data is MessageAlbumObject) {
            if (data.type == MessageAlbumType.TYPE_ALARM_CONTEXT_CHANGED) {
                val message: Any = data.message

                if (message is PlayMoreSleepTimerItem) {
                    bOperateItemTimer = true
                    localSmartConfigItem?.smart_config?.timer?.sleep_timer =
                        message.seconds
                    uihd?.post {
                        updateTimer()
                    }
                }
            }
        }
    }
}