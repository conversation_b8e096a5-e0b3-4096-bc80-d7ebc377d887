package com.wifiaudio.view.account

import androidx.fragment.app.FragmentActivity
import com.harman.log.Logger

object FragmentUtil {

    fun log(msg: String) {
        Logger.d("FragmentUtil", msg)
    }

    fun popBackAll(activity: FragmentActivity?, fragId: Int) {
        log("popBackAll: ${activity?.supportFragmentManager?.backStackEntryCount}")

        if (activity == null) {
            log("popBackAll getActivity is null")
            return
        }
        val manager = activity.supportFragmentManager
        val fr = manager.findFragmentById(fragId)
        val backStackEntryCount = manager.backStackEntryCount
        if (backStackEntryCount - 1 < 0) {
            log("popBackAll warning:  backStackEntryCount < 0")
            return
        }
        val backStackEntryAtTop = manager.getBackStackEntryAt(backStackEntryCount - 1)
        log("popBackAll backStackEntryAtTop id: ${backStackEntryAtTop.id}")
//        val topFragmentName = backStackEntryAtTop.name
        if (fr == null) {
            log("popBackAll fr is null")
            return
        }

        try {
            for (count in 0 until backStackEntryCount) {
                manager.popBackStack()
                log("popBackAll back stack count = $count")
            }

        } catch (e: Exception) {
            log("popBackAll e: " + e.localizedMessage)
        }
    }

    fun removeAllFragment(activity: FragmentActivity?, fragId: Int) {
        if (activity == null) {
            log("removeAllFragment getActivity is null")
            return
        }
        val fr = activity.supportFragmentManager.findFragmentById(fragId)
        if (fr == null) {
            log("removeAllFragment fr is null")
            return
        }
        try {
            val manager = activity.supportFragmentManager
            val list = manager.fragments
//            for (fragment in list) {
//                val ft = manager.beginTransaction()
//                ft.remove(fragment!!)
//            }
            var count = manager.backStackEntryCount
            log("removeAllFragment count = $count")
            while (count > 0) {
                manager.popBackStackImmediate()
                count = manager.backStackEntryCount
                log("removeAllFragment back stack count = $count")
            }
        } catch (e: Exception) {
            log("removeAllFragment e: " + e.localizedMessage)
        }
    }
}