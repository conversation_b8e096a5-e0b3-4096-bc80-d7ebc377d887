package com.wifiaudio.view.pagesmsccenter;

import android.app.Activity;
import android.app.Dialog;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.harman.bar.app.R;
import com.skin.SkinResourcesUtils;
import com.utils.glide.GlideMgtUtil;
import com.utils.glide.ImageLoadConfig;
import com.views.view.dslv.back.DragSortListView;
import com.wifiaudio.action.deezer.DeezerSharedPreference;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.action.newiheartradio.IHeartRadioSharedPreference;
import com.wifiaudio.action.qobuz.QobuzSharedPreference;
import com.wifiaudio.action.rhapsody.RhapsodySharedPreference;
import com.wifiaudio.action.tidal.TiDalSharedPrefer;
import com.wifiaudio.app.IInitView;
import com.wifiaudio.app.LinkplayApplication;
import com.wifiaudio.model.AlbumInfo;
import com.wifiaudio.model.DeviceInfoExt;
import com.wifiaudio.model.DeviceItem;
import com.wifiaudio.model.PlayQueueMessage;
import com.wifiaudio.model.PresetModeItem;
import com.wifiaudio.model.albuminfo.AlbumMetadataUpdater;
import com.wifiaudio.model.albuminfo.MessageAlbumObject;
import com.wifiaudio.model.albuminfo.MessageAlbumType;
import com.wifiaudio.model.menuslide.MenuSlideInstaller;
import com.wifiaudio.model.menuslide.MessageMenuObject;
import com.wifiaudio.model.menuslide.MessageMenuType;
import com.wifiaudio.model.newiheartradio.NIHeartRadioGetUserInfoItem;
import com.wifiaudio.model.qobuz.QobuzGetUserInfoItem;
import com.wifiaudio.model.rhapsody.RhapsodyGetUserInfoItem;
import com.wifiaudio.model.tidal.TiDalGetUserInfoItem;
import com.wifiaudio.presenter.autotrack.AutoTrackKit;
import com.wifiaudio.service.DlnaServiceProvider;
import com.wifiaudio.service.MusicPushHelper;
import com.wifiaudio.service.WAConstants;
import com.wifiaudio.service.dlna.IDlnaQueryListener;
import com.wifiaudio.utils.FontManager;
import com.wifiaudio.utils.MusicUpdateHelper;
import com.wifiaudio.utils.StringUtils;
import com.wifiaudio.utils.WifiIPUtil;
import com.wifiaudio.utils.source.SourceCurrentQueueItem;
import com.wifiaudio.view.contentview_tintcolor.ContentView;
import com.wifiaudio.view.contentview_tintcolor.ContentViewImpl;
import com.wifiaudio.view.dlg.DlgLinkNotice;
import com.wifiaudio.view.dlg.MessageDialog;
import com.wifiaudio.view.pagesmsccontent.preset.PubPresetFuc;

import org.teleal.cling.model.ModelUtil;
import org.teleal.cling.support.playqueue.callback.browsequeue.total.SourceItemBase;
import org.teleal.cling.support.playqueue.callback.xml.IPlayQueueType;
import org.teleal.cling.support.playqueue.callback.xml.IPlayQueueTypeImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Observable;
import java.util.Observer;

import config.AppLogTagUtil;
import config.GlobalUIConfig;
import config.LogTags;

/**
 * @Author: leyunrui-wiimu
 * @Date: 09:01 28-04-2017
 * @Version: V1.0
 * @Desc: TODO{:当前播放列表可排序，如 Fabriq定制}
 */
public class CurrPlayListDragActivity extends Activity implements IInitView, Observer, ContentView {

    //private ListView vlist;
    private DragSortListView dragSortListView;
    private Button vBack;
    private Button vMore;

    private TextView vtitle;
    private TextView vEmptyLabel = null;

    private Handler uihd = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);

            int what = msg.what;
            if (what == 1) {
                if (bEditClick) {
                    btnEdit.setText(SkinResourcesUtils.getString("devicelist_Confirm"));
                    dragSortListView.setDragEnabled(true);
                } else {
                    btnEdit.setText(SkinResourcesUtils.getString("Edit"));
                    dragSortListView.setDragEnabled(false);
                }
            }
        }
    };

    private BaseAdapter adapter = null;
    private List<AlbumInfo> playingList = new ArrayList<AlbumInfo>();

    private String mTittle = "";
    private Resources mResources = null;
    private int lastPlayIndex = 0;

    RelativeLayout relativeLayoutHeader1, relativeLayoutHeader2, relativeLayout2;
    LinearLayout linearLayoutBottom;
    TextView tvNum;
    ImageView ivMode;
    Button btnClear, btnEdit;

    private boolean bEditClick = false;//是否点击了编辑按钮
    private Drawable mDrawablePlay;//正常模式下播放按钮
    private Drawable mDrawablePause;//暂停按钮
    private Drawable mDrawableMove;//移动按钮
    private int mFirstVisibleIndex = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState);
        setContentView(R.layout.act_curr_playlist_drag);
        LogsUtil.i(LogTags.UI, "page=" + getClass().getSimpleName() + ":onCreate");
        MenuSlideInstaller.me().addObserver(this);
        AlbumMetadataUpdater.me().addObserver(this);
        initView();
        bindSlots();
        initUtils();
    }

    @Override
    protected void onResume() {
        // TODO Auto-generated method stub
        super.onResume();

        uihd.post(reloadDataTask);
    }

    @Override
    protected void onDestroy() {
        // TODO Auto-generated method stub
        MenuSlideInstaller.me().deleteObserver(this);
        AlbumMetadataUpdater.me().deleteObserver(this);
        super.onDestroy();
    }

    final Runnable reloadDataTask = new Runnable() {

        @Override
        public void run() {
            // TODO Auto-generated method stub
            reloadData_PlayList();
        }
    };

    public void reloadData_PlayList() {

        // 如果是第三方播放器，没得播放列表，无需访问
        DeviceItem currentDeviceItem = LinkplayApplication.me.CurrentDevice;

        if (currentDeviceItem == null) {
            return;
        }
        DeviceInfoExt tInfoExt = currentDeviceItem.devInfoExt;

        if (tInfoExt.getDlnaPlayMedium().toUpperCase().equals(WAConstants.IStorageMediumType.SPOTIFY)
                || tInfoExt.getDlnaTrackSource().toUpperCase().equals(WAConstants.IStorageMediumType.SPOTIFY)) {
            vMore.setVisibility(View.INVISIBLE);//预置不可见

            if (relativeLayoutHeader2 != null)
                relativeLayoutHeader2.setVisibility(View.GONE);
            setBtnEnableStatus(false);
            findViewById(R.id.vline2).setVisibility(View.GONE);

            mTittle = currentDeviceItem.devInfoExt.albumInfo.title;
            TextView vSpotify = new TextView(CurrPlayListDragActivity.this);
            vSpotify.setText(mTittle);
            vSpotify.setPadding(15, 15, 15, 15);
            vSpotify.setGravity(Gravity.LEFT | Gravity.CENTER_VERTICAL);
            vSpotify.setTextColor(GlobalUIConfig.color_app_theme);

            vSpotify.setSingleLine();
            vSpotify.setBackgroundResource(R.drawable.select_libg);
            vSpotify.setTextSize(TypedValue.COMPLEX_UNIT_PX,
                    LinkplayApplication.me.getResources().getDimensionPixelSize(R.dimen.font_18));
            vSpotify.setEllipsize(TextUtils.TruncateAt.MIDDLE);

            Drawable leftIcon = LinkplayApplication.me.getResources().getDrawable(com.wifiaudio.R.drawable.icon_current_playqueue_spotify);
            leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());

            vSpotify.setCompoundDrawables(leftIcon, null, null, null);
            //vlist.addHeaderView(vSpotify);
            //vlist.setAdapter(createAdapter());

            dragSortListView.addHeaderView(vSpotify);
            dragSortListView.setAdapter(createAdapter());

            return;
        } else {
            vMore.setVisibility(View.INVISIBLE);
        }

        // 是否含有播放歌单 ,默认是没有的
        boolean hasPlayingList = false;
        hasPlayingList |= tInfoExt.getDlnaPlayMedium().equals(WAConstants.IStorageMediumType.SONGLIST_LOCAL);
        hasPlayingList |= tInfoExt.getDlnaPlayMedium().equals(WAConstants.IStorageMediumType.SONGLIST_LOCAL_TF);
        hasPlayingList |= tInfoExt.getDlnaPlayMedium().equals(WAConstants.IStorageMediumType.SONGLIST_NETWORK);
        hasPlayingList |= tInfoExt.getDlnaPlayMedium().toUpperCase().equals(IPlayQueueType.EXTQPLAY);

        if (!hasPlayingList) { // 没有歌单的条件

            tvNum.setText(0 + " " + SkinResourcesUtils.getString("mymusic__Song"));
            setBtnEnableStatus(false);
            ivMode.setVisibility(View.GONE);
            vEmptyLabel.setVisibility(View.VISIBLE);
            if (playingList != null)
                playingList.clear();
            //vlist.setAdapter(createAdapter());
            dragSortListView.setAdapter(createAdapter());

            return;
        }

        vEmptyLabel.setVisibility(View.INVISIBLE);

        if (playingList != null)
            playingList.clear();
        //vlist.setAdapter(createAdapter());
        dragSortListView.setAdapter(createAdapter());

        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, true,
                SkinResourcesUtils.getString("playview_Loading____"));

        mFirstVisibleIndex = 0;
        getCurrQueue();
    }

    private void reportMusicService(String source) {
        String uuid = "unknow";
        if (LinkplayApplication.me.CurrentDevice != null) {
            uuid = LinkplayApplication.me.CurrentDevice.devStatus.uuid;
        }

        if (source == null) {
            return;
        }
        int isLogin = 0;
        String userID = "";
        String sourceVersion = "1";
        switch (source) {
            case IPlayQueueType.EXTDEEZER:
                if (DeezerSharedPreference.getInstance().getUserInfo() != null) {
                    String loginName = DeezerSharedPreference.getInstance().getUserInfo().user_name;
                    userID = loginName;
                    if (DeezerSharedPreference.getInstance().getUserInfo().msg != null && DeezerSharedPreference.getInstance().getUserInfo().msg.equals(RhapsodyGetUserInfoItem.LOGIN)) {
                        isLogin = 1;
                    } else {
                        isLogin = 0;
                    }
                } else {
                    isLogin = 0;
                }
                break;
            case IPlayQueueType.EXTIHeartRadio:
                NIHeartRadioGetUserInfoItem loginBaseItem = IHeartRadioSharedPreference.getInstance().getUserInfo();
                if (loginBaseItem != null && loginBaseItem.msg != null && loginBaseItem.msg.equals(NIHeartRadioGetUserInfoItem.LOGIN)) {
                    isLogin = 1;
                    userID = loginBaseItem.name;
                } else {
                    isLogin = 0;
                }
                break;
            case IPlayQueueType.Qobuz:
                if (QobuzSharedPreference.getInstance().getUserInfo() != null) {
                    String loginusername = QobuzSharedPreference.getInstance().getUserInfo().username;
                    userID = loginusername;
                    if (QobuzSharedPreference.getInstance().getUserInfo().msg != null && QobuzSharedPreference.getInstance().getUserInfo().msg.equals(QobuzGetUserInfoItem.LOGIN)) {
                        isLogin = 1;
                    } else {
                        isLogin = 0;
                    }

                } else {
                    isLogin = 0;
                }
                break;
            case IPlayQueueType.EXTRHAPSODY:
            case IPlayQueueType.ALDI_LIFE_MUSIK:
                RhapsodyGetUserInfoItem userInfoItem = RhapsodySharedPreference.getInstance().getUserLoginInfo(source);

                if (userInfoItem != null && userInfoItem.msg != null && userInfoItem.msg.equals(RhapsodyGetUserInfoItem.LOGIN)) {
                    isLogin = 1;
                    userID = userInfoItem.username;
                } else {
                    isLogin = 0;
                }
                break;
            case IPlayQueueType.EXTTIDAL:
                TiDalGetUserInfoItem tiDalGetUserInfoItem = TiDalSharedPrefer.getInstance().getUserInfo();
                if (tiDalGetUserInfoItem != null && tiDalGetUserInfoItem.msg != null && tiDalGetUserInfoItem.msg.equals(TiDalGetUserInfoItem.LOGIN)) {
                    isLogin = 1;
                    userID = tiDalGetUserInfoItem.userId;
                } else {
                    isLogin = 0;
                }
                break;
        }
        AutoTrackKit.geInstance().playMusic(isLogin, uuid, userID, sourceVersion, source);
    }

    private void setBtnEnableStatus(boolean enable) {

        if (btnClear != null)
            btnClear.setEnabled(enable);
        if (btnEdit != null)
            btnEdit.setEnabled(enable);
    }

    private void getCurrQueue() {

        MusicPushHelper.queryCurrentQueueAndReplaceLocalQueue(false,
                new DlnaServiceProvider.IQueryCurrentQueue() {

                    @Override
                    public void onSuccess(
                            final SourceCurrentQueueItem currentQueueItem) {
                        uihd.removeCallbacksAndMessages(null);

                        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this,
                                false, null);
                        uihd.post(new Runnable() {
                            public void run() {

                                if (currentQueueItem == null) {
                                    tvNum.setText(0 + " " + SkinResourcesUtils.getString("mymusic__Song"));
                                    if (playingList != null)
                                        playingList.clear();
                                    adapter.notifyDataSetChanged();
                                    setBtnEnableStatus(false);
                                    return;
                                }

                                if (currentQueueItem.tracksList.size() <= 0) {
                                    tvNum.setText(0 + " " + SkinResourcesUtils.getString("mymusic__Song"));
                                    if (playingList != null)
                                        playingList.clear();
                                    adapter.notifyDataSetChanged();
                                    setBtnEnableStatus(false);
                                    return;
                                }

                                try {
                                    lastPlayIndex = 0;
                                    if (StringUtils.isNumDigits(currentQueueItem.LastPlayIndex))
                                        lastPlayIndex = Integer.parseInt(currentQueueItem.LastPlayIndex);
                                } catch (Exception e) {
                                    lastPlayIndex = 0;
                                }

                                if (playingList != null) {
                                    mFirstVisibleIndex = dragSortListView.getFirstVisiblePosition();
                                    playingList.clear();
                                } else {
                                    playingList = new ArrayList<AlbumInfo>();
                                }

                                for (int i = 0; i < currentQueueItem.tracksList
                                        .size(); i++) {
                                    AlbumInfo item = currentQueueItem.tracksList
                                            .get(i);
                                    item.sourceType = currentQueueItem.Name;
                                    playingList.add(item);
                                }

                                if (playingList.size() <= 0) {
                                    vEmptyLabel.setVisibility(View.VISIBLE);
                                    setBtnEnableStatus(false);
                                } else {
                                    vEmptyLabel.setVisibility(View.GONE);
                                    setBtnEnableStatus(true);
                                }

                                if (playingList.size() > 1) {
                                    tvNum.setText(playingList.size() + " " + SkinResourcesUtils.getString("mymusic__Songs"));
                                } else {
                                    tvNum.setText(playingList.size() + " " + SkinResourcesUtils.getString("mymusic__Song"));
                                }

                                //vlist.setAdapter(createAdapter());
                                dragSortListView.setAdapter(createAdapter());

                                int selection = getItemIndex();
                                LogsUtil.i(AppLogTagUtil.LogTag, "selection: " + selection);
//                                vlist.setSelection(getItemIndex(LinkplayApplication.me.CurrentDevice.devInfoExt
//                                        .getDlnaTrackURI()));
                                if (mFirstVisibleIndex < playingList.size())
                                    dragSortListView.setSelection(mFirstVisibleIndex);
                                //dragSortListView.setSelection(getItemIndex());
                            }
                        });
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        // TODO Auto-generated method stub

                        uihd.post(new Runnable() {
                            @Override
                            public void run() {
                                if (playingList != null)
                                    playingList.clear();
                                adapter.notifyDataSetChanged();
                                tvNum.setText(0 + " " + SkinResourcesUtils.getString("mymusic__Song"));
                                setBtnEnableStatus(false);
                            }
                        });

                        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this,
                                false, null);
                    }
                });
    }

    @Override
    public void initView() {
        mResources = LinkplayApplication.me.getResources();
        vMore = (Button) findViewById(R.id.vmore);
        vBack = (Button) findViewById(R.id.vback);
        vtitle = (TextView) findViewById(R.id.vtitle);
        vEmptyLabel = (TextView) findViewById(R.id.emtpy_textview);
        //vlist = (ListView) findViewById(R.id.vlist);
        dragSortListView = (DragSortListView) findViewById(R.id.dslvList);
        vMore.setVisibility(View.GONE);

        vMore.setBackgroundResource(R.drawable.select_icon_msc_preset);

        relativeLayoutHeader1 = (RelativeLayout) findViewById(R.id.vheader);
        relativeLayoutHeader2 = (RelativeLayout) findViewById(R.id.vheader2);
        linearLayoutBottom = (LinearLayout) findViewById(R.id.relayout3);
        relativeLayout2 = (RelativeLayout) findViewById(R.id.relayout2);
        tvNum = (TextView) findViewById(R.id.tv_num);
        ivMode = (ImageView) findViewById(R.id.iv_mode);
        btnClear = (Button) findViewById(R.id.btn_clear);
        btnEdit = (Button) findViewById(R.id.btn_edit);

        btnClear.setText(SkinResourcesUtils.getString("Clear"));
        btnEdit.setText(SkinResourcesUtils.getString("Edit"));
        vtitle.setText(SkinResourcesUtils.getString("playview_Current").toUpperCase());

        initPageView(findViewById(R.id.vparentview));

        vEmptyLabel.setText(SkinResourcesUtils.getString("playview_No_playlist_info_available"));

        dragSortListView.setDropListener(onDrop);
        dragSortListView.setRemoveListener(onRemove);
        dragSortListView.setDragEnabled(false);

        updatePlayMode();
    }

    @Override
    public void initPageView(View view) {

        ContentView contentView = new ContentViewImpl();
        if (contentView != null)
            contentView.initPageView(view);
    }

    private DragSortListView.DropListener onDrop = new DragSortListView.DropListener() {
        @Override
        public void drop(int from, int to) {
            if (from != to) {
                if (playingList != null && playingList.size() > 0) {
                    AlbumInfo item = playingList.get(from);

                    mFirstVisibleIndex = dragSortListView.getFirstVisiblePosition();

                    playingList.remove(from);
                    adapter.notifyDataSetChanged();
                    playingList.add(to, item);
                    dragSortListView.setAdapter(createAdapter());
                    setCurrItemIndex();
                    if (mFirstVisibleIndex < playingList.size())
                        dragSortListView.setSelection(mFirstVisibleIndex);
                    // dragSortListView.setSelection(to);
                    // sortItem(from, to);
                }
            }
        }
    };

    private void setCurrItemIndex() {

        DeviceInfoExt deviceInfoExt = LinkplayApplication.me.CurrentDevice.devInfoExt;

        String tmpArtist = deviceInfoExt.albumInfo.artist;
        String tmpAlbum = deviceInfoExt.albumInfo.album;
        String tmpTitle = deviceInfoExt.albumInfo.title;
        for (int i = 0; i < playingList.size(); i++) {
            AlbumInfo tmpAlbumInfo = playingList.get(i);
            if (tmpTitle.equals(tmpAlbumInfo.title) && tmpAlbum.equals(tmpAlbumInfo.album) && tmpArtist.equals(tmpAlbumInfo.artist)) {
                lastPlayIndex = i + 1;
                return;
            }
        }
    }

    private DragSortListView.RemoveListener onRemove = new DragSortListView.RemoveListener() {
        @Override
        public void remove(int which) {
            if (playingList != null && playingList.size() > 0)
                deleteItem(which);
        }
    };


    private void updatePlayMode() {
        DeviceItem currentDeviceItem = LinkplayApplication.me.CurrentDevice;
        if (currentDeviceItem == null)
            return;

        int playMode = currentDeviceItem.devInfoExt.getDlnaPlayMode();

        switch (playMode) {
            case DlnaServiceProvider.IQueueLoopMode.Shuffle: {
                Drawable drawable = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_currlist_shuffle");
                if (drawable != null) {
                    drawable = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me,
                            drawable, GlobalUIConfig.color_normal);
                    ivMode.setBackgroundDrawable(drawable);
                }
            }
            break;
            case DlnaServiceProvider.IQueueLoopMode.Single: {
                Drawable drawable = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_currlist_loopsingle");
                if (drawable != null) {
                    drawable = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me,
                            drawable, GlobalUIConfig.color_normal);
                    ivMode.setBackgroundDrawable(drawable);
                }
            }
            break;
            case DlnaServiceProvider.IQueueLoopMode.List: {
                Drawable drawable = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_currlist_looplist");
                if (drawable != null) {
                    drawable = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me,
                            drawable, GlobalUIConfig.color_normal);
                    ivMode.setBackgroundDrawable(drawable);
                }
            }
            break;
            case DlnaServiceProvider.IQueueLoopModeLifeBeatX.ShuffleX: {
                Drawable drawable = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_currlist_shuffle");
                if (drawable != null) {
                    drawable = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me,
                            drawable, GlobalUIConfig.color_normal);
                    ivMode.setBackgroundDrawable(drawable);
                }
            }
            break;
            default: {
                Drawable drawable = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_currlist_looplist");
                if (drawable != null) {
                    drawable = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me,
                            drawable, GlobalUIConfig.color_normal);
                    ivMode.setBackgroundDrawable(drawable);
                }
            }
            break;
        }
    }

    private void showFactoryDialog() {
        final DlgLinkNotice noticeDialog = new DlgLinkNotice(CurrPlayListDragActivity.this);
        noticeDialog.setBtnText(SkinResourcesUtils.getString("content_Cancel").toUpperCase(),
                SkinResourcesUtils.getString("Clear").toUpperCase());
        noticeDialog.setNoticeText(SkinResourcesUtils.getString("Clear Queue?"));
        noticeDialog.setLinkNoticeListener(new DlgLinkNotice.ILinkNoticeListener() {
            @Override
            public void onConfirm(Dialog dlg) {
                noticeDialog.dismiss();

                removeTracks();
            }

            @Override
            public void onCancel(Dialog dlg) {
                noticeDialog.dismiss();
            }
        });
        noticeDialog.show();
    }

    private void removeTracks() {

        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this,
                true, SkinResourcesUtils.getString("playview_Loading____"));

        MusicPushHelper.removeTracksInQueue(1, playingList.size(), new IDlnaQueryListener() {

            @Override
            public void onSuccess(Map datamap) {
                uihd.post(new Runnable() {
                    @Override
                    public void run() {

                        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this,
                                false, null);

                        if (playingList != null)
                            playingList.clear();
                        adapter.notifyDataSetChanged();
                        tvNum.setText(0 + " " + SkinResourcesUtils.getString("mymusic__Song"));

                        finish();
                    }
                });
            }

            @Override
            public void onFailure(Throwable e) {
                LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this,
                        false, null);
            }
        });
    }


    MessageDialog messageDlg;

    private void showHintDlg() {

        if (messageDlg != null && messageDlg.isShowing()) {
            messageDlg.dismiss();
            messageDlg = null;
        }

        messageDlg = new MessageDialog(CurrPlayListDragActivity.this, R.style.CustomDialog);
        messageDlg.show();
        messageDlg.setTitle("");
        messageDlg.setDlgBg(1);
        messageDlg.setMessage(SkinResourcesUtils.getString("Clear_Queue"));
        messageDlg.setContentTextColor(mResources.getColor(R.color.fg_primary));
        messageDlg.setCancelText(SkinResourcesUtils.getString("content_Cancel"), mResources.getColor(R.color.gray));
        messageDlg.setOptionText(SkinResourcesUtils.getString("Clear"), mResources.getColor(R.color.fg_primary));
        messageDlg.setDoubleVisible(true);
        messageDlg.setCanceledOnTouchOutside(false);
        messageDlg.setOnClickListener(new MessageDialog.MessageDlgClickListener() {
            @Override
            public void clickCancel() {
                messageDlg.dismiss();
            }

            @Override
            public void clickOption() {
                messageDlg.dismiss();

                removeTracks();
            }
        });
    }

    @Override
    public void bindSlots() {

        btnClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showHintDlg();
            }
        });

        btnEdit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bEditClick = !bEditClick;
                uihd.sendEmptyMessage(1);
                adapter.notifyDataSetChanged();
            }
        });

        vMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // TODO Auto-generated method stub
                doPreset();
            }
        });

        vBack.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // TODO Auto-generated method stub
                finish();
            }
        });

//        vlist.setOnItemClickListener(new AdapterView.OnItemClickListener() {
//
//            @Override
//            public void onItemClick(AdapterView<?> parent, View view,
//                                    int position, long id) {
//                onClick(position);
//            }
//        });

        dragSortListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

                onClick(position);
            }
        });

    }

    @Override
    public void initUtils() {

        updateTheme();
    }

    private void updateTheme() {
    }

    private void doPreset() {

        if (adapter == null) {
            return;
        }


        DeviceItem currentDeviceItem = LinkplayApplication.me.CurrentDevice;
        PresetModeItem tmpItem = new PresetModeItem();

        tmpItem.activity = CurrPlayListDragActivity.this;
        //tmpItem.parent = vlist;
        tmpItem.parent = dragSortListView;
        tmpItem.search_id = 0;
        tmpItem.searchUrl = "";
        tmpItem.title = mTittle;
        tmpItem.search_page = 0;
        tmpItem.page_count = 0;
        tmpItem.strImgUrl = currentDeviceItem.devInfoExt.albumInfo.albumArtURI;
        tmpItem.albumlist = playingList;
        tmpItem.queueName = mTittle + PresetModeItem.getLocalFormatTime();
        tmpItem.sourceType = IPlayQueueType.ExtSpotify;
        tmpItem.isRadio = false;

        PubPresetFuc mPreset = new PubPresetFuc();
        mPreset.doPresetAlbumsNoBackup(tmpItem);
    }

    /***
     * 单击事件
     *
     * @param
     */
    private void onClick(int pos) {

        if (playingList.size() <= pos) {
            return;
        }

        if (playingList.get(pos).sourceType.contains(IPlayQueueType.ExtSpotify)) {

            return;
        }
        reportMusicService(playingList.get(pos).source);
        MusicPushHelper.playQueue(playingList.get(pos).sourceType, pos);

        uihd.post(new Runnable() {
            public void run() {
                finish();
            }
        });
    }

    private BaseAdapter createAdapter() {
        adapter = new BaseAdapter() {

            @Override
            public View getView(final int position, View view, ViewGroup parent) {
                // TODO Auto-generated method stub

                HolderView hv;
                if (view == null) {

                    hv = new HolderView();
                    view = LayoutInflater.from(CurrPlayListDragActivity.this).inflate(R.layout.item_curr_queue,
                            null);
                    hv.ivIcon = (ImageView) view.findViewById(R.id.iv_icon);
                    hv.tvsongname = (TextView) view.findViewById(R.id.vsong_name);
                    hv.tvsingername = (TextView) view.findViewById(R.id.vsinger_name);
                    hv.tvduration = (TextView) view.findViewById(R.id.vduration);
                    hv.ivDel = (ImageView) view.findViewById(R.id.click_remove);
                    hv.ivMore = (ImageView) view.findViewById(R.id.drag_handle);
                    hv.ivHightBG = (RelativeLayout) view.findViewById(R.id.list_display_view_container);
                    hv.vContentView = view;

                    view.setTag(hv);
                } else {
                    hv = (HolderView) view.getTag();
                }

                hv.ivDel.setBackgroundResource(R.drawable.select_icon_search_del_history);


                FontManager.changeFonts((ViewGroup) view);

                AlbumInfo item = playingList.get(position);

                hv.tvsongname.setText(item.title);
                hv.tvsingername.setText(item.artist + "-" + item.album);
                if (item.duration != 0 && item.duration != 1) {
                    hv.tvduration.setText(ModelUtil.toTimeString(item.duration / 1000));
                } else {
                    hv.tvduration.setText("");
                }

//                hv.ivDel.setOnClickListener(new View.OnClickListener() {
//
//                    @Override
//                    public void onClick(View v) {
//                        // TODO Auto-generated method stub
//                        deleteItem(position);
//                    }
//                });

                if (LinkplayApplication.me.CurrentDevice == null) {
                    return view;
                }

                DeviceInfoExt deviceInfoExt = LinkplayApplication.me.CurrentDevice.devInfoExt;

                String tmpArtist = deviceInfoExt.albumInfo.artist;
                String tmpAlbum = deviceInfoExt.albumInfo.album;
                String tmpTitle = deviceInfoExt.albumInfo.title;

                int iColor = GlobalUIConfig.color_hight;

                //  if (tmpTitle.equals(item.title) && tmpAlbum.equals(item.album) && tmpArtist.equals(item.artist)) {
                if (lastPlayIndex > 0 && lastPlayIndex <= playingList.size()) {
                    if (position == lastPlayIndex - 1) {
                        hv.tvsongname.setTextColor(iColor);
                    } else {
                        int songNameColor = mResources.getColor(R.color.fg_primary);

                        hv.tvsongname.setTextColor(songNameColor);
                    }
                } else {
                    hv.tvsongname.setTextColor(iColor);
                }

                int size = LinkplayApplication.me.getResources().getDimensionPixelSize(R.dimen.width_60);

                ImageLoadConfig.OverrideSize overrideSize = new ImageLoadConfig.OverrideSize(size,
                        size);

                ImageLoadConfig config = ImageLoadConfig.parseBuilder(GlideMgtUtil.defConfig).
                        setSkipMemoryCache(false).
                        setAsBitmap(true).
                        setPlaceHolderResId(R.drawable.global_images).
                        setErrorResId(R.drawable.global_images).
                        setDiskCacheStrategy(ImageLoadConfig.DiskCache.SOURCE).
                        setSize(overrideSize).
                        build();

                GlideMgtUtil.loadStringRes(getApplicationContext(), hv.ivIcon, item.albumArtURI, config, null);

                return view;
            }

            @Override
            public long getItemId(int position) {
                // TODO Auto-generated method stub
                return position;
            }

            @Override
            public Object getItem(int position) {
                // TODO Auto-generated method stub
                return playingList.get(position);
            }

            @Override
            public int getCount() {
                // TODO Auto-generated method stub
                return playingList.size();
            }
        };

        return adapter;
    }


    protected int getImgOnLoading() {
        return R.drawable.global_images;
    }

    protected int getImgForEmptyUri() {
        return R.drawable.global_images;
    }

    protected int getImgOnFail() {
        return R.drawable.global_images;
    }

    class HolderView {
        View vContentView;
        TextView tvsongname;
        TextView tvsingername;
        TextView tvduration;
        ImageView ivIcon;
        ImageView ivDel;
        ImageView ivMore;
        RelativeLayout ivHightBG;
    }

    public int getItemIndex() {

        String curIP = null;
        String playUrl = LinkplayApplication.me.CurrentDevice.devInfoExt.getDlnaTrackURI();
        if (LinkplayApplication.me.CurrentDevice.devInfoExt.getDlnaTrackSource().equals(IPlayQueueTypeImpl.ExtLocalQueue)) {//更换本地IP
            curIP = WifiIPUtil.makeIPV4IP(LinkplayApplication.me);
            if (curIP != null) {
                playUrl = MusicUpdateHelper.getLocalIPUrl(curIP, playUrl);
            }
        }

        for (int i = 0; i < playingList.size(); i++) {
            AlbumInfo tmpAlbumInfo = playingList.get(i);
            if (tmpAlbumInfo.playUri.trim().equals(playUrl.trim())) {
                return i;
            }
        }
        return 0;
    }

    public void sortItem(int from, final int to) {

        // 1.判断当前有无播放歌曲和类型，
        // 2.获得当前播放的index
        // 3.插入歌曲(条件处理)

        AlbumInfo tmpAlbumInfo = playingList.get(from);
        String curIP = null;
        if (tmpAlbumInfo.sourceType.equals(IPlayQueueTypeImpl.ExtLocalQueue)) {
            curIP = WifiIPUtil.makeIPV4IP(LinkplayApplication.me);
        }
        //将歌曲插入到 下一曲需要 替换IP
        if (curIP != null) {
            tmpAlbumInfo = MusicPushHelper.getLocalAlbumInfo(tmpAlbumInfo.sourceType, curIP, tmpAlbumInfo);
        }
        final AlbumInfo albumInfo = tmpAlbumInfo;

        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, true, SkinResourcesUtils.getString("playview_Loading____"));

        MusicPushHelper.removeTracksInQueue(from + 1, from + 1, new IDlnaQueryListener() {

            @Override
            public void onSuccess(Map datamap) {
                // TODO Auto-generated method stub

                SourceItemBase baseItem = new SourceItemBase();
                baseItem.Name = IPlayQueueType.ExtCurrentQueue;
                baseItem.Source = "";
                baseItem.SearchUrl = "";
                baseItem.isRadio = false;
                baseItem.Quality = "2";

                MusicPushHelper.appendTracksInQueueEx(baseItem, albumInfo, to, new IDlnaQueryListener() {

                    @Override
                    public void onSuccess(Map datamap) {
                        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, false, null);
                        LinkplayApplication.me.toastMessage(CurrPlayListDragActivity.this, true,
                                SkinResourcesUtils.getString("content_Added_successfully"));
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, false, null);

                        LinkplayApplication.me.toastMessage(CurrPlayListDragActivity.this, true,
                                SkinResourcesUtils.getString("content_Added_failed"));
                    }

                });
            }

            @Override
            public void onFailure(Throwable e) {
                LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, false, null);

                LinkplayApplication.me.toastMessage(CurrPlayListDragActivity.this, true,
                        SkinResourcesUtils.getString("content_Added_failed"));
            }
        });
    }


    private void deleteItem(final int position) {

        if (playingList == null || playingList.size() <= 0)
            return;

        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, true, SkinResourcesUtils.getString("playview_Please_wait"));
        MusicPushHelper.removeTracksInQueue(position + 1, position + 1, new IDlnaQueryListener() {

            @Override
            public void onSuccess(Map datamap) {
                // TODO Auto-generated method stub

                uihd.post(new Runnable() {
                    public void run() {

                        LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, false, null);

                        boolean bJudgeOk = false;// 是否可以判断
                        if (playingList == null || playingList.size() <= 0 || position > playingList.size()) {
                            return;
                        }

                        if (LinkplayApplication.me.CurrentDevice == null) {
                            return;
                        }
                        if (LinkplayApplication.me.CurrentDevice.devInfoExt == null) {
                            return;
                        }

                        AlbumInfo tmpCurrAlbumInfo = LinkplayApplication.me.CurrentDevice.devInfoExt.albumInfo;
                        if (tmpCurrAlbumInfo == null) {
                            return;
                        }
                        if (playingList == null || playingList.size() <= 0) {
                            return;
                        }
                        AlbumInfo itemAlbumInfo = null;
                        if (position > playingList.size()) {
                            return;
                        }

                        itemAlbumInfo = playingList.get(position);
                        if (itemAlbumInfo == null) {
                            return;
                        }

                        if (tmpCurrAlbumInfo.creator != null
                                && tmpCurrAlbumInfo.album != null
                                && tmpCurrAlbumInfo.title != null
                                && itemAlbumInfo != null
                                && itemAlbumInfo.creator != null && itemAlbumInfo.album != null
                                && itemAlbumInfo.title != null) {// 检查下，保护语句
                            bJudgeOk = true; // 可以判断
                        }

                        if (bJudgeOk
                                && (tmpCurrAlbumInfo.creator
                                .equals(itemAlbumInfo.creator))
                                && (tmpCurrAlbumInfo.album
                                .equals(itemAlbumInfo.album))
                                && (tmpCurrAlbumInfo.title
                                .equals(itemAlbumInfo.title))) {

                            int tPos = -1;
                            int curSize = playingList.size();
                            int toPlayIndex = -1;
                            if (curSize >= 1) {
                                if (curSize == 1) {
                                    tPos = -1;
                                } else {
                                    if (position == curSize - 1) {
                                        tPos = 0;
                                        toPlayIndex = 0;
                                    } else {
                                        tPos = position + 1;
                                        toPlayIndex = position;
                                    }
                                }
                            }

                            final int nextPosition = tPos;

                            try {
                                if (nextPosition > -1) {
                                    MusicPushHelper.playQueue(playingList.get(nextPosition).sourceType, toPlayIndex);
                                }

                            } catch (Exception e) {
                                // TODO: handle exception
                                e.printStackTrace();
                            }
                        }

                        try {

                            playingList.remove(position);

                            if (playingList != null) {
                                if (playingList.size() > 1) {
                                    tvNum.setText(playingList.size() + " " + SkinResourcesUtils.getString("mymusic__Songs"));
                                    mFirstVisibleIndex = dragSortListView.getFirstVisiblePosition();
                                } else {
                                    tvNum.setText(playingList.size() + " " + SkinResourcesUtils.getString("mymusic__Song"));
                                    mFirstVisibleIndex = 0;
                                }
                            }

                            if (playingList == null || playingList.size() <= 0) {
                                setBtnEnableStatus(false);
                            }

//                            vlist.setAdapter(createAdapter());
//                            vlist.setSelection(getItemIndex(
//                                    LinkplayApplication.me.CurrentDevice.devInfoExt.getDlnaTrackURI()));

                            dragSortListView.setAdapter(createAdapter());
                        } catch (Exception e) {
                            // TODO: handle exception
                            e.printStackTrace();
                        }

                        if (playingList == null || playingList.size() <= 0) {
                            vEmptyLabel.setVisibility(View.VISIBLE);
                        } else {
                            if (mFirstVisibleIndex < playingList.size())
                                dragSortListView.setSelection(mFirstVisibleIndex);
                            vEmptyLabel.setVisibility(View.GONE);
                        }


                    }
                });
            }

            @Override
            public void onFailure(Throwable e) {
                LinkplayApplication.me.showProgDlg(CurrPlayListDragActivity.this, false, null);
                String msgDelFailure = SkinResourcesUtils.getString("playview_Delete_fail");
                LinkplayApplication.me.toastMessage(CurrPlayListDragActivity.this, true, msgDelFailure);
            }
        });
    }

    @Override
    public void update(Observable arg0, Object data) {
        // TODO Auto-generated method stub
        if (data instanceof MessageMenuObject) {
            updateByMessageMenuObject((MessageMenuObject) data);
        } else if (data instanceof MessageAlbumObject) {
            final MessageAlbumObject msg = (MessageAlbumObject) data;
            MessageAlbumType type = msg.getType();
            if (type.equals(MessageAlbumType.TYPE_UPDATE_ALBUMINFO)) {

                uihd.post(() -> {
                    setCurrItemIndex();
                    adapter.notifyDataSetChanged();
                });
            }
        }
    }

    private void updateByMessageMenuObject(MessageMenuObject data) {
        MessageMenuObject msg = (MessageMenuObject) data;
        if (msg.getType() == MessageMenuType.TYPE_FRAGMENT_HIDE) {
            if (uihd == null) {
                return;
            }
            uihd.post(new Runnable() {
                public void run() {
                    if (adapter != null) {
                        adapter.notifyDataSetChanged();
                    }
                }
            });
        } else if (msg.getType() == MessageMenuType.TYPE_PLAYQUEUE_CHANGED) {
            if (msg.getMessage() instanceof PlayQueueMessage) {
                try {
                    DeviceItem curdev = LinkplayApplication.me.CurrentDevice;
                    PlayQueueMessage playQueueMessage = (PlayQueueMessage) msg.getMessage();
                    if (curdev.uuid.equals(playQueueMessage.getUuid())) {

                        getCurrQueue();

//                        uihd.post(new Runnable() {
//                            public void run() {
//                                if (adapter != null) {
//                                    adapter.notifyDataSetChanged();
//                                }
//                            }
//                        });
                    }
                } catch (Exception e) {
                }
            }
        }
    }

    private void updateThemeFabriq() {

        Drawable drawable1 = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_queue_004");
        if (drawable1 != null)
            vBack.setBackgroundDrawable(drawable1);

        vtitle.setText(SkinResourcesUtils.getString("Queue"));
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        params.addRule(RelativeLayout.CENTER_IN_PARENT);
        vtitle.setLayoutParams(params);

        vtitle.setTextColor(mResources.getColor(R.color.fg_primary));
        vEmptyLabel.setTextColor(mResources.getColor(R.color.fg_primary));
        tvNum.setTextColor(mResources.getColor(R.color.fg_primary));


        int colorPress = mResources.getColor(R.color.vol_gray);
        int colorNormal = mResources.getColor(R.color.bg_inverse);
        int[] colors = new int[]{colorPress, colorNormal, colorPress};
        int[][] states = new int[3][];
        states[0] = new int[]{android.R.attr.state_enabled, android.R.attr.state_pressed};
        states[1] = new int[]{android.R.attr.state_enabled};
        states[2] = new int[]{};
        ColorStateList colorStateList = new ColorStateList(states, colors);
        btnClear.setTextColor(colorStateList);
        btnEdit.setTextColor(colorStateList);

        int layoutHeaderColor = GlobalUIConfig.color_app_theme;
        int layoutContentColor = GlobalUIConfig.color_app_theme;

        if (relativeLayoutHeader1 != null)
            relativeLayoutHeader1.setBackgroundColor(layoutHeaderColor);
        if (relativeLayoutHeader2 != null)
            relativeLayoutHeader2.setBackgroundColor(layoutContentColor);
        if (relativeLayout2 != null)
            relativeLayout2.setBackgroundColor(layoutContentColor);


        if (linearLayoutBottom != null)
            linearLayoutBottom.setBackgroundColor(layoutHeaderColor);

        //vlist.setDividerHeight(0);

        dragSortListView.setDividerHeight(0);

        int iconColor = mResources.getColor(R.color.bg_inverse);
        mDrawableMove = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_queue_006");
        if (mDrawableMove != null)
            mDrawableMove = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me, mDrawableMove, iconColor);

        mDrawablePause = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_queue_007");
        if (mDrawablePause != null)
            mDrawablePause = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me,
                    mDrawablePause, iconColor);

        mDrawablePlay = SkinResourcesUtils.getDrawable(LinkplayApplication.me, 0, "audioplay_queue_008");
        if (mDrawablePlay != null)
            mDrawablePlay = SkinResourcesUtils.getSingleTintDrawable(LinkplayApplication.me, mDrawablePlay,
                    iconColor);
    }
}
