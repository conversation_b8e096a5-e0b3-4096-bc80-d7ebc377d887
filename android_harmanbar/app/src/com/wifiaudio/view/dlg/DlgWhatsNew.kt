package com.wifiaudio.view.dlg

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import android.widget.TextView
import com.skin.SkinResourcesUtils
import com.harman.bar.app.R
import config.GlobalUIConfig

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/2 14:02
 * @Version 1.0
 */
class DlgWhatsNew(context: Context) : Dialog(context, R.style.CustomDialog) {
    private val view: View
    private val tv1: TextView
    private val tv2: TextView
    private val tv3: TextView
    private val btnOk: TextView
    private var callback: OnBtnClick? = null


    fun setOnBtnClickListener(callback: OnBtnClick?) {
        this.callback = callback
    }

    interface OnBtnClick {
        fun onConfirm()
    }

    init {
        val window = window
        window!!.requestFeature(Window.FEATURE_NO_TITLE)
        window.setBackgroundDrawable(ColorDrawable())
        setCancelable(false)
        view = LayoutInflater.from(context).inflate(R.layout.dlg_whatsnew, null)
        setContentView(view)
        tv1 = view.findViewById(R.id.tv1)
        tv1?.text= SkinResourcesUtils.getString("WiiM Mini is up to date")
        tv2 = view.findViewById(R.id.tv2)

        tv3 = view.findViewById(R.id.tv3)

        btnOk = view.findViewById(R.id.btn_ok)
        btnOk?.text= SkinResourcesUtils.getString("OK");
        tv1?.setTextColor(Color.parseColor("#F6A21E"))
        tv2?.setTextColor(GlobalUIConfig.color_gray)
        tv3?.setTextColor(GlobalUIConfig.color_normal)
        btnOk?.setTextColor(GlobalUIConfig.color_normal)
        btnOk?.setOnClickListener { view: View? ->
            dismiss()
        }

    }
    fun setMessage(verson:String,releasenote:String){
        var version_result=verson
        if(verson.contains("Linkplay.")){
            version_result= verson.replace("Linkplay.","V")
        }
        tv2?.text = version_result
        tv3?.text = releasenote
    }
}