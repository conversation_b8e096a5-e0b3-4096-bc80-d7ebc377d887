package com.harman.bean;

public class CommonEvent {
    public static final String EVENT_REFRESH_COMPOSE_STATUE = "refresh_compose_status";
    public static final String EVENT_BACK_TO_HOME_DEVICES_LIST = "back_to_home_device_list";
    public static final String EVENT_REFRESH_MUSIC_SERVICE = "refresh_music_service";
    public static final String EVENT_ENABLE_CHROMECAST = "enable_chromecast";
    public static final String EVENT_AMAZON_LOGIN = "amazon_login";
    private int value = -1;

    public CommonEvent(String name) {
        this.name = name;
    }

    public CommonEvent(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "CommonEvent{" +
                "value=" + value +
                ", name='" + name + '\'' +
                '}';
    }
}
