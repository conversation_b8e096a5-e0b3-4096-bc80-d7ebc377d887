package com.harman.multichannel

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogCalibrationTipBinding
import com.harman.BottomPopUpDialog

/**
 * Created by sky on 2024/7/8.
 */
class CalibrationTipDialog(
    context: Context,
    private val listener: com.harman.multichannel.IDialogEvent?
) : BottomPopUpDialog(context = context, anim = R.style.SlideDialogAnim) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val binding = DialogCalibrationTipBinding.inflate(LayoutInflater.from(context))
        binding.dialog = this@CalibrationTipDialog
        binding.lifecycleOwner = this
        setContentView(binding.root)
    }

    fun onCloseBtnClick() {
        listener?.onCloseBtnClick()
    }

    fun onContinueBtnClick() {
        listener?.onOKBtnClick()
    }
}



