package com.harman.multichannel

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.harman.bar.app.databinding.FragmentMultichannelRenameBinding
import com.harman.discover.util.Tools.hideSoftKeyboard
import com.harman.log.Logger
import com.skin.SkinResourcesUtils


/**
 * Created by sky on 2024/8/20.
 */
class MultichannelRenameFragment : com.harman.multichannel.BaseMultiChannelFragment() {

    private val viewModel: MultichannelViewModel by activityViewModels()

    private val _isTimeout = MutableLiveData<Boolean>()
    val isTimeout: LiveData<Boolean>
        get() = _isTimeout

    private val _image = MutableLiveData<Drawable?>()
    val image: LiveData<Drawable?>
        get() = _image

    private val _text = MutableLiveData<CharSequence?>()
    val text: LiveData<CharSequence?>
        get() = _text

    private lateinit var binding: FragmentMultichannelRenameBinding

    private val fragmentHelper = MultichannelFragmentHelper()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMultichannelRenameBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this
        binding.fragment = this
        binding.viewModel = viewModel

        return binding.root
    }


    fun onFeelThePowerClick() {
        Logger.d(TAG, " onFeelThePowerClick")
        val activity = multiChannelActivity ?: run {
            Logger.e(TAG, " context is missing")
            return
        }
        var goDevice = viewModel.findGO() ?: run {
            Logger.e(TAG, " return null by findGo")
            viewModel.entryPoint
        }
        activity.onFeelThePowerBtnClick(goDevice)
    }

    fun onDoneClick() {
        Logger.d(TAG, " onDoneClick")
        val activity = multiChannelActivity ?: return
        activity.gotoSystemControl(viewModel.findGO())
    }


    fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
        if (actionId == EditorInfo.IME_ACTION_DONE) {
            val newName = v?.text ?: return false
            Logger.d(TAG, "renameGroup newName = $newName")
            viewModel.renameGroup(newName.toString())
            v.clearFocus()
            (v as EditText).hideSoftKeyboard()
            return true


        }

        return false

    }


    //    val groupNameTextWatcher = object : TextWatcher {
//
//        private var lastName: String? = null
//
//        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
//            // no impl
//        }
//
//        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
//            // no impl
//        }
//
//        override fun afterTextChanged(editable: Editable?) {
//            val str = editable?.toString() ?: ""
//
//            val length = str.toByteArray().size
//            if (length > MAX_DEVICE_NAME_BYTE_LENGTH) {
//                binding.etName.setText(lastName)
//            } else {
//                lastName = str
//            }
//        }
//    }
    val onFocusChangeListener = OnFocusChangeListener { v, hasFocus ->
        if (hasFocus) {
            _image.value = SkinResourcesUtils.getDrawable("textcursordrawable")
        } else {
            _image.value = null
        }
    }

    val textWatcher = object : TextWatcher {

        private var lastName: String? = null

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            // no impl
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            // no impl
        }

        override fun afterTextChanged(s: Editable?) {
            if (s.overThreshold()) {
                val et = binding.etName

                et.setText(lastName)
                et.setSelection(lastName?.length ?: 0)
            } else {
                lastName = s?.toString()
            }
        }

        private fun Editable?.overThreshold(): Boolean {
            val str = this?.toString() ?: ""

            return str.toByteArray().size > MAX_DEVICE_NAME_BYTE_LENGTH
        }
    }

    val etFilters = arrayOfNulls<InputFilter>(1).also { array ->
        array[0] = InputFilter.LengthFilter(MAX_DEVICE_NAME_BYTE_LENGTH)
    }

    companion object {
        private const val TAG = "MultichannelRenameFragment"
        private const val MAX_DEVICE_NAME_BYTE_LENGTH = 26
    }
}