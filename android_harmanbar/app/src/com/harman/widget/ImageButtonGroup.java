package com.harman.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.harman.bar.app.R;

public class ImageButtonGroup extends LinearLayout implements View.OnClickListener {
    private ImageButton ib_deep;
    private ImageButton ib_punchy;
    private TextView tv_deep;
    private TextView tv_punchy;
    private int currentValue = 0;

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public ImageButtonGroup(Context context) {
        this(context, null);
    }

    public ImageButtonGroup(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        View contentView = LayoutInflater.from(context).inflate(R.layout.layout_image_button_group, this, true);
        ib_deep = contentView.findViewById(R.id.ib_deep);
        ib_deep.setOnClickListener(this);
        ib_punchy = contentView.findViewById(R.id.ib_punchy);
        ib_punchy.setOnClickListener(this);
        tv_deep = contentView.findViewById(R.id.tv_deep);
        tv_punchy = contentView.findViewById(R.id.tv_punchy);

//        onCheckedIndexChanged(0);
    }

    public void setCheckedValue(int value) {
        if (value == 1) {
            tv_deep.setEnabled(true);
            tv_punchy.setEnabled(false);
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_checked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_selected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_unselected);
        } else if (value == 2) {
            tv_deep.setEnabled(false);
            tv_punchy.setEnabled(true);
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_left_unchecked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_checked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_unselected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_selected);
        } else {
            tv_deep.setEnabled(false);
            tv_punchy.setEnabled(false);
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_left_unchecked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_unselected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_unselected);
        }
        currentValue = value;
    }

    public void onCheckedValueChanged(int value) {
        setCheckedValue(value);
        if (onItemClickListener != null) onItemClickListener.onClick(value);
    }

    public int getCurrentValue() {
        return currentValue;
    }

    public void disable() {
        ib_deep.setEnabled(false);
        ib_punchy.setEnabled(false);
        if (currentValue == 0) {
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_unselected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_unselected);
        } else if (currentValue == 1) {
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_disable);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_selected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_unselected);
        } else if (currentValue == 2) {
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_disable);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_unselected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_selected);
        }
    }

    public void enable() {
        ib_deep.setEnabled(true);
        ib_punchy.setEnabled(true);
        if (currentValue == 1) {
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_checked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_selected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_unselected);
        } else if (currentValue == 2) {
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_checked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_unselected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_selected);
        } else {
            ib_deep.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_punchy.setBackgroundResource(R.drawable.shape_default_source_right_unchecked);
            ib_deep.setImageResource(R.drawable.icon_bass_boost_deep_unselected);
            ib_punchy.setImageResource(R.drawable.icon_bass_boost_punchy_unselected);
        }
    }

    private void onAbleChange() {

    }

    @Override
    public void onClick(View v) {
        if (v == ib_deep) {
            onCheckedValueChanged(1);
        } else if (v == ib_punchy) {
            onCheckedValueChanged(2);
        }
    }

    public interface OnItemClickListener {
        void onClick(int value);
    }
}
