package com.harman.webview

import androidx.annotation.MainThread
import com.harman.discover.bean.OneDevice

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/10/22.
 *
 * Trans events from [IHybridViewModel] to [DeviceControlHybridActivity]
 */
interface IViewModelEvents {

    /**
     * For [OneDeviceReadyToConnectViewModel] update hybrid device control page to online style.
     */
    @MainThread
    fun refreshAsOnline(device: OneDevice) {}

}