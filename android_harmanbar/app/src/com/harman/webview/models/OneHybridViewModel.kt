package com.harman.webview.models


import android.app.Activity
import android.app.Dialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.graphics.Bitmap
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import com.harman.bar.app.R
import com.harman.calibration.CalibrationBaseActivity
import com.harman.calibration.CalibrationGuideDialog
import com.harman.calibration.IAudioCalibrationDialogEvent
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.AudioSync
import com.harman.command.one.bean.AudioSyncResp
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.CUSTOM_EQ_ID
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.EQListItem
import com.harman.command.one.bean.EQListPayload
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQPayload
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EnumCoulsonStatus
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetSmartBtnConfigRsp
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.Rear
import com.harman.command.one.bean.RearSpeakerVolume
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.TypeBassBoostEq
import com.harman.command.one.bean.TypeLEDLightPatternNumber
import com.harman.command.one.bean.TypeLEDLightShowSwitch
import com.harman.command.one.bean.TypeLedLightShowAllSwitch
import com.harman.command.one.bean.UltimateLight
import com.harman.command.one.bean.UltimateLightId
import com.harman.command.one.bean.UltimatePattern
import com.harman.command.one.bean.ValueResponse
import com.harman.command.one.bean.displayBattery
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.getAlexaCblLoggedWithTimeout
import com.harman.connect.getAlexaLwaLoggedWithTimeout
import com.harman.connect.getC4aPermissionStatusWithTimeout
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.session.OneBusiness
import com.harman.connect.syncCmd
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetFeatureSupportWithTimeout
import com.harman.connect.syncGetGeneralConfig
import com.harman.connect.syncGetGroupParameterWithTimeout
import com.harman.connect.syncGetLightColorPicker
import com.harman.control.controller.RemoteControllerActivity
import com.harman.discover.DeviceScanner
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.bean.deviceSupportAudioSync
import com.harman.discover.bean.trackSource
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
<<<<<<< HEAD
import com.harman.discover.util.Tools.getGroupMode
=======
>>>>>>> 3a4c8781d (HKSS5W-385 fix-issue: filter eq notification from device after send set eq command to avoid UI bounce)
import com.harman.discover.util.Tools.overThreshold
import com.harman.discover.util.Tools.roundPercentage
import com.harman.discover.util.Tools.safeResume
import com.harman.discover.util.Tools.toJSONObj
import com.harman.effectlab.EffectLabActivity
import com.harman.getFwCheckRspWithTimeout
import com.harman.isSub
import com.harman.log.Logger
import com.harman.miceffect.MicEffectActivityV2
import com.harman.moment.EnumMoment
import com.harman.moment.IOTAGuideDialogEvent
import com.harman.moment.MomentActivity
import com.harman.moment.MomentViewModel
import com.harman.moment.OTAGuideDialog
import com.harman.multichannel.MultichannelActivity
import com.harman.multichannel.SoundTuningActivity
import com.harman.multichannel.TriggerCastLedUtil
import com.harman.multichannel.UngroupDialog
import com.harman.multiroom.MultiRoomLearnMoreActivity
import com.harman.music.EnumInputSource
import com.harman.music.Tools
import com.harman.music.Tools.isUriConst
import com.harman.music.Tools.millsToSecs
import com.harman.music.Tools.musicServiceSource
import com.harman.music.mini.EnumMiniPlayerType
import com.harman.music.player.FullPlayerActivity
import com.harman.music.player.PlayerViewModel
import com.harman.music.player.isMediaPlayerSupport
import com.harman.music.service.EnumMusicServiceSource
import com.harman.music.service.IMusicServiceListListener
import com.harman.music.service.MusicServiceListDialog
import com.harman.music.service.MusicServiceProvider
import com.harman.music.service.MusicServiceSDKHelper
import com.harman.nightlistening.NightListeningActivity
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.oobe.wifi.WiFiOOBEDialogHelper
import com.harman.ota.one.OneOtaActivity
import com.harman.portalActivity
import com.harman.portalUrl
import com.harman.printf
import com.harman.product.info.ProductInfoActivity
import com.harman.product.rename.RenameDeviceActivity
import com.harman.product.setting.activity.BaseProductSettingsActivity
import com.harman.streaming.WiFiStreamingDetailActivity
import com.harman.streaming.google.GooglePortalHelper
import com.harman.streaming.roon.Campaign
import com.harman.streaming.roon.RoonReadyManager
import com.harman.supportFlexListening
import com.harman.supportMultiChanel
import com.harman.supportPersonalListeningMode
import com.harman.supportPureVoice
import com.harman.supportRearSpeaker
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.toBean
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.BeanMap.toSecondaryDeviceInfoList
import com.harman.webview.BleStereoSetupWifiTipsDialog
import com.harman.webview.CommonHybridActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.webview.DeviceInfo
import com.harman.webview.EQItem
import com.harman.webview.EnterMusicService
import com.harman.webview.EnumDeviceInfoStatus
import com.harman.webview.FlexListeningFeature
import com.harman.webview.GetCoulsonStatus
import com.harman.webview.GetEQ
import com.harman.webview.HybridTools.toScaledFloat
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.JSBassboostInfo
import com.harman.webview.JsLightInfo
import com.harman.webview.JsSoundbarFeature
import com.harman.webview.MomentStatus
import com.harman.webview.MusicServiceList
import com.harman.webview.NetworkInfo
import com.harman.webview.OTAStatus
import com.harman.webview.PlayInfo
import com.harman.webview.RoonReadyLaterTipsDialog
import com.harman.webview.RoonReadyStatus
import com.harman.webview.SetEQ
import com.harman.webview.toJsBassBoostInfo
import com.harman.widget.ColorPickerDialog
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.alarm.AlarmContextItem
import com.wifiaudio.model.albuminfo.AlbumMetadataUpdater
import com.wifiaudio.model.albuminfo.MessageAlbumObject
import com.wifiaudio.model.albuminfo.MessageAlbumType
import com.wifiaudio.model.local_music.MainItem
import com.wifiaudio.service.DlnaServiceProviderPool
import com.wifiaudio.utils.BackupQueueUtils
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.TopPanelActivity
import java.util.Observer
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONObject


/**
 * Created by gerrardzhang on 2024/4/10.
 *
 * @param launcher used to receive msg from [OneOtaActivity]
 */
class OneHybridViewModelFactory(
    private val device: OneDevice,
    private val launcher: ActivityResultLauncher<Intent>?,
    private val momentViewModel: MomentViewModel?,
    private val playerViewModel: PlayerViewModel<*>?
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OneHybridViewModel(
            device = device,
            launcher = launcher,
            momentViewModel = momentViewModel,
            playerViewModel = playerViewModel
        ) as T
    }
}

class OneHybridViewModel(
    val device: OneDevice,
    val launcher: ActivityResultLauncher<Intent>?,
    private val momentViewModel: MomentViewModel?,
    private val playerViewModel: PlayerViewModel<*>?
) : ViewModel(), IHybridViewModel<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem,
        OneDevice> {

    private var checkFwRsp: CheckFWResp? = null

    private var musicServiceListDialog: MusicServiceListDialog? = null
    private var audioCalibrationDialog: CalibrationGuideDialog? = null

    private val loginHelper = MusicServiceSDKHelper(TAG = TAG, viewModelScope = viewModelScope, ::device)

    private var uiState = UiState()
    private var coulsonStatus: EnumCoulsonStatus? = null

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    private val _networkCardVisible = MutableLiveData<Boolean>(false)
    val networkCardVisible: LiveData<Boolean>
        get() = _networkCardVisible.distinctUntilChanged()

    private var wifiOOBEDialogHelper: WiFiOOBEDialogHelper? = null

    private var mRoonReadyLaterTipsDialog: RoonReadyLaterTipsDialog? = null

    private val _musicId = MutableLiveData<String?>(device.smartBtnConfigExt?.config?.music?.musicId)
    val musicId: LiveData<String?>
        get() = _musicId.distinctUntilChanged()
    val eventStream = MutableSharedFlow<OHVMEvent>(extraBufferCapacity = 1)

    private val playerType: EnumMiniPlayerType
        get() = Tools.mapMiniPlayerType(
            version = momentViewModel?.momentVersion?.value,
            soundScapeId = momentViewModel?.activeSoundscape?.value?.id,
            momentPlayState = momentViewModel?.soundScapeV2State?.value,
            isMusicPlaying = device.isPlaying
        )

    private val _bottomPlayerVisibility = MutableLiveData(false)
    val bottomPlayerVisibility: LiveData<Boolean>
        get() = _bottomPlayerVisibility.distinctUntilChanged()

    /**
     * Used to filter [notifyEQList] and [notifyUserEQChanged] from device after
     * doing [setActiveEQ] or [setEQ] from app.
     */
    @Volatile
    private var lastSetEQTs = 0L

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)

        Logger.i(TAG, "onCreate() >>> device[${device.UUID}]")
        loginHelper.registerLoginListener(iMusicServiceLoginListener)
        device.registerDeviceListener(jblOneDeviceListener)
        device.getDeviceInfo()

        // for moment status UI refresh
        device.getSmartBtnConfig()
        // for featSupportExt.isMediaPlayerSupport
        device.getFeatureSupport()

        if (device.groupParameterExt == null) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                device.syncGetGroupParameterWithTimeout(TAG,3,3000)
            }

        }
        networkCardVisible.observe(owner) { visible ->
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyNetworkInfo(isVisible = visible)
                syncNotifyEQInfo()
            }
        }

        refreshNetworkCardViewStyle()

        DeviceScanner.registerObserver(observer = scanObserver)

        AlbumMetadataUpdater.me().addObserver(albumMetadataObserver)

        bindPlayInfoRelatedData(owner)
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)

        notifyDeviceInfo()
        updateJsSoundbarFeature(device.rears)

        handleSecondaryDeviceInfos()
        refreshMoment(activeSoundscapeId = null)

        // for c4a permission status (Google VA)
        device.getC4aPermissionStatus()

        checkOtaStatus()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)

        musicServiceListDialog?.dismiss()
        audioCalibrationDialog?.dismiss()
        mUngroupDialog?.dismiss()
        mRoonReadyLaterTipsDialog?.dismiss()

        loginHelper.unregisterLoginListener(iMusicServiceLoginListener)

        device.unregisterDeviceListener(jblOneDeviceListener)

        DeviceScanner.unregisterObserver(observer = scanObserver)

        AlbumMetadataUpdater.me().deleteObserver(albumMetadataObserver)
    }

    @MainThread
    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: JsCallback
    ) {
        super.onCustomEvent(activity, eventName, data, callback)

        Logger.d(TAG, "onCustomEvent() >>> event[$eventName] ${data?.toString()}")
        when (eventName) {
            Events.PLAY_FUNCTION -> {
                handlePlayFunction(device = device, data = data, callback = callback)
            }

            Events.VOLUME_CHANGE -> {
                handleVolumeChange(device = device, data = data, callback = callback)
            }

            Events.GET_EQ -> {
                handleGetEQ(device = device, callback = callback)
            }

            Events.GET_COULSON_STATUS -> {
                handleGetCoulsonStatus(device = device, callback = callback)
            }

            Events.ENTER_COULSON -> {
                handleEnterCoulson(activity = activity, device = device, callback = callback)
            }

            Events.SET_EQ -> {
                handleSetEQ(device = device, data = data, callback = callback)
            }

            Events.GET_PLAY_INFO -> {
                handleGetPlayInfo(activity = activity, device = device, callback = callback)
            }

            Events.GET_OTA_STATUS -> {
                handleGetOtaStatus(device = device, callback = callback)
            }

            Events.ENTER_OTA -> {
                handleEnterOTA(activity = activity, device = device, callback = callback)
            }

            Events.EFFECT_LAB -> {
                handleEffectLabInfo(activity = activity, device = device, callback = callback)
            }

            Events.ENTER_MUSIC_SERVICE -> {
                handleEnterMusicService(activity = activity, data = data, device = device, callback = callback)
            }

            Events.GET_MUSIC_SERVICE -> {
                handleGetMusicService(device = device, callback = callback)
            }

            Events.ENTER_PRODUCT_INFO -> {
                handleEnterProductInfo(activity = activity, device = device, callback = callback)
            }

            Events.ENTER_MOMENT -> {
                handleEnterMoment(activity = activity, device = device, callback = callback)
            }

            Events.ENTER_PRODUCT_SETTINGS -> {
                handleEnterProductSettings(activity = activity, device = device, callback = callback)
            }

            Events.GET_DEVICE_INFO -> {
                notifyDeviceInfo()
                handleSecondaryDeviceInfos()
            }

            Events.ENTER_CALIBRATION -> {
                handleEnterCalibration(activity = activity, callback = callback)
            }

            Events.ENTER_REMOTE_CONTROL -> {
                handleEnterRemoteControl(activity = activity, callback = callback)
            }

            Events.ENTER_RENAME -> {
                handleEnterRename(activity = activity, callback = callback)
            }

            Events.ENTER_HOME_THEATRE, Events.ENTER_CREATE_A_SYSTEM, Events.CONNECT_TO_SOUNDBAR -> {
                handleEnterHomeTheatre(activity = activity, callback = callback)
            }

            Events.ENTER_WIFI_STREAMING -> {
                handleEnterWifiStreaming(activity = activity, callback = callback)
            }

            Events.ENTER_MIC_EFFECTS -> {
                handleEnterMicEffects(activity = activity, callback = callback)
            }

            Events.ENTER_TOP_PANNEL_SETTINGS -> {
                handleEnterTopPannelSettings(activity = activity, callback = callback)
            }

            Events.ENTER_SETUP_NETWORK -> {
                handleEnterSetupNetwork(activity = activity, callback = callback)
            }

            Events.GET_NETWORK_INFO -> {
                notifyNetworkInfo(isVisible = networkCardVisible.value ?: false)
            }

            Events.ENTER_COLOR_PICKER -> {
                handleEnterColorPicker(activity = activity, callback = callback)
            }

            Events.ENTER_MULTI_ROOM -> {
                handleEnterMultiRoom(activity = activity, callback = callback)
            }

            Events.GET_BASSBOOST_INFO -> {
                handleGetBassBoostInfo(callback)
            }

            Events.SET_BASSBOOST_INFO -> {
                handleSetBassBoostInfo(callback, data!!)
            }

            Events.UNGROUP -> {
                handleUngroup(activity = activity)
            }

            Events.ENTER_SOUND_TUNING -> {
                handleEnterSoundTuning(activity = activity, callback = callback)
            }

            Events.GET_ROON_READY_STATUS -> {
                handleGetRoonReadyStatus(activity = activity, callback = callback)
            }

            Events.GET_MOMENT_STATUS -> {
                handleGetMomentStatus(callback = callback)
            }

            Events.ENTER_QSG -> {
                val pId = data?.get("pId")?.asString
                handleEnterQSG(activity = activity, callback = callback, pId)
            }

            Events.ENTER_PRODUCT_SUPPORT -> {
                val pId = data?.get("pId")?.asString
                handleEnterProductSupport(activity = activity, callback = callback, pId)
            }

            Events.SET_SOUNDBAR_FEATURE -> {
                handleSetSoundbarFeature(data, callback)
            }

            Events.GET_SOUNDBAR_FEATURE -> {
                handleGetSoundbarFeature(callback = callback)
            }

            Events.GET_BROADCASTING_INFO -> {
                handleGetFlexListeningFeature(callback = callback)
            }

            Events.SET_BROADCASTING_INFO -> {
                handleSetFlexListeningFeature(data, callback)
            }

            Events.GET_LIGHT_INFO -> {
                handleGetLightInfo(callback)
            }

            Events.SET_LIGHT_CONTROL -> {
                handleSetLightControl(data, callback)
            }

            Events.ENTER_PERSONAL_LISTENING_MODE -> {
                handleEnterPersonalListeningMode(activity, callback)
            }

            Events.COPY_COUPON_CODE -> {
                handleCopyCouponCode(activity = activity, callback = callback)
            }

            Events.TO_ROON_READY_WEB -> {
                handleToRoonReadyWeb(activity = activity, callback = callback)
            }

            Events.SHOW_ROON_READY_LATER_TIPS -> {
                showRoonReadyLaterTipsDialog(activity)
            }

            Events.CLICK_ROON_READY_CARD -> {
                handleClickRoonReadyCard()
            }

            Events.ENABLE_GOOGLE_CAST -> {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    GooglePortalHelper.portalCastOrVA(
                        device = device,
                        activity = activity,
                        logTag = TAG
                    )
                }
            }

            Events.SHOW_PRIMARY_GREEN_LED -> {
                handleGreenLED(device, true)
            }

            Events.HIDE_PRIMARY_GREEN_LED -> {
                handleGreenLED(device, false)
            }

            Events.ENTER_FULL_PLAYER -> {
                handleEnterFullPlayer(device = device, activity = activity, callback = callback)
            }
        }
    }

    private fun handleGreenLED(device: OneDevice, showLED: Boolean) {
        if (showLED) {
            TriggerCastLedUtil.start()
            TriggerCastLedUtil.add(device)
        } else {
            TriggerCastLedUtil.stop()
        }

    }

    var primaryGreenLEDJob: Job? = null
    private fun handlePrimaryGreenLED(device: OneDevice, showLED: Boolean) {
        if (showLED) {
            if (primaryGreenLEDJob == null) {
                primaryGreenLEDJob = viewModelScope.launch {
                    repeat(Int.MAX_VALUE) {
                        device.triggerCastLED()
                        delay(3000)
                    }
                }
            }
        } else {
            primaryGreenLEDJob?.cancel()
            primaryGreenLEDJob = null
        }

    }

    private fun handleClickRoonReadyCard() {
        viewModelScope.launch {
            RoonReadyManager.hideRoonReadyCard(device.UUID, true)
        }
    }


    private fun showRoonReadyLaterTipsDialog(activity: CommonHybridActivity) {
        mRoonReadyLaterTipsDialog = RoonReadyLaterTipsDialog(activity).apply {
            show()
        }
    }

    private fun handleEnterPersonalListeningMode(activity: CommonHybridActivity, callback: JsCallback) {
        if (activity.portalActivity(target = NightListeningActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }

    }

    private fun handleSetLightControl(data: JsonObject?, callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                data.toBean<JsLightInfo>()!!.run {
                    enable?.also {
                        device.setGeneralConfig(GeneralConfig(TypeLEDLightShowSwitch, if (it) 1 else 0))
                    }
                    patternId?.also {
                        device.setGeneralConfig(
                            GeneralConfig(
                                TypeLEDLightPatternNumber,
                                JsLightInfo.JsPatternId.mapByKey(it)?.uPatter?.value ?: 0
                            )
                        )
                    }
                    lightToken?.also {
                        device.setGeneralConfig(
                            GeneralConfig(
                                JsLightInfo.JsLightTokenId.fromId(it.id!!)!!.uLightTokenId!!.tag,
                                if (it.state == true) 1 else 0,
                            )
                        )
                    }
                    color?.also {
                        device.setColorPicker(it.toColorPicker()!!)
                    }
                }
                callback.invoke("0", null, null)
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", it.toString(), null)
            }
        }
    }

    private fun handleGetLightInfo(callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                //fetch color picker
                val cp = device.syncGetLightColorPicker()!!
                //light control switch
                val lightControlSwitch = device.syncCmd<ValueResponse>(
                    EnumCommandMapping.GET_GENERAL_CONFIG,
                    paramsJsonString = GsonUtil.parseBeanToJson(GeneralConfigType(TypeLEDLightShowSwitch))
                )!!.toIntValue()!!
                //current pattern
                val patten = device.syncCmd<ValueResponse>(
                    EnumCommandMapping.GET_GENERAL_CONFIG,
                    paramsJsonString = GsonUtil.parseBeanToJson(GeneralConfigType(TypeLEDLightPatternNumber))
                )!!.toIntValue()!!.let {
                    UltimatePattern.fromValue(it)!!
                }
                //all lights switch
                val allLightSwitch = device.syncCmd<MutableMap<String, Int>>(
                    EnumCommandMapping.GET_GENERAL_CONFIG,
                    paramsJsonString = GsonUtil.parseBeanToJson(GeneralConfigType(TypeLedLightShowAllSwitch))
                )!!
                val am = allLightSwitch.apply {
                    //Removes elements that aren't lighting switch
                    keys.toSet().forEach { key ->
                        UltimateLightId.entries.find { it.tag == key } ?: run {
                            <EMAIL>(key)
                        }
                    }
                }.mapKeys {
                    UltimateLightId.fromTag(it.key)!!
                }
                val ul = UltimateLight(lightControlSwitch, patten, am)
                callback.invoke("0", null, JsLightInfo.fromUltimateLight(ul, cp).toJSONObj())
                uiState = uiState.copy(colorPicker = cp, ultimateLight = ul)
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", it.toString(), null)
            }
        }
    }

    private fun handleSetSoundbarFeature(data: JsonObject?, callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                val setBean = data.toBean<JsSoundbarFeature.SetBean>()!!

                setBean.audioSync?.also {
                    val audioSync = uiState.jsSoundbarFeature.audioSync?.copy(it,true)
                    uiState = uiState.copy(jsSoundbarFeature = uiState.jsSoundbarFeature.copy(audioSync =audioSync ))
                    device.syncCmd<Unit>(
                        EnumCommandMapping.SET_AUDIO_SYNC,
                        paramsJsonString = GsonUtil.parseBeanToJson(AudioSync(it.toString()))
                    )
                }
                setBean.rearSpeakerLevel?.also {
                    device.syncCmd<Unit>(
                        EnumCommandMapping.SET_REAR_SPEAKER_VOLUME,
                        paramsJsonString = GsonUtil.parseBeanToJson(RearSpeakerVolume(volume = it.toString()))
                    )
                }
                setBean.pureVoiceStatus?.also {
                    device.setProSetting(
                        pureVoice = if (it) ProdSettingResponse.ON else ProdSettingResponse.OFF
                    )
                }
                callback.invoke("0", null, null)
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", it.message, null)
            }
        }
    }

    private fun handleGetSoundbarFeature(callback: JsCallback) {
        viewModelScope.launch {
            val audioSyncRet = if (device.deviceSupportAudioSync()) {
                device.syncCmd<AudioSyncResp>(EnumCommandMapping.GET_AUDIO_SYNC)
            } else null
            val rearSpeakerVolumeRet = if (device.supportRearSpeaker()) {
                device.syncCmd<RearSpeakerVolumeResponse>(EnumCommandMapping.GET_REAR_SPEAKER_VOLUME)
            } else null
            val prodSetting = if (device.supportPureVoice()) {
                device.getProSetting()
            } else null

            val supportPersonalListeningMode = device.supportPersonalListeningMode()
            val supportMultiChannel = device.supportMultiChanel()
            val pureVoiceStatus = prodSetting?.isPureVoiceOn

            callback.invoke(
                "0",
                null,
                JsSoundbarFeature(
                    audioSyncRet?.let { JsSoundbarFeature.JsAudioSync(it.intValue(), true) },
                    rearSpeakerVolumeRet?.let { JsSoundbarFeature.JsRearSpeaker(it.volume?.toInt(), it.isDetached) },
                    supportPersonalListeningMode,
                    supportMultiChannel,
                    supportBroadcasting = device.supportFlexListening(),
                    supportSoundTuning = device.groupInfoExt?.groupInfo?.groupInfo?.notNeedCalibration() == false,
                    pureVoiceStatus = pureVoiceStatus
                ).apply {
                    Logger.d(TAG, "JsSoundbarFeature: $this")
                    uiState = uiState.copy(jsSoundbarFeature = this)
                }.toJSONObj(),
            )
        }
    }

    private fun handleSetFlexListeningFeature(data: JsonObject?, callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                val setBean = data.toBean<FlexListeningFeature>()
                setBean?.also {
                    device.setProSetting(
                        flexListening = if (true != it.enabled
                            && ProdSettingResponse.FLEX_LISTENING_MONO.toInt(10) != it.outputType
                            && ProdSettingResponse.FLEX_LISTENING_STEREO.toInt(10) != it.outputType) {
                            ProdSettingResponse.OFF
                        } else {
                            it.outputType?.toString() ?: ProdSettingResponse.FLEX_LISTENING_MONO
                        }
                    )
                }
                callback.invoke("0", null, null)
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", it.message, null)
            }
        }
    }

    private fun handleGetFlexListeningFeature(callback: JsCallback) {
        viewModelScope.launch {
            val prodSetting = if (device.supportPureVoice()) {
                device.getProSetting()
            } else null

            callback.invoke(
                "0",
                null,
                FlexListeningFeature(
                    enabled = true == prodSetting?.isFlexListeningMono || true == prodSetting?.isFlexListeningStereo,
                    outputType = if (true == prodSetting?.isFlexListeningMono) {
                        1
                    } else if (true == prodSetting?.isFlexListeningStereo) {
                        2
                    } else {
                        0
                    }
                ).apply {
                    Logger.d(TAG, "FlexListeningFeature: $this")
                }.toJSONObj(),
            )
        }
    }

    private var mUngroupDialog: Dialog? = null
    private fun handleUngroup(activity: CommonHybridActivity) {
        device?.let {
            mUngroupDialog = UngroupDialog(device = it, activity = activity, oneHybridViewModel = OneHybridViewModel@this).apply {
                show()
            }
        }
    }

    private var mBleStereoDialog: Dialog? = null
    private fun showSetupWifiForBleStereo(activity: CommonHybridActivity) {
        mBleStereoDialog = BleStereoSetupWifiTipsDialog(context = activity).apply {
            show()
        }

    }

    private fun handleEnterSoundTuning(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "handleEnterSoundTuning()")
        if (activity.portalActivity(target = SoundTuningActivity::class.java, device = device)) {
//            activity.finishAfterTransition()
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }

    }

    var campaign: Campaign? = null
    private fun handleGetRoonReadyStatus(activity: CommonHybridActivity, callback: JsCallback) {
        val roonReadySupport = device.featSupportExt?.featSupport?.isRoonReadySupport() == true
        Logger.d(TAG, "handleGetRoonReadyStatus() roonReady support $roonReadySupport")
        Logger.d(TAG, "handleGetRoonReadyStatus() featSupport ${device.featSupportExt?.featSupport}")
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val hasClickRoonReadyCard = RoonReadyManager.hasClickRoonReadyCard(device.UUID)
            if (!hasClickRoonReadyCard && roonReadySupport) {
                campaign = RoonReadyManager.getCampaignFromCache(device.UUID)
                var code = campaign?.data?.deviceCouponCode
                if (!code.isNullOrBlank()) {
                    val status = RoonReadyStatus(true, code)
                    callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(status)))
                } else {
                    campaign = RoonReadyManager.getRemoteCouponCampaign(device)
                    code = campaign?.data?.deviceCouponCode
                    val status = RoonReadyStatus(!code.isNullOrBlank(), code)
                    callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(status)))
                }
            }

        }
    }

    private fun handleCopyCouponCode(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "onCustomEvent() >>> handleCopyCouponCode")
        val redeemCode = campaign?.data?.deviceCouponCode
        if (redeemCode.isNullOrBlank()) {
            return
        }
        val clipboard = activity.getSystemService(FragmentActivity.CLIPBOARD_SERVICE) as? ClipboardManager ?: return
        clipboard.setPrimaryClip(ClipData.newPlainText("Copied Text", redeemCode))
    }

    private fun handleToRoonReadyWeb(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "onCustomEvent() >>> handleToRoonReadyWeb")
        val redeemCode = campaign?.data?.deviceCouponCode
        if (redeemCode.isNullOrBlank()) {
            return
        }
        activity.portalUrl(
            config = AppConfigurationUtils.getRoonRedeemFormat()?.format(redeemCode),
            default = RoonReadyManager.DEFAULT_ROON_READY_REDEEM_URL.format(redeemCode)
        )
    }

    private fun handleEnterQSG(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterQSG()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlQsg ?: return
        Logger.d(TAG, "launchQsg pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("jbl_Quick_Start_Guide"))
        callback.invoke("0", null, null)

    }

    private fun handleEnterProductSupport(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterProductSupport()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlSupport ?: return
        Logger.d(TAG, "handleEnterProductSupport pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("harmanbar_jbl_Product_Support"))
        callback.invoke("0", null, null)

    }

    private fun loadingUrlInAppWebView(activity: CommonHybridActivity, linkUrl: String, title: String) {
        Logger.d(TAG, "loadingUrlInAppWebView Url: $linkUrl")
        QuickStartGuideWebActivity.launchQuickStartGuideWebActivity(activity, linkUrl, title)
    }

    private fun handleSetBassBoostInfo(callback: JsCallback, data: JsonObject) {
        viewModelScope.launch {
            runCatching {
                GsonUtil.parseJsonToBean(data.toString(), JSBassboostInfo::class.java).run {
                    val config = toGeneralConfig()
                    device.setGeneralConfig(config)
                    callback.invoke("0", null, null)
                    if (isOn && null == selectedIndex) {
                        notificationInvoker?.notifyObj(
                            Events.NOTIFY_BASSBOOST_INFO,
                            config.intValue().toJsBassBoostInfo().toJSONObj()
                        )
                    }
                }
            }.onFailure {
                callback.invoke("-1", it.message, null)
            }
        }
    }

    private fun handleGetBassBoostInfo(callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                val value = device.syncGetGeneralConfig(GeneralConfigType(TypeBassBoostEq))!!.toIntValue()!!
                callback.invoke("0", null, value.toJsBassBoostInfo().toJSONObj())
            }.onFailure {
                callback.invoke("-1", it.message, null)
            }
        }
    }

    private fun handleEnterMultiRoom(activity: CommonHybridActivity, callback: JsCallback) {
        if (activity.portalActivity(target = MultiRoomLearnMoreActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    private fun handleEnterColorPicker(activity: CommonHybridActivity, callback: JsCallback) {
        ColorPickerDialog(
            activity,
            onColorPick = {
                ColorPicker(0, it.red, it.green, it.blue).run {
                    device.setColorPicker(this)
                    uiState = uiState.copy(colorPicker = this)
                    notificationInvoker?.notifyObj(
                        Events.NOTIFY_LIGHT_INFO,
                        JsLightInfo.fromUltimateLight(uiState.ultimateLight, this).toJSONObj(),
                    )
                }
            },
            uiState.colorPicker.getColor(),
        ).show()
    }

    private fun handleEnterTopPannelSettings(activity: CommonHybridActivity, callback: JsCallback) {
        activity.portalActivity(target = TopPanelActivity::class.java, device = device)
        callback.invoke("0", null, null)
    }

    private fun handleEnterMicEffects(activity: CommonHybridActivity, callback: JsCallback) {
        if (activity.portalActivity(target = MicEffectActivityV2::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    private fun handleGetPlayInfo(
        activity: FragmentActivity,
        device: OneDevice,
        callback: JsCallback
    ) {
        callback.invoke("0", null, null)
        notifyPlayInfo(device = device, force = true)
    }

     fun notifyDeviceInfo() {
        notifyDeviceInfo(
            info = device.toDeviceInfo(
                status = if (device.isWiFiOnline) EnumDeviceInfoStatus.WIFI_CONNECTED else EnumDeviceInfoStatus.BLE_CONNECTED,
                groupInfo = device.groupInfoExt?.groupInfo,
            ),
        )
    }

    private fun handleSecondaryDeviceInfos() {
        notifySecondaryDeviceInfos(device.toSecondaryDeviceInfoList())
    }

    private fun handlePlayFunction(
        device: OneDevice,
        data: JsonObject?,
        callback: JsCallback
    ) {
        when (data?.get("function")?.asString) {
            Events.PlayFunctionEvents.PLAY_OR_PAUSE -> {
                handlePlayOrPauseMusic(device = device, callback = callback)
            }

            Events.PlayFunctionEvents.PREVIOUS -> {
                handlePrevMusic(device = device, callback = callback)
            }

            Events.PlayFunctionEvents.NEXT -> {
                handleNextMusic(device = device, callback = callback)
            }
        }
    }

    private fun handlePlayOrPauseMusic(
        device: OneDevice,
        callback: JsCallback
    ) {
        when (playerType) {
            EnumMiniPlayerType.MOMENT -> handleMomentPlayOrPause(device = device, callback = callback)
            EnumMiniPlayerType.PLAYER -> handlePlayerPlayOrPause(device = device, callback = callback)
            else -> handlePlayerPlayOrPause(device = device, callback = callback)
        }
    }

    private fun handleMomentPlayOrPause(device: OneDevice, callback: JsCallback) {
        momentViewModel?.cancelSoundScapeV2()
        Logger.i(TAG, "handlePlayerPlayOrPause() >>> cancelSoundScapeV2")
        callback.invoke("0", null, null)
    }

    private fun handlePlayerPlayOrPause(device: OneDevice, callback: JsCallback) {
        if (device.isPlaying) {
            Logger.i(TAG, "handlePlayerPlayOrPause() >>> pause")
            device.pauseMusic()
        } else {
            Logger.i(TAG, "handlePlayerPlayOrPause() >>> play")
            device.playMusic()
        }

        callback.invoke("0", null, null)
    }

    private fun handlePrevMusic(
        device: OneDevice,
        callback: JsCallback
    ) {
        Logger.i(TAG, "handlePrevMusic() >>> process")
        device.prevMusic()
        callback.invoke("0", null, null)
    }

    private fun handleNextMusic(
        device: OneDevice,
        callback: JsCallback
    ) {
        Logger.i(TAG, "handleNextMusic() >>> process")
        device.nextMusic()
        callback.invoke("0", null, null)
    }

    private fun handleVolumeChange(
        device: OneDevice,
        data: JsonObject?,
        callback: JsCallback,
    ) {
        val volume = data?.get("volume")?.asInt ?: return
        Logger.i(TAG, "handleVolumeChange() >>> volume[$volume]")

        device.setRemoteVolume(volume.roundPercentage())
        callback.invoke("0", null, null)
    }

    @MainThread
    private suspend fun handleGetEQ(
        device: OneDevice,
        callback: JsCallback
    ) {
        val eqInfo = syncBuildGetEQ(device = device) ?: return
        callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(eqInfo)))
        notifyEQInfo(eqInfo)
    }

    @MainThread
    private suspend fun handleGetGroupParameter(
        device: OneDevice,
        callback: JsCallback
    ) {
        val featSupport = getFeatureSupport(device)
        Logger.d(TAG, "handleGetEQInfo() >>> featSupport:\n${GsonUtil.parseBeanToJson(featSupport)}")
//        if (true != featSupport.userEQ?.isPresetSupport) {
        if (device.support7BandsEq) {
            onGet7BandsEq(device = device, callback = callback)
        } else {
            onGetEq(device = device, callback = callback)
        }
    }

    @MainThread
    private suspend fun handleGetCoulsonStatus(
        device: OneDevice,
        callback: JsCallback
    ) {
        val featSupport = getFeatureSupport(device)
        Logger.d(TAG, "handleGetCoulsonStatus() >>> featSupport:\n${GsonUtil.parseBeanToJson(featSupport)}")

        val alexaStatus = if (featSupport.isLwaSupport()) {
            device.getAlexaLwaLoggedWithTimeout(logTag = TAG)
        } else {
            device.getAlexaCblLoggedWithTimeout(logTag = TAG)
        }
        val googleStatus = device.getC4aPermissionStatusWithTimeout(logTag = TAG)
        coulsonStatus = EnumCoulsonStatus.fromValue(alexaStatus, googleStatus)
        Logger.d(
            TAG,
            "handleGetCoulsonStatus() >>> featSupport : alexaStatus : $alexaStatus googleStatus : $googleStatus coulsonStatus : ${coulsonStatus?.value} ${
                GsonUtil.parseBeanToJson(featSupport)
            } "
        )
        val info = GetCoulsonStatus(
            status = coulsonStatus?.value
        )

        callback.invoke(
            "0",
            null,
            JSONObject(GsonUtil.parseBeanToJson(info))
        )
    }

    @MainThread
    private fun handleEnterCoulson(
        activity: Activity,
        device: OneDevice,
        callback: JsCallback
    ) {
        Logger.d(TAG, "handleEnterCoulson()")
        coulsonStatus?.also {
            eventStream.tryEmit(OHVMEvent.EnterCoulson(it, device))
        }
        callback.invoke("0", null, null)
    }

    @MainThread
    private suspend fun handleGetOtaStatus(device: OneDevice, callback: JsCallback) {
        val otaStatus = fetchOTAStatus(device = device)
        if (null != otaStatus) {
            callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(otaStatus)))
        } else {
            callback.invoke("-1", null, null)
        }

        otaStatus?.let {
            super.notifyOtaStatus(status = otaStatus)
        }
    }

    private fun handleEnterProductInfo(
        activity: Activity,
        device: OneDevice,
        callback: JsCallback
    ) {
        Logger.d(TAG, "handleEnterProductInfo()")
        ProductInfoActivity.launchProductInfoPage(activity, device)
        callback.invoke("0", null, null)
    }

    private fun handleEnterOTA(
        activity: Activity,
        device: OneDevice,
        callback: JsCallback
    ) {
        val rsp = checkFwRsp ?: run {
            Logger.e(TAG, "handleEnterOTA() >>> missing checkFwRsp")
            callback.invoke("-1", null, null)
            return
        }

        val success = OneOtaActivity.launchOtaPage(
            launcher = launcher,
            activity = activity,
            device = device,
            checkFwRsp = rsp
        )

        callback.invoke(if (success) "0" else "-1", null, null)
    }

    private var otaGuideDialog: OTAGuideDialog? = null

    private fun handleEnterMoment(
        activity: Activity,
        device: OneDevice,
        callback: JsCallback
    ) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            if (!supportMomentFirmware(device)) {
                // show block dialog and guide to OTA page.
                otaGuideDialog?.dismiss()
                otaGuideDialog = OTAGuideDialog(
                    context = activity,
                    device = device,
                    checkFwRsp = checkFwRsp,
                    listener = OTAGuideDialogListener(activity = activity)
                ).apply {
                    show()
                }
                return@launch
            }

            val success = MomentActivity.portal(context = activity, device = device)
            callback.invoke(if (success) "0" else "-1", null, null)
        }
    }

    private inner class OTAGuideDialogListener(
        private val activity: Activity
    ) : IOTAGuideDialogEvent {
        override fun onContinueBtnClick(callbackRsp: CheckFWResp?) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                if (null == checkFwRsp && null != callbackRsp) {
                    checkFwRsp = callbackRsp
                }

                val rsp = checkFwRsp
                    ?: device.getFwCheckRspWithTimeout(logTag = TAG)?.also {
                        checkFwRsp = it
                    }

                rsp ?: run {
                    Logger.e(TAG, "onContinueBtnClick() >>> fail to get CheckFWResp")
                    return@launch
                }

                Logger.i(TAG, "onContinueBtnClick() >>> ${checkFwRsp?.printf()}")

                OneOtaActivity.launchOtaPage(
                    launcher = launcher,
                    activity = activity,
                    device = device,
                    checkFwRsp = rsp
                )
            }
        }
    }

    private suspend fun supportMomentFirmware(device: OneDevice): Boolean {
        if (true == device.featSupportExt?.featSupport?.isSoundScapeSupport()) {
            Logger.d(TAG, "supportMomentFirmware() >>> support soundscape by cached feature support.")
            return true
        }

        if (!device.isWiFiOnline && !device.syncGattConnectWithTimeout(context = WAApplication.me)) {
            Logger.e(TAG, "supportMomentFirmware() >>> fail to establish both WiFi and Gatt session with device[${device.UUID}]")
            return false
        }

        Logger.d(TAG, "supportMomentFirmware() >>> start to fetch featSupport")
        val featSupport = withContext(DISPATCHER_DEFAULT) {
            device.syncGetFeatureSupportWithTimeout(logTag = TAG)
        }

        featSupport ?: run {
            Logger.w(TAG, "isUnSupportMomentFirmware() >>> can't get feat support. regard as dont support")
            return false
        }

        Logger.d(TAG, "supportMomentFirmware() >>> ${featSupport.soundscape}")
        return featSupport.isSoundScapeSupport()
    }

    private suspend fun onGetEq(
        device: OneDevice,
        callback: JsCallback
    ) {
        val eqRsp = getEQ(device = device)
        Logger.d(TAG, "onNonPresetEQDevice() >>> eqRsp:\n${GsonUtil.parseBeanToJson(eqRsp)}")
        val info = GetEQ(
            isEnable = eqRsp.success(),
            currentEQId = eqRsp.eqSetting?.eqID,
            customEQId = eqRsp.eqSetting?.eqID,
            eqList = eqRsp.eqSetting.mapToEQList()
        )

        callback.invoke(
            eqRsp.errorCode ?: "-1",
            null,
            JSONObject(GsonUtil.parseBeanToJson(info))
        )
    }

    private fun EQSetting?.mapToEQList(): List<EQItem>? {
        val eqSetting = this ?: return null

        val eqItem = EQItem(
            eqName = eqSetting.eqName,
            eqId = eqSetting.eqID,
            fs = eqSetting.eqPayload?.fs?.map { it.toInt() },
            gain = eqSetting.eqPayload?.gain?.map { it.toDouble() },
            max = 6.0,
            min = -6.0
        )

        return listOf(eqItem)
    }

    private suspend fun onGet7BandsEq(
        device: OneDevice,
        callback: JsCallback
    ) {
        val eqList = getEQList(device)
        Logger.d(TAG, "onGet7BandsEq() >>> eqList:\n${GsonUtil.parseBeanToJson(eqList)}")

        val info = GetEQ(
            isEnable = eqList.success(),
            currentEQId = eqList.activeEQID,
            customEQId = CUSTOM_EQ_ID,
            eqList = eqList.eqList.mapToEQList()?.filter {
                if (!device.supportPresetEq) {
                    it.eqId == CUSTOM_EQ_ID
                } else true
            }
        )

        callback.invoke(
            eqList.errorCode ?: "-1",
            null,
            JSONObject(GsonUtil.parseBeanToJson(info))
        )
    }

    private fun List<EQListItem>?.mapToEQList(): List<EQItem>? {
        val eqListItem = this ?: return null

        return eqListItem.map { item ->
            EQItem(
                eqName = item.eqName,
                eqId = item.eqID,
                fs = item.eqListPayload?.fs?.map { it.toInt() },
                gain = item.eqListPayload?.gain?.map { it.toDouble() },
                max = 6.0,
                min = -6.0
            )
        }
    }

    @MainThread
    private fun handleSetEQ(
        device: OneDevice,
        data: JsonObject?,
        callback: JsCallback
    ) {
        lastSetEQTs = System.currentTimeMillis()

        val setEQ = GsonUtil.parseJsonToBean(data?.toString(), SetEQ::class.java) ?: run {
            Logger.w(TAG, "handleSetEQ() >>> fail to parse data as SetEQ:${data?.toString()}")
            callback.invoke("-1", "fail to parse set eq bean", null)
            return
        }

        if (device.support7BandsEq) {
            handleSet7BandsEQ(device = device, setEQ = setEQ, callback = callback)
        } else {
            handleSetEQ(device = device, setEQ = setEQ, callback = callback)
        }
    }

    @MainThread
    private fun handleSetEQ(device: OneDevice, setEQ: SetEQ, callback: JsCallback) {
        val settings = retrieveEqSetting(device = device, setEQ = setEQ, callback = callback) ?: run {
            callback.invoke("-1", "fail to retrieve set eq request", null)
            return
        }
        // send async and ignore result.
        device.setEQ(eq = settings)
        device.updateEQSettings(settings)
        callback.invoke("0", "set eq success", null)
    }

    @MainThread
    private fun handleSet7BandsEQ(device: OneDevice, setEQ: SetEQ, callback: JsCallback) {
        val request = retrieve7BandsEQRequest(device = device, setEQ = setEQ, callback = callback) ?: run{
            callback.invoke("-1", "fail to retrieve set active eq request", null)
            return
        }

        // send async and ignore result.
        device.setActiveEQ(request)
        device.updateActiveEQ(request)
        callback.invoke("0", "set preset eq success", null)
    }

    private fun retrieveEqSetting(
        device: OneDevice,
        setEQ: SetEQ,
        callback: JsCallback
    ): EQSetting? {
        val eqSettings = device.eqExt?.data?.eqSetting ?: run {
            Logger.w(TAG, "retrieveEqSetting() >>> empty eq settings from device instance")
            callback.invoke("-1", "eq list is empty", null)
            return null
        }

        return EQSetting(
            eqName = eqSettings.eqName,
            eqID = eqSettings.eqID,
            eqStatus = eqSettings.eqStatus,
            band = eqSettings.band,
            eqPayload = EQPayload(
                type = eqSettings.eqPayload?.type,
                q = eqSettings.eqPayload?.q,
                fs = eqSettings.eqPayload?.fs,
                gain = setEQ.gain?.map {
                    it.toScaledFloat(newScale = 2) // %.1f
                }
            )
        )
    }

    private fun retrieve7BandsEQRequest(
        device: OneDevice,
        setEQ: SetEQ,
        callback: JsCallback
    ): SetActiveEQItemRequest? {
        val eqList = device.eqListExt?.data ?: run {
            Logger.w(TAG, "retrieve7BandsEQRequest() >>> empty eq list from device instance")
            callback.invoke("-1", "eq list is empty", null)
            return null
        }

        val eqItem = eqList.eqList?.firstOrNull { item ->
            item.eqID == setEQ.eqId
        }

        if (null == eqItem) {
            Logger.w(TAG, "setEQOnPresetEQSupport() >>> didn't find target eq item match id[${setEQ.eqId}]")
            callback.invoke("-1", "didn't find target eq item match id[${setEQ.eqId}]", null)
            return null
        }

        val payload = EQListPayload(
            gain = if (eqItem.isCustomEQ()) {
                // use passback gain from hybrid
                setEQ.gain?.map {
                    it.toScaledFloat(newScale = 2) // %.1f
                }
            } else {
                // use gain from eq list
                eqItem.eqListPayload?.gain
            },
            fs = eqItem.eqListPayload?.fs
        )

        return SetActiveEQItemRequest(
            activeEQID = eqItem.eqID,
            band = eqItem.band,
            eqPayload = payload
        )
    }

    @MainThread
    private suspend fun getFeatureSupport(device: OneDevice): FeatureSupport =
        suspendCancellableCoroutine<FeatureSupport> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onFeatureSupport(featureSupport: FeatureSupport) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(featureSupport)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.getFeatureSupport()
        }

    @MainThread
    private suspend fun getEQList(device: OneDevice): EQListResponse =
        suspendCancellableCoroutine<EQListResponse> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onEQList(eqListResponse: EQListResponse) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(eqListResponse)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.getEQList()
        }

    @MainThread
    private suspend fun getEQ(device: OneDevice): EQResponse =
        suspendCancellableCoroutine<EQResponse> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onEQ(eqResponse: EQResponse) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(eqResponse)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.getEQ()
        }

    @MainThread
    private suspend fun setActiveEQ(device: OneDevice, request: SetActiveEQItemRequest): Int? =
        suspendCancellableCoroutine<Int?> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onSetActiveEQResult(errCode: Int?) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(errCode)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.setActiveEQ(request)
        }

    @MainThread
    private suspend fun setEQ(device: OneDevice, eq: EQSetting): Int? =
        suspendCancellableCoroutine<Int?> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onSetEQResult(errCode: Int?) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(errCode)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)

        }

    private val iMusicServiceLoginListener = object : IMusicServiceEventListener{

        override fun onEnterMusicServiceMainContentPage() {
        }

        override fun onEnterMusicServiceLoginPage() {
        }

        override fun onExitMusicServicePage() {
        }
    }

    private val jblOneDeviceListener = object : IOneDeviceListener {

        override fun onUpnpSetCastGroup(uuid: String, result: GetGroupInfo?) {
            super.onUpnpSetCastGroup(uuid, result)
            Logger.d(TAG, "onUpnpSetCastGroup() >>> [${device.UUID}] [$uuid] [${device?.wifiDevice?.deviceItem?.uuid}] $result")
            if(uuid == device?.wifiDevice?.deviceItem?.uuid){
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    notifyDeviceInfo()
                    handleSecondaryDeviceInfos()
                }

            }
        }

        override fun onSetCastGroup(success: Boolean) {
            super.onSetCastGroup(success)
            Logger.d(TAG, "onSetCastGroup() >>> [${device.UUID}]")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                handleSecondaryDeviceInfos()
            }

        }

        override fun setMusicServiceOpened(isOpened: Boolean) {
            super.setMusicServiceOpened(isOpened)
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _bottomPlayerVisibility.value = isOpened
            }

        }
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            gattConnectStatusListener?.invoke(status, session)
        }

        @WorkerThread
        override fun onDeviceInfo(rsp: GetDeviceInfoResponse) {
            val context = WAApplication.me ?: return

            val info = rsp.deviceInfo
            if (rsp.success() && null != info) {
                viewModelScope.launch(DISPATCHER_DEFAULT) {
                    LocalCacheAdapter.upsert(device = device, ctx = context)
                }
            }
        }

        @WorkerThread
        override fun onGetGroupParameter(rsp: GetGroupParameterRsp) {
            super.onGetGroupParameter(rsp)
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                handleSecondaryDeviceInfos()
            }
        }

        @WorkerThread
        override fun onSpeakerRearNotify(rears: List<Rear>) {
            super.onSpeakerRearNotify(rears)
            viewModelScope.launch {
                notifyDeviceInfo()
                updateJsSoundbarFeature(rears)
            }
        }

        override fun onGeneralConfigNotify(value: GeneralConfig) {
            viewModelScope.launch {
                when (value.type) {
                    TypeBassBoostEq -> {
                        notificationInvoker?.notifyObj(
                            Events.NOTIFY_BASSBOOST_INFO,
                            value.intValue().toJsBassBoostInfo().toJSONObj()
                        )
                    }

                    TypeLEDLightPatternNumber -> {
                        uiState.ultimateLight.copy(pattern = UltimatePattern.fromValue(value.intValue())!!).also {
                            uiState = uiState.copy(ultimateLight = it)
                            notificationInvoker?.notifyObj(
                                Events.NOTIFY_LIGHT_INFO,
                                JsLightInfo.fromUltimateLight(it, uiState.colorPicker).toJSONObj(),
                            )
                        }
                    }

                    TypeLEDLightShowSwitch -> {
                        uiState.ultimateLight.copy(lightControlSwitch = value.intValue()).also {
                            uiState = uiState.copy(ultimateLight = it)
                            notificationInvoker?.notifyObj(
                                Events.NOTIFY_LIGHT_INFO,
                                JsLightInfo.fromUltimateLight(it, uiState.colorPicker).toJSONObj(),
                            )
                        }
                    }
                }
            }
        }

        override fun onBatteryStatus(rsp: BatteryStatusResponse) {
            viewModelScope.launch {
                notifyDeviceInfo()
            }
        }

        override fun onGetSmartBtnConfig(rsp: GetSmartBtnConfigRsp) {
            Logger.i(TAG, "onGetSmartBtnConfig() >>> [${device.UUID}] rsp:$rsp")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _musicId.value = rsp.smartConfig?.music?.musicId
                refreshMoment(rsp.smartConfig?.soundscape?.activeSoundscapeId)
            }
        }

        override fun onC4aPermissionStatus(rsp: C4aPermissionStatusResponse) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
            }
        }

        override fun onEQ(eqResponse: EQResponse) {
            val debounce = lastSetEQTs.overThreshold(EQ_SET_NOTIFY_DEBOUNCE_MILLS)
            Logger.d(TAG, "onEQ() >>> [${device.UUID}] debounce[$debounce] rsp:$eqResponse")
            if (!debounce) return

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                syncNotifyEQInfo(eqResponse = eqResponse)
            }
        }

        override fun onEQList(eqListResponse: EQListResponse) {
            val debounce = lastSetEQTs.overThreshold(EQ_SET_NOTIFY_DEBOUNCE_MILLS)
            Logger.d(TAG, "onEQList() >>> [${device.UUID}] debounce[$debounce] rsp:$eqListResponse")
            if (!debounce) return

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                syncNotifyEQInfo(eqListResponse = eqListResponse)
            }
        }
    }

    private suspend fun syncNotifyEQInfo(
        eqResponse: EQResponse? = null,
        eqListResponse: EQListResponse? = null
    ) {
        val eqInfo = syncBuildGetEQ(
            device = device,
            eqResponse = eqResponse,
            eqListResponse = eqListResponse
        ) ?: return
        notifyEQInfo(info = eqInfo)
    }

    private fun updateJsSoundbarFeature(rears: List<Rear>?) {
        uiState.jsSoundbarFeature.rearSpeaker?.copy(enable = rears?.all { it.displayBattery() })
            .let {
                uiState.jsSoundbarFeature.copy(rearSpeaker = it)
            }.let {
                uiState.copy(jsSoundbarFeature = it)
            }.also {
                uiState = it
                notificationInvoker?.notifyObj(
                    Events.NOTIFY_SOUNDBAR_FEATURE,
                    it.jsSoundbarFeature.toJSONObj(),
                )
            }
    }

    /**
     * @link [toPlayInfo]
     */
    private fun bindPlayInfoRelatedData(owner: LifecycleOwner) {
        val momentViewModel = <EMAIL> ?: run {
            Logger.w(TAG, "bindPlayInfoRelatedData() >>> missing momentViewModel")
            return
        }

        val playerViewModel = <EMAIL> ?: run {
            Logger.w(TAG, "bindPlayInfoRelatedData() >>> missing playerViewModel")
            return
        }

        momentViewModel.momentVersion.observe(owner) { notifyPlayInfo(device = device, force = false) }
        momentViewModel.activeSoundscape.observe(owner) { notifyPlayInfo(device = device, force = false) }
        momentViewModel.soundScapeV2State.observe(owner) { notifyPlayInfo(device = device, force = false) }
        momentViewModel.albumCoverUrl.observe(owner) { notifyPlayInfo(device = device, force = false) }

        playerViewModel.isPlaying.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.inputSource.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.songName.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.artistName.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.durationMills.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.currentMills.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.volume.observe(owner) { notifyPlayInfo(device = device, force = false) }
        playerViewModel.albumCoverUrl.observe(owner) { notifyPlayInfo(device = device, force = false) }
    }

    private var lastPlayInfo: PlayInfo? = PlayInfo()

    @MainThread
    private fun notifyPlayInfo(device: OneDevice, force: Boolean) {
        if (!device.isWiFiOnline && // WiFi Controllable
            !device.isMediaPlayerSupport()) { // BLE Controllable
            if (null == lastPlayInfo) {
                return
            }

            lastPlayInfo = null
            super.notifyPlayInfo(info = null)
            return
        }

        val musicService = device.musicServiceSource()
        val playInfo = buildPlayInfo()

        if (!force && lastPlayInfo == playInfo) {
            return
        } else if (lastPlayInfo?.albumCoverUrl == playInfo.albumCoverUrl &&
            !musicService.isUriConst()) { // some music service didn't change uri between different meta
            // re.use the same imageSrc if url is same between two PlayInfos
            playInfo.imageSrc = lastPlayInfo?.imageSrc
        }

        lastPlayInfo = playInfo
        super.notifyPlayInfo(info = playInfo) // notify PlayInfo once with no album img.

        if (!playInfo.albumCoverUrl.isNullOrBlank()) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                val base64 = loadAlbumCoverBase64WithTimeout(context = WAApplication.me,
                    uri = playInfo.albumCoverUrl, pid = null, format = Bitmap.CompressFormat.PNG,
                    musicService = musicService, deviceItem = device.wifiDevice?.deviceItem,
                    width = 75, height = 75, logTag = TAG)
                if (lastPlayInfo != playInfo) {
                    return@launch
                }

                lastPlayInfo?.imageSrc = base64
                playInfo.imageSrc = base64
                super.notifyPlayInfo(info = playInfo)
            }
        }
    }

    private fun buildPlayInfo(): PlayInfo {
        val playerType = playerType

        val inputSource = Tools.mapInputSource(medium = device.mediumSource,
            track = device.trackSource(), songName = device.songName, isPlaying = device.isPlaying)

        val mixWithMusic = momentViewModel?.mixWithMusic?.value ?: false
        val activeSoundscape = momentViewModel?.activeSoundscape?.value
        val songName = Tools.mapPlayerTitle(songName = device.songName, inputSource = inputSource)
        val artistName = Tools.mapPlayerSubTitle(artistName = device.artistName, inputSource = inputSource)
        val durationMills = device.durationMills
        val currentMills = device.currentMills
        val albumCoverUrl = when (playerType) {
            EnumMiniPlayerType.MOMENT -> momentViewModel?.albumCoverUrl?.value
            EnumMiniPlayerType.PLAYER -> playerViewModel?.albumCoverUrl?.value
            else -> playerViewModel?.albumCoverUrl?.value
        }
        val smartConfigMusicId = device.smartBtnConfigExt?.config?.music?.musicId

        Logger.d(TAG, "buildPlayInfo() >>> UUID[${device.UUID}] pid[${device.pid}] " +
                "playerType[$playerType] mediumSource[${device.mediumSource}] " +
                "trackSource[${device.trackSource()}] mixWithMusic[$mixWithMusic] " +
                "activeSoundscape[${activeSoundscape?.name}] songName[$songName] " +
                "artistName[$artistName] durationMills[$durationMills] " +
                "currentMills[$currentMills] albumCoverUrl[$albumCoverUrl] " +
                "smartConfigMusicId[$smartConfigMusicId]")

        return PlayInfo(
            musicSource = inputSource.toInt(playerType = playerType, mixWithMusic = mixWithMusic),
            momentType = playerType.toMomentType(soundScapeId = activeSoundscape?.id, mixWithMusic = mixWithMusic),
            status = playerType.toStatus(musicSource = inputSource, isPlaying = device.isPlaying),
            musicName = playerType.toMusicName(moment = activeSoundscape, songName = songName),
            artistName = toArtistName(
                playerType = playerType,
                artistName = artistName,
                soundscape = activeSoundscape,
                musicId = smartConfigMusicId
            ),
            totalTime = if (EnumMiniPlayerType.PLAYER == playerType) {
                durationMills.millsToSecs().toInt()
            } else {
                null
            },
            playingTime = if (EnumMiniPlayerType.PLAYER == playerType) {
                currentMills.millsToSecs().toInt()
            } else {
                null
            },
            volume = device.volumeWithMute,
            albumCoverUrl = albumCoverUrl
        )
    }

    private fun toArtistName(
        playerType: EnumMiniPlayerType?,
        artistName: String?,
        soundscape: EnumMoment?,
        musicId: String?
    ): String? = when (playerType) {
        EnumMiniPlayerType.MOMENT -> Tools.mapMomentSubTitle(
            soundscape = soundscape,
            musicId = musicId,
            defaultMomentTitle = WAApplication.me?.getString(R.string.ambient_audio)
        )
        else -> artistName
    }

    private fun EnumMiniPlayerType?.toMusicName(moment: EnumMoment?, songName: String?): String? = when (this) {
        EnumMiniPlayerType.MOMENT -> moment?.displayNameStringRes?.let { res ->
            WAApplication.me.getString(res)
        }
        else -> songName
    }

    /**
     * 0:'none' </br> 1:'wi-fi' </br> 2: 'bt' </br>3:'usb' </br>4:'hdmi' </br>5:'tv' </br>6:'aux'
     */
    private fun EnumInputSource?.toInt(
        playerType: EnumMiniPlayerType?,
        mixWithMusic: Boolean
    ): Int {
        if (EnumMiniPlayerType.MOMENT == playerType && !mixWithMusic) {
            return 0
        }

        return when (this) {
            EnumInputSource.WIFI -> 1
            EnumInputSource.BT -> 2
            EnumInputSource.USB -> 3
            EnumInputSource.HDMI -> 4
            EnumInputSource.TV -> 5
            EnumInputSource.AUX -> 6
            else -> 0
        }
    }

    /**
     * 0:'none' </br> 1: 'forest' </br>2: 'rain' </br>3: 'ocean' </br>4: 'cityWalk'
     */
    private fun EnumMiniPlayerType?.toMomentType(soundScapeId: Int?, mixWithMusic: Boolean): Int {
        if (EnumMiniPlayerType.MOMENT == this || mixWithMusic) {
            return soundScapeId ?: 0
        }

        return 0
    }

    /**
     * 0:'no music' </br>1: 'play' </br>2: 'pause' 3: not controllable
     */
    private fun EnumMiniPlayerType?.toStatus(
        musicSource: EnumInputSource?,
        isPlaying: Boolean
    ): Int = when (this) {
        EnumMiniPlayerType.MOMENT -> 1
        EnumMiniPlayerType.PLAYER -> when (musicSource) {
            null -> 0
            EnumInputSource.TV,
            EnumInputSource.HDMI,
            EnumInputSource.AUX -> 3
            else -> if (isPlaying) 1 else 2
        }
        else -> 0
    }

    @MainThread
    private suspend fun fetchOTAStatus(device: OneDevice): OTAStatus? {
        val checkFwRsp = device.getFwCheckRspWithTimeout(logTag = TAG) ?: run {
            Logger.w(TAG, "fetchOTAStatus() >>> fail to get check fw rsp")
            return null
        }

        <EMAIL> = checkFwRsp
        val available = OneOtaActivity.isOtaAvailable(device = device, checkFwRsp = checkFwRsp)
        Logger.d(TAG, "fetchOTAStatus() >>> available[$available]")

        return OTAStatus(available = available)
    }

    private fun handleEffectLabInfo(
        activity: Activity,
        device: OneDevice,
        callback: JsCallback
    ) {
        EffectLabActivity.launchEffectLab(
            activity,
            device,
        )
        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleEnterMusicService(
        activity: FragmentActivity,
        data: JsonObject?,
        device: OneDevice,
        callback: JsCallback
    ) {
        val jsonStr = data?.toString()

        val enterMusicServiceBean = if (jsonStr.isNullOrBlank()) {
            null
        } else {
            GsonUtil.parseJsonToBean(jsonStr, EnterMusicService::class.java)
        }

        val result = portalMusicServicePage(
            activity = activity,
            source = EnumMusicServiceSource.str2Enum(value = enterMusicServiceBean?.id)
        )

        callback.invoke(if (result) "0" else "-1", null, null)
    }

    @MainThread
    fun portalMusicServicePage(activity: FragmentActivity, source: EnumMusicServiceSource?): Boolean {
        val result = if (null == source) { // fix bug HOP-27630
            musicServiceListDialog = MusicServiceListDialog(
                device = device,
                activity = activity,
                listener = object : IMusicServiceListListener {
                    override fun onMusicServices(services: List<MainItem>) {
                        notifyMusicService(info = MusicServiceList(ids = getEnableMusicServiceIds()))
                    }
                }
            ).apply {
                show()
            }
            true
        } else {
            loginHelper.notifyEnterMusicServiceMainContentPage()
            portalMusicService(source = source, activity = activity)
        }

        val provider = device.wifiDevice?.deviceItem?.uuid?.let { DlnaServiceProviderPool.me().getDlanHelper(it) }
        if (provider != null) { // fix bug HOP-27001
            WAApplication.me.SetCurrentServerProvider(provider)
        }

        return result
    }

    @MainThread
    private fun handleGetMusicService(
        device: OneDevice,
        callback: JsCallback
    ) {
        val ids = getEnableMusicServiceIds()
        callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(MusicServiceList(ids = ids))))
    }

    @MainThread
    private fun handleEnterProductSettings(
        activity: CommonHybridActivity,
        device: OneDevice,
        callback: JsCallback
    ) {
        val result = BaseProductSettingsActivity.portal(
            activity = activity,
            device = device,
            requestCode = DeviceControlHybridActivity.REQUEST_CODE
        )

        callback.invoke(if (result) "0" else "-1", null, null)
    }

    private fun getEnableMusicServiceIds(): List<String> =
        MusicServiceProvider.getEnableSources(currentDevice = device.wifiDevice?.deviceItem)
            .map { source ->
                source.strValue
            }

    private fun EQListItem.isCustomEQ(): Boolean {
        return CUSTOM_EQ_ID == this.eqID
    }

    /**
     * @return false if meeting unsupported music service type
     * supported source [MusicServiceProvider.defaultSource]
     */
    @MainThread
    private fun portalMusicService(
        source: EnumMusicServiceSource?,
        activity: FragmentActivity
    ): Boolean = when (source) {
        EnumMusicServiceSource.TUNEIN_NEW -> {
            loginHelper.portalTuneIn(activity = activity)
            true
        }

        EnumMusicServiceSource.IHEART -> {
            loginHelper.portalIHeart(activity = activity)
            true
        }

        EnumMusicServiceSource.TIDAL -> {
            loginHelper.portalTidal(activity = activity)
            true
        }

        EnumMusicServiceSource.VTUNER -> {
            loginHelper.portalVTuner(activity = activity)
            true
        }

        EnumMusicServiceSource.NAPSTER -> {
            loginHelper.portalNapster(activity = activity)
            true
        }

        EnumMusicServiceSource.QOBUZ -> {
            loginHelper.portalQOBuz(activity = activity)
            true
        }

        EnumMusicServiceSource.AMAZON_PRIME -> {
            loginHelper.portalAmazonPrime(activity = activity)
            true
        }

        EnumMusicServiceSource.CALM_RADIO -> {
            loginHelper.portalCalmRadio(activity = activity)
            true
        }

        else -> {
            Logger.e(TAG, "portalMusicService() >>> unsupported service source[${source?.strValue}]")
            false
        }
    }

    @MainThread
    private fun handleEnterCalibration(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        audioCalibrationDialog?.dismiss()
        audioCalibrationDialog = CalibrationGuideDialog(
            activity = activity,
            device = device,
            listener = object : IAudioCalibrationDialogEvent {
                override fun onCalibrationBtnClick() {
                    CalibrationBaseActivity.portal(context = activity, device = device)
                }

                override fun onLaterBtnClick() {
                    // do nothing
                }
            }
        ).apply {
            show()
        }

        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleEnterRemoteControl(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        if (activity.portalActivity(target = RemoteControllerActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private fun handleEnterRename(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        if (activity.portalActivity(target = RenameDeviceActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private fun handleEnterHomeTheatre(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        if (activity.portalActivity(target = MultichannelActivity::class.java, device = device)) {
//            activity.finishAfterTransition()
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private fun handleEnterWifiStreaming(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        if (activity.portalActivity(target = WiFiStreamingDetailActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    // Memory cache to reduce the visit time from DB.
    private var sESSID: String? = null

    @Volatile
    private var sESSIDRestoreFlag = false

    /**
     * While device is not WiFi online:
     * Show [EnumNetworkCardViewStyle.NETWORK_NOT_CONNECTED] if exists essid in DB cache before,
     * or [EnumNetworkCardViewStyle.NEVER_SETUP_BEFORE] if not.
     */
    @MainThread
    private suspend fun notifyNetworkInfo(isVisible: Boolean) {
        if (!isVisible) {
            notifyNetworkInfo(
                NetworkInfo(
                    viewStyle = EnumNetworkCardViewStyle.NOT_DISPLAYED.value,
                    essid = null
                )
            )
            return
        }

        val eSSID = eSSIDFromCache()
        val viewStyle = if (!eSSID.isNullOrBlank()) {
            EnumNetworkCardViewStyle.NETWORK_NOT_CONNECTED
        } else {
            EnumNetworkCardViewStyle.NEVER_SETUP_BEFORE
        }

        notifyNetworkInfo(
            NetworkInfo(
                viewStyle = viewStyle.value,
                essid = eSSID
            )
        )
    }

    /**
     * Read from DB only once in each lifecycle.
     */
    @MainThread
    private suspend fun eSSIDFromCache(): String? {
        if (sESSIDRestoreFlag) {
            return sESSID
        }

        sESSIDRestoreFlag = true
        sESSID = withContext(DISPATCHER_IO) {
            val ctx = WAApplication.me ?: return@withContext null
            val uuid = device.UUID ?: return@withContext null

            LocalCacheAdapter.getTargetDeviceCache(context = ctx, uuid = uuid)
        }?.eSSID

        return sESSID
    }

    /**
     * Refresh card view style when device online/offline.
     */
    @MainThread
    private fun refreshNetworkCardViewStyle() {
        _networkCardVisible.value = !device.isWiFiOnline
    }

    private val scanObserver = object : IHmDeviceObserver {
        @AnyThread
        override fun onDeviceOffline(device: Device) {
            if (device is OneDevice && device == <EMAIL>) {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    refreshNetworkCardViewStyle()
                }
            }
        }

        @AnyThread
        override fun onDeviceOnlineOrUpdate(device: Device) {
            if (device is OneDevice && device == <EMAIL>) {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    refreshNetworkCardViewStyle()
                }
            }
        }
    }

    @MainThread
    private fun handleEnterSetupNetwork(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "handleEnterSetupNetwork() >>> isBLEOnline[${device.isBLEOnline}] isPlaying[${device.isPlaying}] isGroup[${device.getGroupMode() != GroupMode.SINGLE}]")
        if (device.isBLEOnline && device.isPlaying && device.getGroupMode() == GroupMode.GROUP) {
            showSetupWifiForBleStereo(activity)
            return
        }

        wifiOOBEDialogHelper?.dismissAllDialogs()
        wifiOOBEDialogHelper = WiFiOOBEDialogHelper(
            logTag = TAG,
            activity = activity,
            device = device,
            mode = EnumMode.WIFI_SETUP,
            oobeDialogDismissListener = null,
            oobeDialogEventListener = null,
            calibrationDialogEvent = null,
            vaGuideDialogDismissListener = null,
            launcher = activity.launcher
        ).run()

        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleGetMomentStatus(callback: JsCallback) {
        val activeId = device.activeSoundscapeId
        val status = activeId.toMomentStatus()
        Logger.d(TAG, "handleGetMomentStatus() >>> activeId[$activeId] status[$status]")

        callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(status)))
    }

    @MainThread
    private fun refreshMoment(activeSoundscapeId: Int?) {
        val activeId = activeSoundscapeId ?: device.activeSoundscapeId
        val status = activeId.toMomentStatus()

        Logger.d(TAG, "refreshMoment() >>> activeId[$activeId] status[$status]")
        notifyMomentStatus(status = status)
    }

    private fun Int?.toMomentStatus(): MomentStatus {
        val activeId = this
        val moment = EnumMoment.fromValue(activeId)

        return MomentStatus(
            momentName = if (EnumMoment.MOMENT_PLAYLIST == moment) {
                val songName = musicId.value

                if (songName.isNullOrBlank()) {
                    WAApplication.me.getString(EnumMoment.MOMENT_PLAYLIST.displayNameStringRes)
                } else {
                    songName
                }
            } else {
                moment?.displayNameStringRes?.let { res ->
                    WAApplication.me.getString(res)
                }
            }
        )
    }

    @MainThread
    private fun checkOtaStatus() {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            // refresh to not available first until receive latest status rsp
            super.notifyOtaStatus(status = OTAStatus(available = false))

            val otaStatus = fetchOTAStatus(device = device)
            if (null != otaStatus) {
                super.notifyOtaStatus(status = otaStatus)
            }
        }
    }

    private val albumMetadataObserver = Observer { _, data ->
        if (data is MessageAlbumObject && MessageAlbumType.TYPE_ALARM_CONTEXT_CHANGED == data.type) {
            val item = (data.message as? AlarmContextItem) ?: return@Observer
            updateMusicList(item = item)
        }
    }

    @AnyThread
    private fun updateMusicList(item: AlarmContextItem) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val coverUrl = BackupQueueUtils.getAlarmAlbumCover(item)
            Logger.d(TAG, "updateMusicList() >>> album name[${item.name}] coverUrl:$coverUrl")
            _musicId.value = item.name
        }
    }

    @MainThread
    private fun handleEnterFullPlayer(device: OneDevice, activity: CommonHybridActivity, callback: JsCallback) {
        if (device.isSub()) {
            Logger.w(TAG, "handleEnterFullPlayer() >>> sub device not able to enter full player. [${device.UUID}]")
            return
        }

        if (!device.isWiFiOnline && // WiFi Controllable
            !device.isMediaPlayerSupport()) { // BLE Controllable
            Logger.w(TAG, "handleEnterFullPlayer() >>> device neither wifi online nor ble controllable. [${device.UUID}]")
            return
        }

        activity.portalActivity(FullPlayerActivity::class.java, device)
        callback.invoke("0", null, null)
    }

    @MainThread
    private suspend fun syncBuildGetEQ(
        device: OneDevice,
        eqResponse: EQResponse? = null,
        eqListResponse: EQListResponse? = null
    ): GetEQ? {
        Logger.d(TAG, "syncBuildGetEQ() >>> cached featSupport:\n${device.featSupportExt?.featSupport}")
        val featSupport = device.featSupportExt?.featSupport ?: getFeatureSupport(device)

        Logger.d(TAG, "syncBuildGetEQ() >>> featSupport:\n${GsonUtil.parseBeanToJson(featSupport)}")
        Logger.d(TAG, "syncBuildGetEQ() >>> featSupport band:\n${device.featSupportExt?.featSupport?.userEQ?.band}, ${device.support7BandsEq}")

        return if (device.support7BandsEq) {
            syncBuild7BandsGetEQ(eqListResponse = eqListResponse)
        } else {
            syncBuildNon7BandsGetEQ(eqResponse = eqResponse)
        }
    }

    @MainThread
    private suspend fun syncBuild7BandsGetEQ(eqListResponse: EQListResponse? = null): GetEQ? {
        Logger.d(TAG, "syncBuild7BandsGetEQ() >>> cached eqList:\n${device.eqListExt?.data}")
        val eqList = eqListResponse ?: getEQList(device)
        Logger.d(TAG, "syncBuild7BandsGetEQ() >>> eqList:\n${GsonUtil.parseBeanToJson(eqList)}")

        return if (eqList.success()) {
            GetEQ(
                isEnable = true,
                currentEQId = eqList.activeEQID,
                customEQId = CUSTOM_EQ_ID,
                eqList = eqList.eqList.mapToEQList()?.filter {
                    if (!device.supportPresetEq) {
                        it.eqId == CUSTOM_EQ_ID
                    } else true
                }
            )
        } else {
            Logger.w(TAG, "syncBuild7BandsGetEQ() >>> fail to get eq list")
            null
        }
    }

    @MainThread
    private suspend fun syncBuildNon7BandsGetEQ(
        eqResponse: EQResponse? = null
    ): GetEQ {
        Logger.d(TAG, "syncBuild7BandsGetEQ() >>> cached eqRsp:\n${device.eqExt?.data}")
        val eqRsp = eqResponse ?: getEQ(device = device)
        Logger.d(TAG, "syncBuildNon7BandsGetEQ() >>> eqRsp:\n${GsonUtil.parseBeanToJson(eqRsp)}")

        return GetEQ(
            isEnable = eqRsp.success(),
            currentEQId = eqRsp.eqSetting?.eqID,
            customEQId = eqRsp.eqSetting?.eqID,
            eqList = eqRsp.eqSetting.mapToEQList()
        )
    }

    override val tag: String = TAG

    override fun handleStartForResult(result: ActivityResult) {
        super.handleStartForResult(result)
        wifiOOBEDialogHelper?.onActivityResult(result = result)
    }

    companion object {
        private const val TAG = "OneHybridViewModel"
        const val LEARN_MORE_ABOUT_VA = "https://www.jbl.com/mae"

        private const val EQ_SET_NOTIFY_DEBOUNCE_MILLS = 500L
    }
}

private data class UiState(
    val colorPicker: ColorPicker = ColorPicker(),
    val ultimateLight: UltimateLight = UltimateLight(),
    val jsDevInfo: DeviceInfo = DeviceInfo(),
    val jsSoundbarFeature: JsSoundbarFeature = JsSoundbarFeature(),
)

sealed class OHVMEvent {
    data class EnterCoulson(val status: EnumCoulsonStatus, val device: OneDevice) : OHVMEvent()
}
