package com.harman.oobe.ble

import android.bluetooth.BluetoothDevice
import android.os.Bundle
import android.view.LayoutInflater
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.harman.bar.app.databinding.DialogBleOobeBinding
import com.harman.connect.disconnectBrEdr
import com.harman.connect.disconnectGatt
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBandDevice
import com.harman.oobe.IOOBEDialogEventListener
import com.harman.oobe.FoundNewProductUIState
import com.harman.oobe.wifi.DoubleConfirmDialog
import com.harman.oobe.wifi.IDoubleConfirmListener
import com.harman.oobe.AuthUIState
import com.harman.oobe.BaseOOBEDialog
import com.harman.discover.bean.PartyBoxDevice
import com.harman.getBLEProtocol
import com.harman.log.Logger

/**
 * Created by gerrar<PERSON><PERSON>hang on 2025/1/15.
 */
class BLEOOBEDialog(
    activity: FragmentActivity,
    override val device: Device,
    override val deviceName: String,
    private val listener: IOOBEDialogEventListener? = null
) : BaseOOBEDialog(activity), IOOBEDialog {

    init {
        Logger.i(TAG, "() >>> UUID[${device.UUID}] displayedName[${deviceName}]")
    }

    private var binding: DialogBleOobeBinding? = null

    private var viewModel: BLEOOBEViewModel? = null

    override val authUIStatus = MutableLiveData<AuthUIState>(AuthUIState.READY)

    override val foundNewProductUIStatus = MutableLiveData<FoundNewProductUIState>(
        FoundNewProductUIState.FOUND_NEW_PRODUCT
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCancelable(flag = false)

        val viewModel = ViewModelProvider(
            activity,
            BLEOOBEViewModelFactory(activity = activity)
        )[BLEOOBEViewModel::class.java]
        <EMAIL> = viewModel

        lifecycle.addObserver(viewModel)

        val binding = binding()
        <EMAIL> = binding
        setContentView(binding.root)

        viewModel.init(dialog = this@BLEOOBEDialog)
        activity.lifecycle.addObserver(activityLifecycleObserver)
    }

    private fun binding(): DialogBleOobeBinding {
        val inflater = LayoutInflater.from(context)

        val binding = DialogBleOobeBinding.inflate(inflater)
        binding.dialog = this@BLEOOBEDialog
        binding.viewModel = viewModel
        binding.lifecycleOwner = this@BLEOOBEDialog

        return binding
    }

    override fun showDoubleConfirmDialog() {
        doubleConfirmDialog.show()
    }

    override fun dismissDialog(isInterrupt: Boolean) {
        when (device) {
            is PartyBoxDevice -> {
                listener?.onOOBEFlowEnd(device = device, isInterrupt = isInterrupt)
                // disconnect Gatt/BrEdr after OOBE complete (whether success or not)
                when (device.getBLEProtocol()) {
                    BluetoothDevice.TRANSPORT_LE -> device.disconnectGatt()
                    BluetoothDevice.TRANSPORT_BREDR -> device.disconnectBrEdr()
                    else -> {
                        // no impl.
                    }
                }
            }

            is PartyBandDevice -> {
                listener?.onOOBEFlowEnd(device = device, isInterrupt = isInterrupt)
            }

            else -> throw Exception("Devices that do not support the OOBE process. $device")
        }
        dismiss()
    }

    override fun <T> observe(target: LiveData<T>, observer: Observer<T>) {
        target.observe(this, observer)
    }

    override fun onSetupWiFiSuccess() {
        listener?.onSetupWiFiSuccess()
    }

    override fun onStop() {
        super.onStop()

        viewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }
    }

    private val doubleConfirmDialog: DoubleConfirmDialog by lazy {
        DoubleConfirmDialog(activity = activity, resultCallback = doubleConfirmListener)
    }

    fun onDebugCloseAllClick() {
        listener?.onDebugCloseAllClick()
    }

    fun onPositiveButtonClick() {
        viewModel?.onConnectBLE()
    }

    fun onNegativeButtonClick() {
        viewModel?.onLater()
    }

    private val doubleConfirmListener = object : IDoubleConfirmListener {
        override fun onResult(quit: Boolean) {
            viewModel?.onDoubleConfirmResult(quit = quit)
        }
    }

    private val activityLifecycleObserver = object : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            doubleConfirmDialog.dismiss()
        }
    }

    companion object {
        private const val TAG = "BLEOOBEDialog"
    }
}