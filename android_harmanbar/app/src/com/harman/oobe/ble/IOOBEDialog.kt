package com.harman.oobe.ble

import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.harman.discover.bean.Device
import com.harman.oobe.AuthUIState
import com.harman.oobe.FoundNewProductUIState
import com.harman.oobe.wifi.flow.IOOBEFlow
import com.harman.discover.bean.PartyBoxDevice

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/2.
 *
 * Limit the dialog operation options in each [IOOBEFlow]
 */
interface IOOBEDialog {

    val device: Device

    val deviceName: String

    val authUIStatus: MutableLiveData<AuthUIState>

    val foundNewProductUIStatus: MutableLiveData<FoundNewProductUIState>

    fun dismissDialog(isInterrupt: Boolean)

    fun showDoubleConfirmDialog()

    fun <T> observe(target: LiveData<T>, observer: Observer<T>)

    @MainThread
    fun onSetupWiFiSuccess()
}