package com.harman.oobe.wifi

import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.harman.oobe.AuthUIState
import com.harman.oobe.wifi.flow.SelfTuningUIState
import com.harman.oobe.FoundNewProductUIState
import com.harman.oobe.wifi.flow.IOOBEFlow
import com.harman.oobe.wifi.flow.SetupWifiUIState
import com.harman.discover.bean.OneDevice
import com.harman.oobe.wifi.flow.FirstOTAUIState

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/2.
 *
 * Limit the dialog operation options in each [IOOBEFlow]
 */
interface IOOBEDialog {

    val device: OneDevice

    val deviceName: String

    val authUIStatus: MutableLiveData<AuthUIState>

    val calibrationUIStatus: MutableLiveData<SelfTuningUIState>

    val icUpToDateVisible: MutableLiveData<Boolean>

    val foundNewProductUIStatus: MutableLiveData<FoundNewProductUIState>

    val setupWifiUIStatus: MutableLiveData<SetupWifiUIState>

    val isPwdVisible: MutableLiveData<Boolean>

    fun showDoubleConfirmDialog()

    fun dismissDialog(isInterrupt: Boolean)

    fun <T> observe(target: LiveData<T>, observer: Observer<T>)

    fun portalUrl(url: String)

    fun hideInputMethod()

    fun portalHelpLink()

    fun portalSettingsPage()

    @MainThread
    fun onSetupWiFiSuccess()

    val firstOtaUIStatus: MediatorLiveData<FirstOTAUIState>

}