package com.harman.oobe.wifi

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.map
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogWifiOobeBinding
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumOtaStatus
import com.harman.connect.disconnectGatt
import com.harman.oobe.IOOBEDialogEventListener
import com.harman.streaming.google.IGoogleCastDialog
import com.harman.oobe.AuthUIState
import com.harman.oobe.BaseOOBEDialog
import com.harman.oobe.wifi.flow.SelfTuningUIState
import com.harman.oobe.wifi.flow.ChromeCastUIState
import com.harman.oobe.wifi.flow.FirstOTAUIState
import com.harman.oobe.FoundNewProductUIState
import com.harman.oobe.wifi.flow.SetupWifiUIState
import com.harman.portalUrl
import com.harman.safeAddNonNullSources
import com.harman.streaming.google.GoogleCastActivity.Companion.DEFAULT_GOOGLE_HOME_URL
import com.harman.discover.bean.OneDevice
import com.harman.displayDeviceName
import com.harman.isHKOneApp
import com.harman.log.Logger
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.model.APViewModel
import com.wifiaudio.utils.AssetsProjectConfigParse

/**
 * Created by gerrardzhang on 2024/4/29.
 *
 * Base fragment for OOBE flow navi graph.
 */
class WiFiOOBEDialog(
    activity: FragmentActivity,
    override val device: OneDevice,
    override val deviceName: String,
    private val mode: EnumMode,
    private val listener: IOOBEDialogEventListener? = null
) : BaseOOBEDialog(activity), IOOBEDialog, IGoogleCastDialog {

    init {
        Logger.i(TAG, "() >>> UUID[${device.UUID}] displayedName[${deviceName}]")
    }

    private var binding: DialogWifiOobeBinding? = null

    private val doubleConfirmDialog: DoubleConfirmDialog by lazy {
        DoubleConfirmDialog(activity = activity, resultCallback = doubleConfirmListener)
    }

    private var viewModel: WiFiOOBEViewModel? = null

    private val securityViewModel: APViewModel by lazy {
        ViewModelProvider(activity)[APViewModel::class.java]
    }

    private var apListAdapter: APListAdapter? = null
    private var securityAdapter: SecurityAdapter? = null

    override val foundNewProductUIStatus = MutableLiveData<FoundNewProductUIState>(
        FoundNewProductUIState.FOUND_NEW_PRODUCT)

    override val authUIStatus = MutableLiveData<AuthUIState>(AuthUIState.READY)

    override val setupWifiUIStatus = MutableLiveData<SetupWifiUIState>()

    override val firstOtaUIStatus = MediatorLiveData<FirstOTAUIState>()

    override val calibrationUIStatus = MutableLiveData<SelfTuningUIState>(SelfTuningUIState.READY)

    override val isPwdVisible = MutableLiveData<Boolean>(false)

    val isFirstOtaFail: LiveData<Boolean> = firstOtaUIStatus.map { status ->
        when (status) {
            FirstOTAUIState.FAIL_WITH_RETRY,
            FirstOTAUIState.FAIL_WITHOUT_RETRY -> true
            else -> false
        }
    }

    val otaLoading: LiveData<Boolean> = firstOtaUIStatus.map { status ->
        FirstOTAUIState.LOADING == status
    }

    val otaTitleTxt: LiveData<String?> = firstOtaUIStatus.map { status ->
        when (status) {
            FirstOTAUIState.LOADING -> context.resources.getString(R.string.checking_for_the_latest_firmware)
            FirstOTAUIState.UP_TO_DATE -> context.resources.getString(R.string.harmanbar_jbl_Your_product_is_up_to_date_)
            FirstOTAUIState.PERCENTAGE -> context.resources.getString(R.string.jbl_product_will_be_updated_to_a_latest_software_to_ensure_better_user_experience)
            FirstOTAUIState.BATTERY_TIPS -> context.resources.getString(R.string.plug_in_power_to_continue_update)
            FirstOTAUIState.FAIL_WITH_RETRY,
            FirstOTAUIState.FAIL_WITHOUT_RETRY -> context.resources.getString(R.string.jbl_Sorry__there_was_a_problem_updating_your_product__No_changes_were_made_)
            else -> ""
        }
    }

    override val icUpToDateVisible = MutableLiveData<Boolean>(true)

    private val _otaProgress = MediatorLiveData<Int>()
    val otaProgress: LiveData<Int>
        get() = _otaProgress

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCancelable(flag = false)

        val viewModel = ViewModelProvider(
            activity,
            WiFiOOBEViewModelFactory(activity = activity, mode = mode)
        )[WiFiOOBEViewModel::class.java]
        <EMAIL> = viewModel

        lifecycle.addObserver(viewModel)

        val apAdapter = APListAdapter(viewModel = viewModel, lifecycleOwner = this@WiFiOOBEDialog)
        <EMAIL> = apAdapter

        val securityAdapter = SecurityAdapter(viewModel = viewModel, lifecycleOwner = this@WiFiOOBEDialog)
        <EMAIL> = securityAdapter

        val binding = binding(apAdapter = apAdapter, securityAdapter = securityAdapter)
        <EMAIL> = binding
        setContentView(binding.root)

        viewModel.init(dialog = this@WiFiOOBEDialog, device = device, mode = mode)
        activity.lifecycle.addObserver(activityLifecycleObserver)
        activity.lifecycle.addObserver(viewModel)

        dataBinding(viewModel = viewModel)
    }

    private fun dataBinding(viewModel: WiFiOOBEViewModel) {
        firstOtaUIStatus.safeAddNonNullSources(
            viewModel.batteryStatus,
            viewModel.otaStatus
        ) { batteryStatus, otaStatus ->
            firstOtaUIStatus.value = updateFirstOtaUIStatus(otaStatus = otaStatus, batteryStatus = batteryStatus)
        }

        _otaProgress.safeAddNonNullSources(
            viewModel.otaStatus,
            viewModel.downloadProgress,
            viewModel.burningProgress
        ) { otaStatus, downloadProgress, burningProgress ->
            _otaProgress.value = mapOtaProgress(
                otaStatus = otaStatus,
                downloadProgress = downloadProgress ?: 0,
                burningProgress = burningProgress ?: 0
            )
        }
    }

    private fun mapOtaProgress(
        otaStatus: EnumOtaStatus?,
        downloadProgress: Int,
        burningProgress: Int
    ): Int = when (otaStatus) {
        EnumOtaStatus.DOWNLOADING -> downloadProgress / 2
        EnumOtaStatus.BURNING_START,
        EnumOtaStatus.BURNING_SUCCESS -> 50 + burningProgress / 2
        else -> 0
    }

    fun onPositiveButtonClick() {
        viewModel?.onConnectBLE()
    }

    fun onNegativeButtonClick() {
        viewModel?.onLater()
    }

    private fun binding(apAdapter: APListAdapter, securityAdapter: SecurityAdapter): DialogWifiOobeBinding {
        val inflater = LayoutInflater.from(context)

        val binding = DialogWifiOobeBinding.inflate(inflater)
        binding.dialog = this@WiFiOOBEDialog
        binding.viewModel = viewModel
        binding.apListAdapter = apAdapter
        binding.securityAdapter = securityAdapter
        binding.securityViewModel = securityViewModel
        binding.lifecycleOwner = this@WiFiOOBEDialog

        return binding
    }

    override fun onStop() {
        super.onStop()
        viewModel?.stopAllBusinesses()

        viewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }
    }

    override fun showDoubleConfirmDialog() {
        doubleConfirmDialog.show()
    }

    override fun dismissDialog(isInterrupt: Boolean) {
        listener?.onOOBEFlowEnd(device = device, isInterrupt = isInterrupt)
        // disconnect Gatt after OOBE complete (whether success or not)
        device.disconnectGatt()
        dismiss()
    }

    private val activityLifecycleObserver = object : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            doubleConfirmDialog.dismiss()
        }
    }

    private val doubleConfirmListener = object : IDoubleConfirmListener {
        override fun onResult(quit: Boolean) {
            viewModel?.onDoubleConfirmResult(quit = quit)
        }
    }

    override fun portalHelpLink() {
        val url = AppConfigurationUtils.getSupportModelList()?.wifiSetupFailUrl
            ?: if (isHKOneApp()) "https://gtly.to/8lcF6wGCN" else "https://gtly.to/iPKcbaTcO"
        Logger.i(TAG, "portalHelpLink() >>> url:$url")

        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        intent.data = Uri.parse(url)
        activity.startActivity(intent)
    }

    override fun hideInputMethod() {
        val imm = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            ?: return

        if (imm.isActive) {
            imm.hideSoftInputFromWindow(
                <EMAIL>?.root?.windowToken,
                InputMethodManager.HIDE_NOT_ALWAYS
            )
        }
    }

    override fun portalSettingsPage() {
        activity.startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
    }

    private fun updateFirstOtaUIStatus(otaStatus: EnumOtaStatus?, batteryStatus: BatteryStatusResponse?): FirstOTAUIState {
        Logger.d(TAG, "updateFirstOtaUIStatus() >>> ota[$otaStatus][${otaStatus?.desc}] " +
                "battery[$batteryStatus][${batteryStatus?.isOtaAvailable}]")

        if (otaStatus.isOtaProcessing() && (false == batteryStatus?.isOtaAvailable)) {
            // Only display low battery tips when confirm Ota is configured.
            return FirstOTAUIState.BATTERY_TIPS
        }

        return otaStatus.mapToUiState()
    }

    private fun EnumOtaStatus?.mapToUiState(): FirstOTAUIState = when (this) {
        EnumOtaStatus.UNKNOWN,
        EnumOtaStatus.NOT_START,
        EnumOtaStatus.CHECKING -> FirstOTAUIState.LOADING

        EnumOtaStatus.NEW_VERSION -> FirstOTAUIState.LOADING

        EnumOtaStatus.UPDATE_TO_DATE,
        EnumOtaStatus.SUCCESS -> FirstOTAUIState.UP_TO_DATE

        EnumOtaStatus.DOWNLOADING,
        EnumOtaStatus.BURNING_START,
        EnumOtaStatus.BURNING_SUCCESS -> FirstOTAUIState.PERCENTAGE

        EnumOtaStatus.BURNING_FAIL,
        EnumOtaStatus.FAIL -> FirstOTAUIState.FAIL_WITH_RETRY

        else -> FirstOTAUIState.LOADING
    }

    private fun EnumOtaStatus?.isOtaProcessing(): Boolean = when (this) {
        EnumOtaStatus.NEW_VERSION,
        EnumOtaStatus.DOWNLOADING,
        EnumOtaStatus.BURNING_START -> true
        EnumOtaStatus.BURNING_SUCCESS,
        EnumOtaStatus.BURNING_FAIL,
        EnumOtaStatus.FAIL -> false
        else -> false
    }

    val calibrationTitle: LiveData<String> = calibrationUIStatus.map { state ->
        when (state) {
            SelfTuningUIState.READY -> {
                context.resources
                    .getString(R.string.tune_device_for_optimized_sound_performance)
                    .format(device.deviceName)
            }
            SelfTuningUIState.DOING -> {
                context.resources.getString(R.string.text_tuning_2)
            }
            SelfTuningUIState.DONE -> {
                context.resources.getString(R.string.your_product_has_been_tuned_tips)
            }
            else -> ""
        }
    }
    val calibrationBtnTxt: LiveData<String> = calibrationUIStatus.map { state ->
        when (state) {
            SelfTuningUIState.READY -> {
                context.resources.getString(R.string.harmanbar_jbl_START)
            }
            SelfTuningUIState.DONE -> {
                context.resources.getString(R.string.harmanbar_jbl_DONE)
            }
            else -> ""
        }
    }
    val calibrationBtnVisible: LiveData<Boolean> = calibrationUIStatus.map { state ->
        when (state) {
            SelfTuningUIState.READY -> true
            SelfTuningUIState.DOING -> false
            SelfTuningUIState.DONE -> true
            else -> false
        }
    }
    val calibrationLoadingVisible: LiveData<Boolean> = calibrationBtnVisible.map { !it }

    override fun portalUrl(url: String) {
        activity.portalUrl(url = url)
    }

    fun onDebugCloseAllClick() {
        listener?.onDebugCloseAllClick()
    }

    override fun <T> observe(target: LiveData<T>, observer: Observer<T>) {
        target.observe(this, observer)
    }

    override val chromeCastUIStatus = MutableLiveData<ChromeCastUIState>(ChromeCastUIState.AVAILABLE)

    override val chromecastTitle: LiveData<String> = chromeCastUIStatus.map { state ->
        when (state) {
            ChromeCastUIState.AVAILABLE -> {
                context.resources.getString(R.string.google_cast_available)
            }
            ChromeCastUIState.HELP_IMPROVE -> {
                context.resources.getString(R.string.google_cast_terms_of_service_and_google_privacy_policy)
            }
            ChromeCastUIState.YOU_ARE_READY_TO_START_CASTING -> {
                context.resources.getString(R.string.you_are_ready_to_start_casting)
            }
            else -> ""
        }
    }

    override val chromecastImgRes: LiveData<Int> = chromeCastUIStatus.map { state ->
        when (state) {
            ChromeCastUIState.AVAILABLE,
            ChromeCastUIState.HELP_IMPROVE -> {
                R.drawable.ic_google_cast_with_text_fg_primary
            }
            ChromeCastUIState.YOU_ARE_READY_TO_START_CASTING -> {
                R.drawable.ic_works_with_google_home
            }
            else -> {
                R.drawable.ic_google_cast_with_text_fg_primary
            }
        }
    }

    override val closeBtnVisible: Boolean = false

    override val enableLaterVisible: Boolean = true

    override val enableBtnTextRes: Int = R.string.harmanbar_jbl_ENABLE_SERVICE

    override val dialogBackgroundRes: Int = R.drawable.radius_large_bg_card

    override fun onGoogleTOSClick() {
        val url = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_terms_url")
        if (url.isNullOrBlank()) {
            return
        }

        portalUrl(url = url)
    }

    override fun onGooglePrivacyPolicyClick() {
        val url = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_privacy_url")
        if (url.isNullOrBlank()) {
            return
        }

        portalUrl(url = url)
    }

    override fun onEnableServiceClick() {
        // send command without waiting response
        viewModel?.setC4aPermissionStatus(status = EnumC4aPermissionStatus.ON)
        chromeCastUIStatus.value = ChromeCastUIState.HELP_IMPROVE
    }

    private var dialog: GoogleCastReminderDialog? = null

    override fun onGoogleCastLater() {
        dialog?.dismiss()
        dialog = GoogleCastReminderDialog(context = activity, listener = {
            dismissDialog(isInterrupt = false)
        }).apply {
            show()
        }
    }

    override fun onLearnMoreClick() {
        val url = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_learn_more_url")
        if (url.isNullOrBlank()) {
            return
        }

        portalUrl(url = url)
    }

    override fun onChromeCastOpt(optIn: Boolean) {
        // send command without waiting response
        // (no matter user choose Yes or No, cast is enabled)
        viewModel?.setChromeCastOptIn(optIn = ChromeCastOpt.ON)

        chromeCastUIStatus.value = ChromeCastUIState.YOU_ARE_READY_TO_START_CASTING
    }

    override fun onGoToGoogleHomeAppClick() {
        val intent = activity.packageManager.getLaunchIntentForPackage("com.google.android.apps.chromecast.app")

        if (null != intent) { // Google Home installed locally.
            activity.startActivity(intent)
        } else { // Portal to Google Play.
            activity.portalUrl(
                config = AppConfigurationUtils.getGPGoogleHomeUrl(),
                default = DEFAULT_GOOGLE_HOME_URL
            )
        }
    }

    override fun onReadyToStartDoneClick() {
        dismissDialog(isInterrupt = false)
    }

    override fun onSetupWiFiSuccess() {
        listener?.onSetupWiFiSuccess()
    }

    companion object {

        private const val TAG = "WiFiOOBEDialog"
    }
}