package com.harman.music.player

import android.bluetooth.BluetoothDevice
import android.content.Context
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.harman.command.partybox.gatt.PartyBoxGattCommandProcessor
import com.harman.command.partybox.gatt.timer.AutoOffTimerRsp
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.supportAutoOff
import com.harman.connect.syncBrEdrConnectWithTimeout
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.music.EnumInputSource
import com.harman.music.service.EnumMusicServiceSource
import com.harman.discover.bean.PartyBoxDevice
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by gerrardzhang on 2024/9/20.
 */
class PartyBoxPlayerViewModelFactory(
    private val device: PartyBoxDevice
): ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return PartyBoxPlayerViewModel(device = device) as T
    }
}

class PartyBoxPlayerViewModel(
    device: PartyBoxDevice
) : PlayerViewModel<PartyBoxDevice>(device = device) {

    override val deviceName: String? = super.device.deviceName

    private val _isPlaying = MutableLiveData<Boolean>(super.device.isPlaying)
    override val isPlaying: LiveData<Boolean?>
        get() = _isPlaying

    private val _songName = MutableLiveData<String?>(super.device.songName)
    override val songName: LiveData<String?>
        get() = _songName

    private val _artistName = MutableLiveData<String?>(super.device.artistName)
    override val artistName: LiveData<String?>
        get() = _artistName

    private val _durationMills = MutableLiveData<Long?>(super.device.durationMills)
    override val durationMills: LiveData<Long?>
        get() = _durationMills

    // don't init param in constructor. let onResume do init later.
    private val _currentMills = MutableLiveData<Long?>()
    override val currentMills: LiveData<Long?>
        get() = _currentMills

    /**
     * @Range from 0 to 100
     */
    private val _playPercentage = MediatorLiveData<Int?>()
    override val playPercentage: LiveData<Int?>
        get() = _playPercentage

    private val _volume = MutableLiveData<Int>(super.device.volumeWithMute)
    override val volume: LiveData<Int>
        get() = _volume

    private val _inputSource = MutableLiveData<EnumInputSource?>(super.device.audioSource.toEnumInputSource())
    override val inputSource: LiveData<EnumInputSource?>
        get() = _inputSource

    // Unused or fixed params.
    override val musicServiceSource: LiveData<EnumMusicServiceSource?> = MutableLiveData<EnumMusicServiceSource?>()
    override val isSupportDolbyAtoms: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isSupportDolbyAudio: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isSupportHD: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isSupportUHD: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isSupportHiRes: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val hiResSampleRate: LiveData<String?> = MutableLiveData<String?>(null)
    override val albumCoverUrl: LiveData<String?> = MutableLiveData<String?>(null)
    override val sleepTimerSecs: LiveData<Int?> = MutableLiveData<Int?>(null)

    private val _sleepTimerRemainSecs: MutableLiveData<Int?> = MutableLiveData<Int?>(null)
    override val sleepTimerRemainSecs: LiveData<Int?>
        get() = _sleepTimerRemainSecs

    override val isCDQuality: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isMp3Quality: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isHighQuality: LiveData<Boolean?> = MutableLiveData<Boolean>(false)
    override val isTransitioning: LiveData<Boolean> = MutableLiveData<Boolean>(false)

    private val _isTimerSupport = MutableLiveData<Boolean>(super.device.supportAutoOff())
    override val isTimerSupport: LiveData<Boolean>
        get() = _isTimerSupport

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        bindDevice(device = device, context = owner as? Context ?: return)
    }

    private fun connect(context: Context) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val protocol = device.getBLEProtocol()

            val result = withContext(DISPATCHER_DEFAULT) {
                when (protocol) {
                    BluetoothDevice.TRANSPORT_LE -> device.syncGattConnectWithTimeout(context)
                    BluetoothDevice.TRANSPORT_BREDR -> device.syncBrEdrConnectWithTimeout(context)
                    else -> false
                }
            }

            if (result) {
                device.reqPlayerInfo(protocol = protocol)

                if (null == device.deviceFeature) {
                    device.reqDeviceFeatureInfo(protocol = protocol)
                }

                if (device.supportAutoOff()) {
                    device.reqAutoOffTimer(protocol = protocol)
                }
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        device.unregisterDeviceListener(deviceListener)
    }

    private val deviceListener = object : IPartyBoxDeviceListener {
        /**
         * mapping [PartyBoxGattCommandProcessor.parsePlayerInfo]
         */
        @AnyThread
        override fun onPlayerInfoUpdate() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _inputSource.value = <EMAIL>()
                _isPlaying.value = <EMAIL>
                _volume.value = <EMAIL>
                _songName.value = <EMAIL>
                _artistName.value = <EMAIL>
            }
        }

        @AnyThread
        override fun onVolume(volume: Int) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _volume.value = <EMAIL>
            }
        }

        @AnyThread
        override fun onMute(isMute: Boolean) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _volume.value = <EMAIL>
            }
        }

        @AnyThread
        override fun onAutoOffTimerUpdate(response: AutoOffTimerRsp?) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                Logger.d(TAG, "onAutoOffTimerUpdate() >>> remainSecs[${response?.remainingTime}]")
                val remainSecs = response?.remainingTime ?: 0

                _sleepTimerRemainSecs.value = remainSecs
                refreshSleepTimerTicker(remainSecs = remainSecs)
            }
        }

        @AnyThread
        override fun onDeviceFeatureUpdate() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                if (<EMAIL>()) {
                    _isTimerSupport.value = true
                    <EMAIL>(protocol = <EMAIL>())
                } else {
                    _isTimerSupport.value = false
                }
            }
        }
    }

    override fun playOrPause() {
        if (true == _isPlaying.value) {
            _isPlaying.value = false
            device.pauseMusic(protocol = device.getBLEProtocol())
        } else {
            _isPlaying.value = true
            device.playMusic(protocol = device.getBLEProtocol())
        }
    }

    override fun previous() {
        device.prevMusic(protocol = device.getBLEProtocol())
    }

    override fun next() {
        device.nextMusic(protocol = device.getBLEProtocol())
    }

    override fun setVolume(volume: Int) {
        device.setRemoteVolume(protocol = device.getBLEProtocol(), value = volume)
    }

    override fun seek(percentage: Int) {
        // no impl
    }

    @MainThread
    fun switchDevice(device: PartyBoxDevice, context: Context) {
        super.device.unregisterDeviceListener(deviceListener)
        super.device = device
        bindDevice(device = device, context = context)
    }

    private fun bindDevice(device: PartyBoxDevice, context: Context) {
        device.registerDeviceListener(deviceListener)
        connect(context = context)
    }

    private var sleepTimerTicker: Job? = null

    private fun refreshSleepTimerTicker(remainSecs: Int) {
        Logger.d(TAG, "refreshSleepTimerTicker() >>> cancel auto sleep timer [$remainSecs]")
        sleepTimerTicker?.cancel()

        if (remainSecs <= 0) {
            return
        }

        sleepTimerTicker = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            Logger.d(TAG, "refreshSleepTimerTicker() >>> start [$remainSecs]")
            while (true) {
                delay(1000)
                val current = _sleepTimerRemainSecs.value
                if (null == current || current <= 0) {
                    return@launch
                }

                _sleepTimerRemainSecs.value = current - 1
            }
        }
    }

    companion object {
        private const val TAG = "PartyBoxPlayerViewModel"
    }
}