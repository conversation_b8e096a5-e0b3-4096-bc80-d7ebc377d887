package com.harman.music.player

import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import com.harman.CustomLoadingDialog
import com.harman.CustomToast
import com.harman.bar.app.R
import com.harman.bar.app.databinding.FragmentOnePlayerBinding
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools.overThreshold
import com.harman.discover.util.Tools.roundPercentage
import com.harman.log.Logger
import com.harman.moment.MomentViewModel
import com.harman.moment.MomentViewModelFactory
import com.harman.music.AutoPlayerOffDialog
import com.harman.music.EnumInputSource
import com.harman.music.EnumInputSource.Companion.toLargeIconImgRes
import com.harman.music.Tools
import com.harman.music.Tools.VOLUME_SEEKBAR_DRAG_UPDATE_THRESHOLD
import com.harman.music.Tools.formatTimeAsMills
import com.harman.music.Tools.formatTimeAsSecs
import com.harman.music.Tools.showQueue
import com.harman.music.Tools.isMomentMixWithMusicActive
import com.harman.music.Tools.showMore
import com.harman.music.more.IMoreEventListener
import com.harman.music.more.MoreDialog
import com.harman.music.more.MoreHelper
import com.harman.music.more.QueueDialog
import com.harman.music.service.EnumMusicServiceSource
import com.harman.portalUrl
import com.harman.safeAddSource
import com.harman.safeAddSources
import com.harman.streaming.SpotifyActivity.Companion.DEFAULT_GOOGLE_PLAY_SPOTIFY
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.jbl.one.configuration.AppConfigurationUtils
import com.utils.glide.BitmapLoadingListener
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * Created by gerrardzhang on 2024/9/13.
 *
 * Music player UI in full player page.
 * Fragment need a constructor with no input param.
 */
class FullPlayerFragment: AbsFullFragment() {

    private val rootActivity: FullPlayerActivity?
        get() = activity as? FullPlayerActivity

    private val device: Device?
        get() = rootActivity?.targetDevice

    private var lastPlayPauseClickTime = 0L

    /**
     * Might be [OnePlayerViewModel] or [PartyBoxPlayerViewModel]
     */
    private var playerViewModel: PlayerViewModel<*>? = null

    private var momentViewModel: MomentViewModel? = null

    private val _isTimerVisible = MutableLiveData<Boolean>()
    val isTimerVisible: LiveData<Boolean>
        get() = _isTimerVisible

    private val _isTickerVisible = MutableLiveData<Boolean>()
    val isTickerVisible: LiveData<Boolean>
        get() = _isTickerVisible

    private val _tickerTimeTxt = MutableLiveData<String?>()
    val tickerTimeTxt: LiveData<String?>
        get() = _tickerTimeTxt

    private val _musicServiceBrandImgRes = MutableLiveData<Int>()
    val musicServiceBrandImgRes: LiveData<Int>
        get() = _musicServiceBrandImgRes

    private val _inputSourceImgRes = MediatorLiveData<Int>()
    val inputSourceImgRes: LiveData<Int>
        get() = _inputSourceImgRes

    private val _qualityImgResource = MediatorLiveData<Int?>()
    val qualityImgResource: LiveData<Int?>
        get() = _qualityImgResource

    private val _title = MediatorLiveData<String>()
    val title: LiveData<String>
        get() = _title

    private val _subTitle = MediatorLiveData<String>()
    val subTitle: LiveData<String>
        get() = _subTitle

    private val _progress = MediatorLiveData<Int>()
    val progress: LiveData<Int>
        get() = _progress

    private val _currentTime = MutableLiveData<String?>()
    val currentTime: LiveData<String?>
        get() = _currentTime

    private val _lastTime = MediatorLiveData<String?>()
    val lastTime: LiveData<String?>
        get() = _lastTime

    private val _playPauseImgRes = MediatorLiveData<Int>()
    val playPauseImgRes: LiveData<Int>
        get() = _playPauseImgRes

    private val _deviceName = MutableLiveData<String?>()
    val deviceName: LiveData<String?>
        get() = _deviceName

    private val _albumCoverUrl = MutableLiveData<String?>()
    private val _albumCoverBitmap = MediatorLiveData<Bitmap?>()
    val albumCoverBitmap: LiveData<Bitmap?>
        get() = _albumCoverBitmap

    private val _isAlbumCoverLayoutVisible = MediatorLiveData<Boolean>()
    val isAlbumCoverLayoutVisible: LiveData<Boolean>
        get() = _isAlbumCoverLayoutVisible

    val isInputSourceLayoutVisible = isAlbumCoverLayoutVisible.map { !it }

    private val _ablePrevNext = MediatorLiveData<Boolean>()
    val ablePrevNext: LiveData<Boolean>
        get() = _ablePrevNext

    private val _visiblePrevNext = MediatorLiveData<Int>(View.VISIBLE)
    val visiblePrevNext: LiveData<Int> = _visiblePrevNext

    private val _ablePlayPause = MutableLiveData<Boolean>()
    val ablePlayPause: LiveData<Boolean?>
        get() = _ablePlayPause

    private val _isMomentMixWithMusicActive = MediatorLiveData<Boolean>(
        (device as? OneDevice)?.isMomentMixWithMusicActive() ?: false
    )
    val isMomentMixWithMusicActive: LiveData<Boolean>
        get() = _isMomentMixWithMusicActive

    private val _isMusicSourceVisible = MediatorLiveData<Boolean>()
    val isMusicSourceVisible: LiveData<Boolean>
        get() = _isMusicSourceVisible

    private val _bannerMomentBgRes = MutableLiveData<Int?>()
    val bannerMomentBgRes: LiveData<Int?>
        get() = _bannerMomentBgRes

    private val _bannerMomentBlcIcRes = MutableLiveData<Int?>()
    val bannerMomentBlcIcRes: LiveData<Int?>
        get() = _bannerMomentBlcIcRes

    private val _bannerMomentTxtRes = MutableLiveData<Int?>()
    val bannerMomentTxtRes: LiveData<Int?>
        get() = _bannerMomentTxtRes

    private val _isPlayTimeVisible = MediatorLiveData<Boolean>()
    val isPlayTimeVisible: LiveData<Boolean>
        get() = _isPlayTimeVisible

    private var autoPlayerOffDialog: AutoPlayerOffDialog? = null

    private var customizeMomentDialog: MusicPlayerMomentDialog? = null

    private val _enableSeek = MediatorLiveData<Boolean>()
    val enableSeek: LiveData<Boolean>
        get() =_enableSeek

    @Volatile
    private var isSeekBarTouching = false

    private val _isTransitioning = MutableLiveData<Boolean>()
    val isTransitioning: LiveData<Boolean>
        get() = _isTransitioning.distinctUntilChanged()

    private var moreDialog: MoreDialog? = null

    private val _isMoreEntranceVisible = MutableLiveData<Boolean>()
    val isMoreEntranceVisible: LiveData<Boolean>
        get() = _isMoreEntranceVisible

    private var customLoadingDialog: CustomLoadingDialog? = null

    private val _isQueueVisible = MutableLiveData<Boolean>()
    val isQueueVisible: LiveData<Boolean>
        get() = _isQueueVisible

    private var queueDialog: QueueDialog? = null

    /**
     * replay = 0 : no need for receiving old value from [volumeSetterStream] after collect.
     */
    private val volumeSetterStream = MutableSharedFlow<Int>(
        replay = 0,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    /**
     * Use for blocking delayed volume notification
     */
    private var lastVolumeBarTouchTime = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val fullPlayerActivity = rootActivity ?: return
        val device = device ?: return

        val viewModel = device.mapPlayerViewModel(owner = fullPlayerActivity) ?: run {
            Logger.e(TAG, "onCreate() >>> can't create player view model for device[${device.UUID}] pid[${device.pid}]")
            return
        }

        playerViewModel = viewModel
        fullPlayerActivity.lifecycle.addObserver(viewModel)

        if (device is OneDevice) {
            momentViewModel = ViewModelProvider(
                fullPlayerActivity,
                MomentViewModelFactory(device = device)
            )[MomentViewModel::class.java].also { vm ->
                fullPlayerActivity.lifecycle.addObserver(vm)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentOnePlayerBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this
        binding.fragment = this
        binding.tvTitle.setSelected(true)

        dataBinding(binding = binding)
        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()

        autoPlayerOffDialog?.dismiss()
        customizeMomentDialog?.dismiss()
        moreDialog?.dismiss()
        customLoadingDialog?.dismiss()
        queueDialog?.dismiss()

        playerViewModel?.let { viewModel ->
            rootActivity?.lifecycle?.removeObserver(viewModel)
        }

        momentViewModel?.let { vm ->
            rootActivity?.lifecycle?.removeObserver(vm)
        }
    }

    private fun loadAlbumCover(albumUrl: String?, musicService: EnumMusicServiceSource?) {
        _albumCoverBitmap.value = null
        Tools.loadAlbumCover(context = context, listener = object : BitmapLoadingListener {
            override fun onSuccess(bitmap: Bitmap?) {
                _albumCoverBitmap.value = bitmap
            }

            override fun onError() {
                _albumCoverBitmap.value = null
            }
        }, albumUrl = albumUrl, source = musicService, deviceItem = device?.wifiDevice?.deviceItem,
            width = 320, height = 320)
    }

    private fun dataBinding(binding: FragmentOnePlayerBinding) {
        val playerViewModel = playerViewModel ?: run {
            Logger.w(TAG, "dataBinding() >>> missing playerViewModel instance")
            return
        }

        playerViewModel.inputSource.observe(viewLifecycleOwner) { source ->
            Logger.d(TAG, "inputSource changed: source[$source]")
            _inputSourceImgRes.value = source.toLargeIconImgRes()

            when (source) {
                EnumInputSource.TV,
                EnumInputSource.HDMI -> {
                    // get streaming status for Dolby(atoms) status while input source is TV or HDMI
                    playerViewModel.getStreamingStatus()
                }

                else -> {
                    // no impl
                }
            }

            _ablePlayPause.value = when (source) {
                EnumInputSource.WIFI,
                EnumInputSource.BT,
                EnumInputSource.MOOD,
                EnumInputSource.USB -> true
                else -> false
            }
        }

        playerViewModel.musicServiceSource.observe(viewLifecycleOwner) { source ->
            _musicServiceBrandImgRes.value = source.toImgRes()
            _isMoreEntranceVisible.value = source.showMore()
        }

        _progress.safeAddSources(
            playerViewModel.inputSource,
            playerViewModel.playPercentage
        ) { source, percentage ->
            _progress.value = Tools.mapPlayerProgress(source = source, percentage = percentage)
        }

        playerViewModel.currentMills.observe(viewLifecycleOwner) { mills ->
            if (isSeekBarTouching) { // don't update current mills if user is seeking
                return@observe
            }

            _currentTime.value = mills.formatTimeAsMills()
        }

        _lastTime.safeAddSources(
            playerViewModel.currentMills,
            playerViewModel.durationMills
        ) { current, duration ->
            _lastTime.value = "-%s".format(
                if (null == current || null == duration) {
                    "0:00"
                } else {
                    (duration - current).formatTimeAsMills()
                }
            )
        }

        _playPauseImgRes.safeAddSources(
            playerViewModel.isPlaying,
            ablePlayPause
        ) { isPlaying, ablePlayPause ->
            _playPauseImgRes.value = if (true == ablePlayPause) {
                isPlaying.toPlayPauseImgRes()
            } else {
                R.drawable.ic_round_pause // keep pause icon if audio source was HDMI/TV/AUX
            }
        }

        val volumeWithMute = device?.volumeWithMute ?: 0

        binding.sbVolume.progress = volumeWithMute
        binding.icVolume.setImageResource(volumeWithMute.toVolumeImgRes())

        binding.sbVolume.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                Logger.d(TAG, "onProgressChanged() >>> progress[$progress] fromUser[$fromUser]")
                if (!fromUser) return
                binding.icVolume.setImageResource(progress.toVolumeImgRes())
                volumeSetterStream.tryEmit(progress.roundPercentage())
                lastVolumeBarTouchTime = System.currentTimeMillis()
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // no impl
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                // no impl
            }
        })

        playerViewModel.volume.observe(viewLifecycleOwner) { volume ->
            if (!lastVolumeBarTouchTime.overThreshold(VOLUME_SEEKBAR_DRAG_UPDATE_THRESHOLD)) {
                return@observe
            }

            binding.sbVolume.progress = volume
            binding.icVolume.setImageResource(volume.toVolumeImgRes())
        }

        _deviceName.value = playerViewModel.deviceName

        playerViewModel.albumCoverUrl.observe(viewLifecycleOwner) { url ->
            _albumCoverUrl.value = url
        }

        _isAlbumCoverLayoutVisible.safeAddSources(
            playerViewModel.inputSource,
            albumCoverBitmap
        ) { inputSource, bitmap ->
            _isAlbumCoverLayoutVisible.value =
                mapAlbumCoverLayoutVisible(inputSource = inputSource, bitmap = bitmap)
        }

        _albumCoverBitmap.safeAddSources(
            _albumCoverUrl,
            playerViewModel.musicServiceSource
        ) { albumUrl, musicService ->
            loadAlbumCover(albumUrl = albumUrl, musicService = musicService)
        }

        _title.safeAddSources(
            playerViewModel.songName,
            playerViewModel.inputSource
        ) { songName, inputSource ->
            _title.value = Tools.mapPlayerTitle(songName = songName, inputSource = inputSource)
        }

        _subTitle.safeAddSources(
            playerViewModel.artistName,
            playerViewModel.inputSource
        ) { artistName, inputSource ->
            _subTitle.value = Tools.mapPlayerSubTitle(artistName = artistName, inputSource = inputSource)
        }

        playerViewModel.isTimerSupport.observe(viewLifecycleOwner) { support ->
            _isTimerVisible.value = support
        }

        playerViewModel.sleepTimerRemainSecs.observe(viewLifecycleOwner) { secs ->
            _tickerTimeTxt.value = secs.formatTimeAsSecs()
        }

        playerViewModel.sleepTimerRemainSecs.observe(viewLifecycleOwner) { sleepTimerRemainSecs ->
            _isTickerVisible.value = null != sleepTimerRemainSecs && sleepTimerRemainSecs > 0
        }

        momentViewModel?.let { vm ->
            _isMomentMixWithMusicActive.safeAddSources(
                vm.momentVersion,
                vm.soundScapeV2State,
                vm.activeSoundscape,
                playerViewModel.isPlaying
            ) { version, state, soundScapeId, isPlaying ->
                _isMomentMixWithMusicActive.value =
                    Tools.isMomentMixWithMusicActive(
                        version = version,
                        momentPlayState = state,
                        soundScapeId = soundScapeId?.id,
                        isMusicPlaying = isPlaying ?: false
                    )
            }

            vm.activeSoundscape.observe(viewLifecycleOwner) { moment ->
                _bannerMomentBgRes.value = moment?.playerBannerBgDrawableRes
                _bannerMomentBlcIcRes.value = moment?.playerBannerBlcImgRes
                _bannerMomentTxtRes.value = moment?.playerBannerTxtRes
            }
        }

        _isMusicSourceVisible.safeAddSources(
            _isMomentMixWithMusicActive,
            playerViewModel.inputSource
        ) { isMomentMixWithMusicActive, inputSource ->
            _isMusicSourceVisible.value = mapMusicSourceVisible(
                isMomentMixWithMusicActive = isMomentMixWithMusicActive,
                inputSource = inputSource
            )
        }

        _isPlayTimeVisible.safeAddSources(
            playerViewModel.inputSource,
            playerViewModel.musicServiceSource
        ) { inputSource, musicServiceSource ->
            _isPlayTimeVisible.value = Tools.mapPlayTimeVisible(
                device = device,
                inputSource = inputSource,
                musicServiceSource = musicServiceSource
            )
        }

        _enableSeek.safeAddSources(
            playerViewModel.inputSource,
            playerViewModel.musicServiceSource
        ) { inputSource, musicServiceSource ->
            _enableSeek.value = mapEnableSeek(inputSource, musicServiceSource)
        }

        binding.seekbar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            private var last: Int = 0
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (!fromUser) return
                last = progress

                _progress.value = progress
                _currentTime.value = transCurrentTime(progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isSeekBarTouching = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isSeekBarTouching = false
                playerViewModel.seek(last)
            }
        })

        mediaQualityDataBinding(playerViewModel)

        playerViewModel.isTransitioning.observe(viewLifecycleOwner) { isTransitioning ->
            _isTransitioning.value = isTransitioning
        }

        _ablePrevNext.safeAddSources(playerViewModel.inputSource, playerViewModel.musicServiceSource) { inputSource, musicServiceSource ->
            _ablePrevNext.value = mapAblePrevNext(inputSource = inputSource, musicServiceSource = musicServiceSource)
        }

        _visiblePrevNext.safeAddSource(playerViewModel.inputSource) { inputSource ->
            Logger.w(TAG, "dataBinding>>> _visiblePrevNext:$inputSource")
            _visiblePrevNext.value = if (inputSource == EnumInputSource.MOOD) View.GONE else View.VISIBLE
        }

        if (playerViewModel is OnePlayerViewModel) {
            playerViewModel.musicServiceSource.observe(viewLifecycleOwner) { source ->
                _isQueueVisible.value = source.showQueue()
            }
        } else {
            _isQueueVisible.value = false
        }

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            volumeSetterStream
                .flowWithLifecycle(lifecycle = lifecycle)
                .sample(200L) // setVolume at most 200 ms per time
                .collect { volume ->
                    Logger.d(TAG, "volumeSetterStream() >>> setVolume[$volume]")
                    playerViewModel.setVolume(volume = volume)
                }
        }
    }

    /**
     * Disabled conditions:
     * @param [inputSource]
     * @param [musicServiceSource]
     */
    private fun mapAblePrevNext(inputSource: EnumInputSource?, musicServiceSource: EnumMusicServiceSource?): Boolean {
        return when (inputSource) {
            EnumInputSource.WIFI -> musicServiceSource.supportPrevNext()
            else -> inputSource.supportPrevNext()
        }
    }

    private fun mediaQualityDataBinding(playerViewModel: PlayerViewModel<*>) {
        fun mapMediaQuality(
            dolbyAtoms: Boolean? = playerViewModel.isSupportDolbyAtoms.value,
            dolbyAudio: Boolean? = playerViewModel.isSupportDolbyAudio.value,
            hd: Boolean? = playerViewModel.isSupportHD.value,
            uhd: Boolean? = playerViewModel.isSupportUHD.value,
            hiRes: Boolean? = playerViewModel.isSupportHiRes.value,
            cd: Boolean? = playerViewModel.isCDQuality.value,
            mp3: Boolean? = playerViewModel.isMp3Quality.value,
            high: Boolean? = playerViewModel.isHighQuality.value
        ) {
            _qualityImgResource.value = when {
                true == dolbyAtoms -> R.drawable.ic_dolby_atoms
                true == dolbyAudio -> R.drawable.ic_dolby_audio
                true == hd -> R.drawable.ic_hd
                true == uhd -> R.drawable.ic_uhd
                true == hiRes -> R.drawable.ic_high_res
                true == cd -> R.drawable.ic_cd
                true == mp3 -> R.drawable.ic_mp3
                true == high -> R.drawable.ic_high
                else -> null
            }
        }

        _qualityImgResource.safeAddSource(playerViewModel.isSupportDolbyAtoms) { dolbyAtoms ->
            mapMediaQuality(dolbyAtoms = dolbyAtoms)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isSupportDolbyAudio) { dolbyAudio ->
            mapMediaQuality(dolbyAudio = dolbyAudio)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isSupportHD) { hd ->
            mapMediaQuality(hd = hd)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isSupportUHD) { uhd ->
            mapMediaQuality(uhd = uhd)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isSupportHiRes) { hiRes ->
            mapMediaQuality(hiRes = hiRes)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isCDQuality) { cd ->
            mapMediaQuality(cd = cd)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isMp3Quality) { mp3 ->
            mapMediaQuality(mp3 = mp3)
        }

        _qualityImgResource.safeAddSource(playerViewModel.isHighQuality) { high ->
            mapMediaQuality(high = high)
        }
    }

    private fun transCurrentTime(progress: Int): String {
        val totalMills = playerViewModel?.durationMills?.value ?: return "0:00"
        return (totalMills * progress / 100).formatTimeAsMills()
    }

    private fun mapEnableSeek(
        inputSource: EnumInputSource?,
        musicServiceSource: EnumMusicServiceSource?
    ): Boolean {
        val dev = device
        Logger.d(TAG, "mapEnableSeek() >>> dev[${dev?.UUID}] platform[${dev?.platform}] " +
                "isWiFiOnline[${dev?.isWiFiOnline}] inputSource[$inputSource] " +
                "musicService[$musicServiceSource]")

        return dev is OneDevice &&
                dev.isWiFiOnline &&
                inputSource.supportSeek() &&
                musicServiceSource.supportSeek()
    }

    private fun mapMusicSourceVisible(
        isMomentMixWithMusicActive: Boolean?,
        inputSource: EnumInputSource?
    ): Boolean = true != isMomentMixWithMusicActive && EnumInputSource.WIFI == inputSource

    private fun mapAlbumCoverLayoutVisible(
        inputSource: EnumInputSource?,
        bitmap: Bitmap?
    ): Boolean = inputSource == EnumInputSource.WIFI && null != bitmap

    fun onBackBtnClick() {
        rootActivity?.finish()
    }

    fun onTimerClick() {
        val autoOffDevice = when (val device = playerViewModel?.device) {
            is OneDevice -> AutoPlayerOffDialog.AutoOffDevice.OneAutoOffDevice(device = device)
            is PartyBoxDevice -> AutoPlayerOffDialog.AutoOffDevice.PartyBoxAutoOffDevice(device = device)
            else -> return
        }

        val context = rootActivity ?: return

        autoPlayerOffDialog?.dismiss()
        autoPlayerOffDialog = AutoPlayerOffDialog(autoOffDevice = autoOffDevice, context = context).apply {
            show()
        }
    }

    fun onPlayPauseClick() {
        val currentTime = System.currentTimeMillis()
        if (currentTime > lastPlayPauseClickTime + 1000) {
            playerViewModel?.playOrPause()
            lastPlayPauseClickTime = currentTime
        }
    }

    fun onPreviousClick() {
        playerViewModel?.previous()
    }

    fun onNextClick() {
        playerViewModel?.next()
    }

    private fun EnumMusicServiceSource?.toImgRes(): Int? = when (this) {
        EnumMusicServiceSource.GOOGLE_CAST -> R.drawable.ic_fg_primary_google_cast_fixed_white
        EnumMusicServiceSource.SPOTIFY -> R.drawable.ic_fg_primary_spotify_fixed_white
        EnumMusicServiceSource.QOBUZ -> com.wifiaudio.R.drawable.sourcemanage_sourcehome_022_selected
        EnumMusicServiceSource.TIDAL -> R.drawable.ic_fg_primary_tidal_fixed_white
        EnumMusicServiceSource.IHEART -> com.wifiaudio.R.drawable.sourcemanage_sourcehome_008_selected
        EnumMusicServiceSource.AMAZON_PRIME -> com.wifiaudio.R.drawable.prime_music_icon_muzo2
        EnumMusicServiceSource.TUNEIN_NEW -> R.drawable.tunein_logo
        EnumMusicServiceSource.SOUNDMACHINE -> com.wifiaudio.R.drawable.sourcemanage_sourcehome_049
        EnumMusicServiceSource.CALM_RADIO -> com.wifiaudio.R.drawable.sourcemanage_sourcehome_050_default
        EnumMusicServiceSource.VTUNER -> com.wifiaudio.R.drawable.sourcemanage_sourcehome_018_selected
        EnumMusicServiceSource.NAPSTER -> com.wifiaudio.R.drawable.sourcemanage_sourcehome_016_selected
        EnumMusicServiceSource.QPLAY -> R.drawable.ic_fg_primary_qplay_fixed_white
        EnumMusicServiceSource.ROON_READY -> R.drawable.ic_fg_primary_roon_fixed_white
        EnumMusicServiceSource.AIR_PLAY -> R.drawable.ic_fg_primary_air_play_fixed_white
        else -> R.drawable.ic_fg_primary_dlna_fixed_white
    }

    private fun Boolean?.toPlayPauseImgRes(): Int =
        if (this == true) R.drawable.ic_round_pause else R.drawable.ic_round_play

    fun onMixWithMusicClick() {
        val oneDevice = device as? OneDevice ?: return
        val context = this.context ?: return
        val owner = this.rootActivity ?: return

        customizeMomentDialog?.dismiss()
        customizeMomentDialog = MusicPlayerMomentDialog(
            device = oneDevice,
            owner = owner,
            context = context
        ).also { dialog ->
            dialog.show()
        }
    }

    fun onAlbumCoverClick() {
        if (EnumMusicServiceSource.SPOTIFY == playerViewModel?.musicServiceSource?.value) {
            val intent = activity?.packageManager?.getLaunchIntentForPackage("com.spotify.music")
            Logger.d(TAG, "onAlbumCoverClick() >>> try to portal Spotify App. installed[${null != intent}]")

            if (null != intent) { // Google Home installed locally.
                startActivity(intent)
            } else { // Portal to Google Play.
                activity?.portalUrl(
                    config = AppConfigurationUtils.getGPSpotifyUrl(),
                    default = DEFAULT_GOOGLE_PLAY_SPOTIFY
                )
            }
        }
    }

    fun onMoreBtnClick() {
        val activity = activity ?: return
        val oneDevice = device as? OneDevice ?: return

        moreDialog?.dismiss()
        moreDialog = MoreDialog(activity = activity, device = oneDevice, listener = moreEventListener).apply {
            show()
        }
    }

    private val moreEventListener = object : IMoreEventListener {
        override fun onFavouriteBtnClick(trackSource: String?, isAlexaOrPandora: Boolean, isFavourite: Boolean) {
            handleMoreFavourite(trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora, isFavourite = isFavourite)
        }

        override fun onAddToPlaylistBtnClick(trackSource: String?, isAlexaOrPandora: Boolean) {
            handleAddToPlaylist(trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
        }

        override fun onAddToMyMusicBtnClick(trackSource: String?, isAlexaOrPandora: Boolean, isAddedMyMusic: Boolean) {
            handleAddMyMusic(trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora, isAddedMyMusic = isAddedMyMusic)
        }

        override fun onViewArtistBtnClick(trackSource: String?, isAlexaOrPandora: Boolean) {
            handleViewArtist(trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
        }

        override fun onViewAlbumBtnClick(trackSource: String?, isAlexaOrPandora: Boolean) {
            handleViewAlbum(trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
        }

        override fun onPlayTrackRadio(trackSource: String?, isAlexaOrPandora: Boolean) {
            handlePlayTrackRadio(trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
        }
    }

    private fun showLoadingDialog() {
        if (true == customLoadingDialog?.isShowing) return
        customLoadingDialog = CustomLoadingDialog(context = context ?: return, cancelable = false).apply {
            show()
        }
    }

    private fun handleMoreFavourite(trackSource: String?, isAlexaOrPandora: Boolean, isFavourite: Boolean) {
        Logger.d(TAG, "handleMoreFavourite() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora] isFavourite[$isFavourite]")
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            showLoadingDialog()
            val rst = withContext(DISPATCHER_API) {
                if (isFavourite) {
                    MoreHelper.removeFromFavourite(logTag = TAG, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
                } else {
                    MoreHelper.addToFavourite(logTag = TAG, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
                }
            }

            Logger.d(TAG, "handleMoreFavourite() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora] isFavourite[$isFavourite] favourite.rst[$rst]")
            customLoadingDialog?.dismiss()

            if (rst) {
                CustomToast.showSuccess(
                    textRes = if (isFavourite) {
                        R.string.harmanbar_newtuneIn_Removed_Favorite_Successfully
                    } else {
                        R.string.harmanbar_content_Added_successfully
                    }
                )
            } else {
                CustomToast.showError(
                    textRes = if (isFavourite) {
                        R.string.harmanbar_newtuneIn_Removed_Favorite_Fail
                    } else {
                        R.string.harmanbar_content_Added_failed
                    }
                )
            }
        }
    }

    private fun handleAddToPlaylist(trackSource: String?, isAlexaOrPandora: Boolean) {
        Logger.d(TAG, "handleAddToPlaylist() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora]")
        MoreHelper.addToPlaylist(logTag = TAG, activity = activity ?: return, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
    }

    private fun handleAddMyMusic(trackSource: String?, isAlexaOrPandora: Boolean, isAddedMyMusic: Boolean) {
        Logger.d(TAG, "handleAddMyMusic() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora] isAddedMyMusic[$isAddedMyMusic]")
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            showLoadingDialog()

            val rst = withContext(DISPATCHER_API) {
                if (isAddedMyMusic) {
                    MoreHelper.removeFromMyMusic(logTag = TAG, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
                } else {
                    MoreHelper.addToMyMusic(logTag = TAG, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
                }
            }

            Logger.d(TAG, "handleAddMyMusic() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora] isAddedMyMusic[$isAddedMyMusic] myMusic.rst[$rst]")
            customLoadingDialog?.dismiss()

            if (rst) {
                CustomToast.showSuccess(
                    textRes = if (isAddedMyMusic) {
                        R.string.removed_successfully
                    } else {
                        R.string.harmanbar_content_Added_successfully
                    }
                )
            } else {
                CustomToast.showError(
                    textRes = if (isAddedMyMusic) {
                        R.string.failed_to_remove
                    } else {
                        R.string.failed_to_add
                    }
                )
            }
        }
    }

    private fun handleViewArtist(trackSource: String?, isAlexaOrPandora: Boolean) {
        Logger.d(TAG, "handleViewArtist() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora]")
        MoreHelper.viewArtist(logTag = TAG, activity = activity ?: return, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
    }

    private fun handleViewAlbum(trackSource: String?, isAlexaOrPandora: Boolean) {
        Logger.d(TAG, "handleViewArtist() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora]")
        MoreHelper.viewAlbum(logTag = TAG, activity = activity ?: return, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
    }

    private fun handlePlayTrackRadio(trackSource: String?, isAlexaOrPandora: Boolean) {
        Logger.d(TAG, "handlePlayTrackRadio() >>> track[$trackSource] isAlexaOrPandora[$isAlexaOrPandora]")
        lifecycleScope.launch(DISPATCHER_API) {
            MoreHelper.playTrackRadio(logTag = TAG, trackSource = trackSource, isAlexaOrPandora = isAlexaOrPandora)
        }
    }

    fun onQueueClick() {
        val oneDevice = device as? OneDevice ?: return

        queueDialog?.dismiss()
        queueDialog = QueueDialog(context = context ?: return, device = oneDevice).apply {
            show()
        }
    }

    companion object {
        private const val TAG = "FullPlayerFragment"
    }

}
