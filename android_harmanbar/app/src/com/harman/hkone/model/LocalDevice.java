package com.harman.hkone.model;

import java.io.Serializable;

public class LocalDevice implements Serializable {
    private static final long serialVersionUID = -9162596923348659381L;
    private String p2p_mac;
    private String wlan0_mac;
    private String serial_number;

    public String getP2p_mac() {
        return p2p_mac;
    }

    public void setP2p_mac(String p2p_mac) {
        this.p2p_mac = p2p_mac;
    }

    public String getWlan0_mac() {
        return wlan0_mac;
    }

    public void setWlan0_mac(String wlan0_mac) {
        this.wlan0_mac = wlan0_mac;
    }

    public String getSerial_number() {
        return serial_number;
    }

    public void setSerial_number(String serial_number) {
        this.serial_number = serial_number;
    }

    @Override
    public String toString() {
        return "LocalDevice{" +
                "p2p_mac='" + p2p_mac + '\'' +
                ", wlan0_mac='" + wlan0_mac + '\'' +
                ", serial_number='" + serial_number + '\'' +
                '}';
    }
}

