package com.harman

import android.content.Context
import com.wifiaudio.app.LinkplayApplication

/**
 * 有些数据在启动app后的操作中，只需要发送一次，
 * 所以缓存一个记录，下次启动App时在清掉之前的记录重新记录
 */
class FirebaseLogEventSharePreference {

    companion object{

        private val Firebase_Log_Event_Status_SP = "firebase_log_event_status"
        private val pref = LinkplayApplication.me.getSharedPreferences(Firebase_Log_Event_Status_SP, Context.MODE_PRIVATE)

        /**
         * 设置设备的某个事件类型是否需要上报
         * isUpload是否上报
         */
        fun setLogEventUpload(uuid: String, event_type: String, isUpload: Boolean) {
            val editor = pref.edit()
            editor.putBoolean(event_type + uuid, isUpload)
            editor.apply()
        }

        /**
         * 获取设备的某个事件类型是否需要上报
         * 默认是需要上报
         */
        fun getLogEventUpload(uuid: String,event_type: String): Boolean? {
            return pref.getBoolean(event_type + uuid, true)
        }

        /**
         * 清空之前的记录
         */
        fun clearLogEventUpload(){
            val editor = pref.edit()
            editor.clear()
            editor.apply()
        }
    }
}