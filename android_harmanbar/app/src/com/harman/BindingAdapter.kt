package com.harman

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.drawable.Drawable
import android.text.Html
import android.text.InputFilter
import android.text.InputType
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextWatcher
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.widget.CheckBox
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.IntRange
import androidx.annotation.RawRes
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.LayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.LottieDrawable
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.SimpleColorFilter
import com.airbnb.lottie.model.KeyPath
import com.airbnb.lottie.value.LottieValueCallback
import com.blankj.utilcode.util.ResourceUtils
import com.blankj.utilcode.util.SizeUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.harman.auracast.AuraCastMoreNearbyDeviceAdapter
import com.harman.auracast.AuraCastMoreNearbyDeviceListDecoration
import com.harman.auracast.AuraCastNearbyDeviceAdapter
import com.harman.auracast.AuraCastNearbyDeviceListDecoration
import com.harman.auracast.widget.AuraCastGuideModel
import com.harman.auracast.widget.EnumAuraCastOffState
import com.harman.bar.app.R
import com.harman.command.one.bean.APItem
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.connect.EnumConnectionStatus
import com.harman.customer.service.DiagnosisReportProductUIBean
import com.harman.customer.service.DiagnosisReportProductsAdapter
import com.harman.customer.service.ErrorType
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.bean.PortableDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.PartyLightBTDevice
import com.harman.discover.bean.bt.PortableBTDevice
import com.harman.discover.filter.pid2ModelName
import com.harman.discover.util.Tools.isBroadcasterOn
import com.harman.discover.util.Tools.macAddress
import com.harman.discover.util.Tools.roundPercentage
import com.harman.hkone.AuracastOffView
import com.harman.hkone.AuracastOnView
import com.harman.hkone.BatteryView
import com.harman.hkone.DeviceImageUtil
import com.harman.hkone.HMPagerIndicator
import com.harman.hkone.MultiChannelSpeakerView
import com.harman.hkone.ScreenUtil
import com.harman.log.Logger
import com.harman.moment.BaseMomentAdapter
import com.harman.moment.EnumMoment
import com.harman.moment.IMomentElementDataListener
import com.harman.moment.MomentActivity
import com.harman.moment.ProgressBarData
import com.harman.moment.ScaleInTransformer
import com.harman.multichannel.HarmancastManager
import com.harman.music.Tools
import com.harman.music.Tools.toIconResource
import com.harman.music.Tools.toSubIconResource
import com.harman.music.more.ItemQueue
import com.harman.music.service.EnumMusicServiceSource
import com.harman.music.service.MusicServiceListDecoration
import com.harman.oobe.wifi.APListAdapter
import com.harman.oobe.wifi.SecurityAdapter
import com.harman.oobe.wifi.flow.SelfTuningUIState
import com.harman.ota.partylight.PartyLightListDecoration
import com.harman.ota.partylight.PartyLightOtaListAdapter
import com.harman.partybox.EnumChannelMode
import com.harman.partybox.PartyBoxTwsActivity
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible
import com.harman.product.list.CustomItemAnimator
import com.harman.product.list.MyProductsAdapter
import com.harman.product.list.bean.BatteryAreaUiBean
import com.harman.radio.bean.FMStationListBean.Companion.toFMString
import com.harman.streaming.google.IGoogleCastDialog
import com.harman.task.partylight.EnumPartyLightDfuStatus
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.widget.CollapsingToolBar
import com.harman.widget.ComponentButtonDoubleMini
import com.harman.widget.ComponentCardStandardItem
import com.harman.widget.CustomTypefaceSpan
import com.harman.widget.DJEffectBar
import com.harman.widget.HmCustomVerticalSeekBar
import com.harman.widget.RoundCornerButton
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.AuthButton
import com.jbl.one.configuration.model.CombinationItem
import com.jbl.one.configuration.model.HmAuraCastPartyIcon
import com.skin.SkinResourcesID
import com.skin.SkinResourcesUtils
import com.utils.glide.BitmapLoadingListener
import com.utils.glide.GlideMgtUtil
import com.utils.glide.ImageLoadConfig
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.SecurityItem
import com.wifiaudio.model.local_music.MainItem
import com.wifiaudio.utils.ImageLoadUtil
import com.wifiaudio.utils.MixTextImg
import com.wifiaudio.utils.StringSpannableUtils
import com.wifiaudio.view.component.ButtonStyle
import com.wifiaudio.view.component.CircleProgress
import com.wifiaudio.view.component.ComponentButton
import com.wifiaudio.view.component.ComponentConfig
import com.wifiaudio.view.component.ComponentLoadingButton
import com.wifiaudio.view.component.ComponentTextProgressBar
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.LinkedList
import java.util.Queue
import kotlin.math.max


/**
 * Created by gerrardzhang on 2024/2/26.
 */
@SuppressLint("SetTextI18n")
@BindingAdapter("deviceName")
fun deviceName(textView: TextView, device: Device?) {
    device ?: return
    textView.text = device.displayDeviceName()
}

@SuppressLint("SetTextI18n")
@BindingAdapter("pid")
fun devicePid(textView: TextView, device: Device) {
    textView.text =
        "Pid|Pname:${device.bleDevice?.pid ?: "[null]"}  ${device.bleDevice?.pid?.pid2ModelName() ?: "[null]"}"
}

@SuppressLint("SetTextI18n")
@BindingAdapter("uuid")
fun deviceUuid(textView: TextView, device: Device) {
    textView.text = "UUID:${device.UUID}"
}

@SuppressLint("SetTextI18n")
@BindingAdapter("bleAddress")
fun deviceBleAddress(textView: TextView, device: Device) {
    textView.text = "BLE:${device.bleDevice?.bleAddress ?: "[null]"}"
}

@SuppressLint("SetTextI18n")
@BindingAdapter("macAddress")
fun deviceMACAddress(textView: TextView, device: Device) {
    textView.text =
        "MAC:${device.bleDevice?.macAddress ?: device.wifiDevice?.deviceItem?.macAddress() ?: "[null]"}"
}

@BindingAdapter(
    value = [
        "bind:adapter",
        "bind:orientation" // RecyclerView.HORIZONTAL RecyclerView.VERTICAL
    ]
)
fun setAdapter(recyclerView: RecyclerView, adapter: BaseAdapter<*, *>?, orientation: Int?) {
    orientation ?: return
    recyclerView.adapter = adapter ?: return

    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }

    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        orientation,
        false
    )
}

@BindingAdapter(
    value = [
        "bind:adapter",
        "bind:orientation" // RecyclerView.HORIZONTAL RecyclerView.VERTICAL
    ]
)
fun setMultiAdapter(recyclerView: RecyclerView, adapter: BaseMultiAdapter<*>?, orientation: Int?) {
    orientation ?: return
    recyclerView.adapter = adapter ?: return

    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }

    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        orientation,
        false
    )
}

@BindingAdapter("setMultiAdapter")
fun setMultiAdapter(recyclerView: RecyclerView, adapter: BaseMultiAdapter<*>?) {
    recyclerView.adapter = adapter ?: return
}

@BindingAdapter("submitList")
fun submitList(recyclerView: RecyclerView, list: List<Device>?) {
    val adapter = (recyclerView.adapter as? BaseAdapter<*, Device>) ?: return
    adapter.updateData(list ?: emptyList())
}

@BindingAdapter("submitMultiList")
fun <Data> submitMultiList(recyclerView: RecyclerView, list: List<Data>?) {
    val adapter = (recyclerView.adapter as? BaseMultiAdapter<Data>) ?: return
    adapter.updateData(list ?: emptyList())
}

@BindingAdapter("submitStatus")
fun submitStatus(recyclerView: RecyclerView, list: List<EnumPartyLightDfuStatus>?) {
    val adapter = recyclerView.adapter as? PartyLightOtaItemAdapter ?: return
    adapter.updateStatus(list ?: emptyList())
}

@BindingAdapter("decoPlatform")
fun decoPlatform(
    imageView: ImageView,
    device: Device
) {
    imageView.setImageResource(
        if (device.isWiFiOnline) {
            com.wifiaudio.R.drawable.icon
        } else if (device.bleDevice is OneBTDevice) {
            com.wifiaudio.R.drawable.icon
        } else if (device.bleDevice is PortableBTDevice) {
            R.drawable.portable_app_icon
        } else if (device.bleDevice is PartyBoxBTDevice) {
            R.drawable.partybox_app_icon
        } else if (device.bleDevice is PartyLightBTDevice) {
            R.drawable.partybox_app_icon
        } else {
            0
        }
    )
}

@BindingAdapter("bleConnectionStatus")
fun bleConnectionStatus(textView: TextView, status: EnumConnectionStatus?) {
    status ?: return

    textView.text = status.name
    textView.setTextColor(
        textView.context.resources.getColor(
            when (status) {
                EnumConnectionStatus.PAIR_FAILED,
                EnumConnectionStatus.DISCONNECTED -> R.color.red_1
                EnumConnectionStatus.PRE_PAIR,
                EnumConnectionStatus.PAIRING,
                EnumConnectionStatus.CONNECTING -> R.color.orange_2
                EnumConnectionStatus.PAIRED,
                EnumConnectionStatus.CONNECTED -> R.color.fg_activate
                else -> R.color.fg_activate
            }
        )
    )
}

@BindingAdapter("updatePayloads")
fun updatePayloads(textView: TextView, queue: Queue<String>?) {
    queue ?: run {
        textView.text = ""
        return
    }

    val innerQueue = LinkedList(queue)
    val sb = StringBuilder()

    innerQueue.reversed().forEachIndexed { index, payload ->
        sb.append("-------- [$index] --------\n").append(payload)

        if (index < innerQueue.size - 1) {
            sb.append("\n\n")
        }
    }

    textView.text = sb.toString()
}

@BindingAdapter("anyPartyLightDevice")
fun anyPartyLightDevice(v: View, devices: List<Device>?) {
    v.isVisible = devices?.any { device -> device is PartyLightDevice } ?: false
}

@SuppressLint("SetTextI18n")
@BindingAdapter("percentage")
fun percentage(v: TextView, percentage: Int) {
    v.text = "$percentage%"
}

@BindingAdapter("imageText")
fun imageText(tv: TextView, textKeyName: String, imageKeyName: String) {
    val resID = SkinResourcesID.getDrawableId(imageKeyName)
    tv.text = Html.fromHtml(
        String.format(
            SkinResourcesUtils.getString(textKeyName),
            "<img src='${resID}'/>"
        ),
        MixTextImg.getImageGetterInstance(
            tv.context, false,
            WAApplication.mResources.getDimension(R.dimen.width_24).toInt(),
            WAApplication.mResources.getDimension(R.dimen.width_24).toInt()
        ), null
    )
}

@BindingAdapter("unselectedSignal")
fun unselectedSignal(iv: ImageView, apItem: APItem?) {
    val rssi = apItem?.rssi ?: return

    iv.setImageResource(
        when {
            (rssi in 0 until 25) -> R.drawable.ic_wifi_level_1
            (rssi in 25 until 90) -> R.drawable.ic_wifi_level_2
            else -> R.drawable.ic_wifi_level_3
        }
    )
}

@BindingAdapter("selectedSignal")
fun selectedSignal(iv: ImageView, apItem: APItem?) {
    val rssi = apItem?.rssi ?: return

    iv.setImageResource(
        when {
            (rssi in 0 until 25) -> R.drawable.ic_wifi_level_1_selected
            (rssi in 25 until 90) -> R.drawable.ic_wifi_level_2_selected
            else -> R.drawable.ic_wifi_level_3_selected
        }
    )
}

@BindingAdapter("submitAPList")
fun submitAPList(recyclerView: RecyclerView, apList: List<APItem>?) {
    val adapter = recyclerView.adapter as? APListAdapter ?: return
    adapter.updateData(apList)
}

@BindingAdapter("setAPListAdapter")
fun setAPListAdapter(recyclerView: RecyclerView, adapter: APListAdapter?) {
    // remove flash anim after invoke notifyItemChanged
    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }
    recyclerView.itemAnimator = null
    recyclerView.adapter = adapter ?: return
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
}

@BindingAdapter("submitSecurityList")
fun submitSecurityList(recyclerView: RecyclerView, apList: List<SecurityItem>?) {
    val adapter = recyclerView.adapter as? SecurityAdapter ?: return
    adapter.updateData(apList)
}

@BindingAdapter("setSecurityAdapter")
fun setSecurityAdapter(recyclerView: RecyclerView, adapter: SecurityAdapter?) {
    // remove flash anim after invoke notifyItemChanged
    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }
    recyclerView.itemAnimator = null
    recyclerView.adapter = adapter ?: return
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
}

@BindingAdapter("isPasswordVisible")
fun isPasswordVisible(et: EditText, visible: Boolean) {
    et.inputType = InputType.TYPE_CLASS_TEXT or if (visible) {
        InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
    } else {
        InputType.TYPE_TEXT_VARIATION_PASSWORD
    }

    et.setSelection(et.length())
}

@BindingAdapter("decoComponentButtonEnable")
fun decoComponentButtonEnable(btn: ComponentButton, enable: Boolean?) {
    btn.isEnabled = enable ?: false
    btn.setBtnType(if (enable == true) ComponentConfig.BTN_REGULAR else ComponentConfig.BTN_DISABLE)
}

@BindingAdapter("decoEnable")
fun decoEnable(v: View, enable: Boolean?) {
    v.isEnabled = enable ?: false
    v.alpha = if (true == enable) 1.0f else 0.5f
}

@BindingAdapter("setBtnEnabled")
fun setBtnEnabled(cb: ComponentButton, enable: Boolean?) {
    cb.setBtnEnabled(enable ?: false)
}

@BindingAdapter("decoAPName")
fun decoAPName(tv: TextView, apName: String?) {
    tv.text =
        tv.resources.getString(R.string.harmanbar_jbl_Your_speaker_is_connected_to_network________successfully_)
            .format(apName)
}

@BindingAdapter("decoPasswordError")
fun decoPasswordError(tv: TextView, apName: String?) {
    tv.text = tv.resources.getString(R.string.harmanbar_jbl_Invalid_password_for_____please_retry_)
        .format(apName)
}

@BindingAdapter(
    value = [
        "bind:selectedAP",
        "bind:selfAP",
        "bind:showSelected"
    ]
)
fun apItemVisible(v: View, selectedAP: APItem?, selfAP: APItem?, showSelected: Boolean) {
    v.isVisible = if (showSelected) {
        null != selectedAP && null != selfAP && selectedAP == selfAP
    } else {
        null == selectedAP || null == selfAP || selectedAP != selfAP
    }
}

@BindingAdapter("decoOobeChromecastAvailableTitle2")
fun decoOobeChromecastAvailableTitle2(tv: TextView, iGoogleCast: IGoogleCastDialog?) {
    val strHighLabel1 = tv.resources.getString(R.string.jbl_google_terms_of_service_no_quotes).replace("\"", "")
    val strHighLabel2 = tv.resources.getString(R.string.jbl_privacy_policy_no_quotes).replace("\"", "")
    val strInfoLabel =
        tv.resources.getString(R.string.google_cast_terms_of_service_and_google_privacy_policy)
    val linkColor = tv.resources.getColor(R.color.fg_activate)

    val stringUtil = StringSpannableUtils().apply {
        setTextBody(strInfoLabel)
        setTextHighlight(arrayOf(strHighLabel1, strHighLabel2), linkColor)
        setHighTxtTypeface(ResourcesCompat.getFont(tv.context, R.font.poppins_bold))

        setSpannableStrings { position ->
            when (position) {
                0 -> iGoogleCast?.onGoogleTOSClick()
                1 -> iGoogleCast?.onGooglePrivacyPolicyClick()
            }
        }
    }

    with(tv) {
        text = stringUtil.spannableString
        typeface = ResourcesCompat.getFont(tv.context, R.font.poppins_regular)
        highlightColor = Color.TRANSPARENT
        setTextColor(tv.resources.getColor(R.color.fg_secondary))
        setTextSize(0, tv.resources.getDimension(R.dimen.font_12sp))
        movementMethod = LinkMovementMethod.getInstance()
    }
}

@BindingAdapter("decoOobeChromecastAvailableTitle3")
fun decoOobeChromecastAvailableTitle3(tv: TextView, iGoogleCast: IGoogleCastDialog?) {
    val strHighLabel = tv.resources.getString(R.string.text_here)
    val strInfoLabel = tv.resources.getString(R.string.tap_here_to_enable_later)
    val linkColor = tv.resources.getColor(R.color.fg_activate)

    val stringUtil = StringSpannableUtils().apply {
        setTextBody(strInfoLabel)
        setTextHighlight(strHighLabel, linkColor)
        setHighTxtTypeface(ResourcesCompat.getFont(tv.context, R.font.poppins_bold))

        setSpannableString {
            iGoogleCast?.onGoogleCastLater()
        }
    }

    with(tv) {
        text = stringUtil.spannableString
        typeface = ResourcesCompat.getFont(tv.context, R.font.poppins_regular)
        highlightColor = Color.TRANSPARENT
        setTextColor(tv.resources.getColor(R.color.fg_secondary))
        setTextSize(0, tv.resources.getDimension(R.dimen.font_12sp))
        movementMethod = LinkMovementMethod.getInstance()
    }
}

@BindingAdapter("deviceImgForce")
fun deviceImgForce(iv: ImageView, device: Device?) {
    DeviceImageUtil.loadAsBitmap(
        iv = iv,
        imgPath = device.deviceImgPath(),
        placeHolder = R.drawable.ic_device_fg_disable,
        error = R.drawable.ic_device_fg_disable,
        scaleType = ImageView.ScaleType.FIT_CENTER,
        pid = device?.pid,
        hasFadeInEffect = true
    )
}

@BindingAdapter("auraCastDeviceImgForce")
fun auraCastDeviceImgForce(iv: ImageView, device: Device?) {
    DeviceImageUtil.loadAsBitmap(
        iv = iv,
        imgPath = device.deviceAuraCastImgPath(),
        placeHolder = R.drawable.ic_device_fg_disable_small,
        error = R.drawable.ic_device_fg_disable_small,
        scaleType = ImageView.ScaleType.FIT_CENTER,
        pid = device?.pid,
        hasFadeInEffect = true,
        isAuraCast = true
    )
}

@BindingAdapter("loadImage")
fun loadImage(iv: ImageView, imgPath: String?) {
    DeviceImageUtil.loadAsBitmap(iv = iv, imgPath = imgPath, placeHolder = null, error = null, scaleType = ImageView.ScaleType.CENTER_INSIDE)
}

@SuppressLint("SetTextI18n")
@BindingAdapter("formatProgress")
fun formatProgress(progressBar: ComponentTextProgressBar, progress: Int?) {
    progressBar.setProgress((progress ?: 0).toFloat(), "${progress ?: 0} %")
}

@BindingAdapter("remainingMinutes")
fun remainingMinutes(tv: TextView, minutes: Int?) {
    if (null == minutes || minutes < 0) {
        return
    }

    tv.text = tv.resources.getString(R.string.min_remaining).format(minutes)
}

@BindingAdapter("targetVersion")
fun targetVersion(tv: TextView, version: String?) {
    tv.text = tv.resources.getString(R.string.harmanbar_jbl_Current_version___).format(version)
}

@BindingAdapter("alertVisible")
fun alertVisible(v: View, style: com.harman.ota.partybox.ViewStyle) {
    v.isVisible = when (style) {
        com.harman.ota.partybox.ViewStyle.TransError -> true
        com.harman.ota.partybox.ViewStyle.ReconnectError -> true
        com.harman.ota.partybox.ViewStyle.UpdateFail -> true
        else -> false
    }
}

@BindingAdapter("maskVisible")
fun maskVisible(v: View, style: com.harman.ota.partybox.ViewStyle) {
    v.alpha = when (style) {
        com.harman.ota.partybox.ViewStyle.TransError -> 0.5f
        com.harman.ota.partybox.ViewStyle.ReconnectError -> 0.5f
        com.harman.ota.partybox.ViewStyle.UpdateFail -> 0.5f
        else -> 1.0f
    }
}

@SuppressLint("SetTextI18n")
@BindingAdapter("whatsNewTitle")
fun whatsNewTitle(tv: TextView, version: String?) {
    tv.text = tv.resources.getString(R.string.jbl_Version) + " $version"
}

@BindingAdapter("loadingStyle1")
fun loadingStyle1(iv: ImageView, loading: Boolean?) {
    if (true == loading && null == iv.animation) {
        val anim = AnimationUtils.loadAnimation(iv.context, R.anim.anim_loading_1)
        anim.repeatCount = Animation.INFINITE
        anim.interpolator = LinearInterpolator()
        iv.startAnimation(anim)
    } else if (false == loading) {
        iv.animation?.cancel()
        iv.animation = null
    }
}

@BindingAdapter("loadingStyle2")
fun loadingStyle2(iv: ImageView, loading: Boolean?) {
    if (true == loading && null == iv.animation) {
        val anim = AnimationUtils.loadAnimation(iv.context, R.anim.anim_loading_fg_primary)
        anim.interpolator = LinearInterpolator()
        anim.repeatCount = Animation.INFINITE
        iv.startAnimation(anim)
    } else if (false == loading && null != iv.animation) {
        iv.tag = null
        iv.clearAnimation()
    }
}

@BindingAdapter("loadingStyle3")
fun loadingStyle3(iv: ImageView, loading: Boolean?) {
    if (true == loading && null == iv.animation) {
        val anim = AnimationUtils.loadAnimation(iv.context, R.anim.anim_loading_3)
        anim.repeatCount = Animation.INFINITE
        anim.interpolator = LinearInterpolator()
        iv.startAnimation(anim)
    } else if (false == loading) {
        iv.animation?.cancel()
        iv.animation = null
    }
}

@BindingAdapter("setPartyLightOtaAdapter")
fun setPartyLightOtaAdapter(recyclerView: RecyclerView, adapter: PartyLightOtaListAdapter?) {
    // remove flash anim after invoke notifyItemChanged
    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }
    recyclerView.itemAnimator = null

    recyclerView.adapter = adapter ?: return
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
    recyclerView.addItemDecoration(PartyLightListDecoration(adapter = adapter))
}

@BindingAdapter(
    value = [
        "bind:devices",
        "bind:completes"
    ]
)
fun submitPartyLightDeviceList(
    recyclerView: RecyclerView,
    devices: List<PartyLightDevice>?,
    completes: List<PartyLightDevice>?
) {
    val adapter = (recyclerView.adapter as? PartyLightOtaListAdapter) ?: return
    adapter.updateData(
        devices = devices ?: emptyList(),
        completes = completes ?: emptyList()
    )
}

@BindingAdapter(
    value = [
        "bind:device",
        "bind:fails",
        "bind:completes"
    ]
)
fun decoCustomInfo(
    tv: TextView,
    device: PartyLightDevice?,
    fails: List<PartyLightDevice>?,
    completes: List<PartyLightDevice>?
) {
    device ?: return

    when {
        true == fails?.contains(device) -> {
            tv.text = tv.resources.getText(com.wifiaudio.R.string.muzoa98_newadddevice_Update_failed)
            tv.setTextColor(ContextCompat.getColor(tv.context, R.color.red_1))
        }

        true == completes?.contains(device) -> {
            tv.text = tv.resources.getText(R.string.version_with_num)
                .toString()
                .format(device.firmwareVersion)
            tv.setTextColor(ContextCompat.getColor(tv.context, R.color.fg_activate))
        }

        else -> {
            tv.text = tv.resources.getText(R.string.version_with_num)
                .toString()
                .format(device.firmwareVersion)
            tv.setTextColor(ContextCompat.getColor(tv.context, R.color.fg_secondary))
        }
    }
}

@BindingAdapter("liveProgress")
fun liveProgress(circleProgress: CircleProgress, progress: Int?) {
    circleProgress.progress = (progress?.roundPercentage() ?: 0).toFloat()
}

@BindingAdapter(
    value = [
        "bind:device",
        "bind:trans"
    ]
)
fun upgradeProgressVisible(
    circleProgress: CircleProgress,
    device: PartyLightDevice?,
    trans: PartyLightDevice?
) {
    circleProgress.isVisible = null != device && null != trans && device == trans
}

@BindingAdapter(
    value = [
        "bind:device",
        "bind:waitings"
    ]
)
fun waitingDevices(
    iv: ImageView,
    device: PartyLightDevice?,
    waitings: List<PartyLightDevice>?
) {
    val visible = null != device && null != waitings && waitings.contains(device)

    iv.isVisible = visible
    loadingStyle2(iv = iv, loading = visible)
}

@BindingAdapter(
    value = [
        "bind:device",
        "bind:failDevices"
    ]
)
fun failDevices(
    tv: TextView,
    device: PartyLightDevice?,
    fails: List<PartyLightDevice>?
) {
    tv.isVisible = null != device && null != fails && fails.contains(device)
}

@BindingAdapter("lottie")
fun lottie(lottieView: LottieAnimationView, res: String?) {
    if (res.isNullOrBlank()) {
        lottieView.cancelAnimation()
        return
    }

    lottieView.setAnimation(res)
    lottieView.playAnimation()
}

@BindingAdapter("repeatCount")
fun repeatCount(lottieView: LottieAnimationView, count: Int?) {
    count ?: return
    lottieView.repeatCount = count
}

@BindingAdapter("calibrationAnim")
fun calibrationAnim(lottieView: LottieAnimationView, state: SelfTuningUIState?) {
    when (state) {
        SelfTuningUIState.DOING -> {
            with(lottieView) {
                isVisible = true
                setAnimation("oobe_calibration_bubble.json")
                repeatMode = LottieDrawable.REVERSE
                repeatCount = LottieDrawable.INFINITE
                playAnimation()
            }
        }

        SelfTuningUIState.DONE -> {
            with(lottieView) {
                isVisible = true
                repeatMode = LottieDrawable.REVERSE
                repeatCount = LottieDrawable.INFINITE
                progress = 1.0f
                cancelAnimation()
            }
        }

        else -> {
            lottieView.isVisible = false
        }
    }
}

@BindingAdapter("alertVisible")
fun alertVisible(v: View, style: com.harman.ota.one.ViewStyle) {
    v.isVisible = when (style) {
        com.harman.ota.one.ViewStyle.TransError -> true
        com.harman.ota.one.ViewStyle.ReconnectError -> true
        com.harman.ota.one.ViewStyle.UpdateFail -> true
        else -> false
    }
}

@BindingAdapter("maskVisible")
fun maskVisible(v: View, style: com.harman.ota.one.ViewStyle) {
    v.alpha = when (style) {
        com.harman.ota.one.ViewStyle.TransError -> 0.5f
        com.harman.ota.one.ViewStyle.ReconnectError -> 0.5f
        com.harman.ota.one.ViewStyle.UpdateFail -> 0.5f
        else -> 1.0f
    }
}

@BindingAdapter("descEstimateUpdateTime")
fun descEstimateUpdateTime(tv: TextView, devices: List<PartyLightDevice>?) {
    tv.text =
        tv.resources.getString(R.string.estimated_update_time).format((devices?.size ?: 0) * 2)
}

@BindingAdapter("setMyProductsAdapter")
fun setMyProductsAdapter(recyclerView: RecyclerView, adapter: MyProductsAdapter?) {
    recyclerView.adapter = adapter ?: return
    recyclerView.itemAnimator = CustomItemAnimator()
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
}

@BindingAdapter("decoPermissionItem")
fun decoPermissionItem(vg: ViewGroup, allowed: Boolean) {
    fun mapLocationIconImgRes(allowed: Boolean): Int =
        if (allowed) R.drawable.icon_permission_location_on else R.drawable.icon_permission_location_off

    fun mapTextColor(context: Context, allowed: Boolean) =
        context.resources.getColor(if (allowed) R.color.fg_disabled else R.color.fg_primary, context.theme)

    vg.findViewById<ImageView>(R.id.ic_location)?.setImageResource(mapLocationIconImgRes(allowed = allowed))
    vg.findViewById<ImageView>(R.id.ic_nearby)?.setImageResource(mapLocationIconImgRes(allowed = allowed))

    vg.findViewById<TextView>(R.id.tv_location_title)?.setTextColor(mapTextColor(context = vg.context, allowed = allowed))
    vg.findViewById<TextView>(R.id.tv_nearby_title)?.setTextColor(mapTextColor(context = vg.context, allowed = allowed))

    vg.findViewById<View>(R.id.tv_nearby_allow)?.isVisible = !allowed
    vg.findViewById<View>(R.id.tv_location_allow)?.isVisible = !allowed

    vg.findViewById<View>(R.id.ic_nearby_check)?.isVisible = allowed
    vg.findViewById<View>(R.id.ic_location_check)?.isVisible = allowed
}

@BindingAdapter(
    value = [
        "bind:targetDevice",
        "bind:actualDevice"
    ]
)
fun decoSelected(iv: ImageView, targetDevice: PartyBoxDevice?, actualDevice: PartyBoxDevice?) {
    iv.setImageResource(
        if (null != targetDevice && null != actualDevice && targetDevice == actualDevice) {
            R.drawable.svg_icon_security_selected
        } else {
            R.drawable.svg_icon_security_unselected
        }
    )
}

@BindingAdapter("customEnable")
fun customEnable(btn: RoundCornerButton, enable: Boolean) {
    btn.setTextColor(
        btn.resources.getColor(
            if (enable) R.color.fg_inverse else R.color.bg_s3,
            btn.context.theme
        )
    )
    btn.background = ResourcesCompat.getDrawable(
        btn.resources,
        if (enable) R.drawable.ripple_button_background else R.drawable.radius_large_bg_card,
        btn.context.theme
    )
    btn.isEnabled = enable
    btn.isClickable = enable
}

@BindingAdapter("decoPartyBoxTwsSelectChannel")
fun decoPartyBoxTwsSelectChannel(tv: TextView, activity: PartyBoxTwsActivity) {
    val fullTxt = tv.context.resources.getString(R.string.which_product_is_lighting)
    val reactTxt = tv.context.resources.getString(R.string.multi_channel_green_led)

    val builder = SpannableStringBuilder(fullTxt)

    val startIdx = fullTxt.indexOf(reactTxt)
    val endIdx = startIdx + reactTxt.length

    if (startIdx in 0 until endIdx) {
        // Set color span last cause click span may infect it.
        val reactColorSp = ForegroundColorSpan(tv.context.resources.getColor(R.color.teal_a200))
        builder.setSpan(reactColorSp, startIdx, endIdx, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    tv.text = builder
    tv.movementMethod = LinkMovementMethod.getInstance()
}

@BindingAdapter("bindAfterTextChanged")
fun bindAfterTextChanged(et: EditText, watcher: TextWatcher) {
    et.setHorizontallyScrolling(true)
    et.addTextChangedListener(watcher)
}

@BindingAdapter("bindOnFocusChangeListener")
fun bindOnFocusChangeListener(et: EditText, onFocusChangeListener: OnFocusChangeListener) {
    et.onFocusChangeListener = onFocusChangeListener
}

@BindingAdapter("setFilters")
fun setFilters(et: EditText, filters: Array<InputFilter>?) {
    if (filters.isNullOrEmpty()) {
        return
    }

    et.filters = filters
}

@BindingAdapter("isVisible")
fun isVisible(v: View, visible: Boolean?) {
    v.isVisible = visible ?: false
}

@BindingAdapter("isVisibleInVisible")
fun isVisibleInVisible(v: View, visible: Boolean?) {
    v.visibility = if (true == visible) View.VISIBLE else View.INVISIBLE
}

@BindingAdapter(
    value = [
        "bind:selectedChannelMode",
        "bind:selfChannelMode"
    ]
)
fun decoChannelModeSwitcher(
    layout: ConstraintLayout,
    selected: EnumChannelMode?,
    self: EnumChannelMode
) {
    layout.background = if (selected == self) {
        ResourcesCompat.getDrawable(
            layout.resources,
            R.drawable.bg_tws_mode_switcher_selected,
            layout.context.theme
        )
    } else {
        null
    }
}

@BindingAdapter(
    value = [
        "bind:activity",
        "bind:fragmentTag",
        "bind:targetFragment"
    ]
)
fun bindFragment(
    frameLayout: FrameLayout,
    activity: FragmentActivity?,
    fragmentTag: String,
    targetFragment: Fragment?
) {
    activity ?: return
    targetFragment ?: return

    val fm = activity.supportFragmentManager
    val preFragment = fm.findFragmentByTag(fragmentTag)

    if (preFragment != null &&
        preFragment.javaClass == targetFragment.javaClass
    ) {
        return
    }

    val ft = fm.beginTransaction()
    ft.setCustomAnimations(R.anim.frag_fade_in, R.anim.frag_fade_out)
    ft.replace(frameLayout.id, targetFragment, fragmentTag)
    ft.commitAllowingStateLoss()
}

@BindingAdapter("decoDevice")
fun decoDevice(view: AuracastOnView, device: Device?) {
    if (null == device) {
        view.isVisible = false
    } else {
        view.isVisible = true
        view.setImageResourceFromModel(device)
        view.setPlaying(device.isBroadcasterOn())
    }
}

@BindingAdapter(
    value = [
        "bind:receiverDevice",
        "bind:moreAuraCastDeviceBtnVisible"
    ]
)
fun decoLastReceiverWithMoreIcon(
    view: AuracastOnView,
    device: Device?,
    moreAuraCastDeviceBtnVisible: Boolean?
) {
    if (null == device || true == moreAuraCastDeviceBtnVisible) {
        view.isVisible = false
    } else {
        view.isVisible = true
        view.setImageResourceFromModel(device)
        view.setPlaying(device.isBroadcasterOn())
    }
}

@BindingAdapter(
    value = [
        "bind:animRes",
        "bind:run"
    ]
)
fun startAnim(view: LottieAnimationView, @RawRes animRes: Int, run: Boolean?) {
    if (true == run) {
        if (view.isAnimating) return
        view.alpha = 0.3f
        view.setAnimation(animRes)
        view.playAnimation()
        view.repeatMode = LottieDrawable.RESTART
    } else {
        if (view.isAnimating) {
            view.cancelAnimation()
        }
        view.alpha = 0.3f
        view.setAnimation(animRes)
    }
}

@BindingAdapter("playMusicAnim")
fun playMusicAnim(view: LottieAnimationView, run: Boolean?) {
    if (true == run) {
        view.setAnimation(R.raw.playing_music)
        val filter = SimpleColorFilter(view.context.getColor(R.color.fg_activate))
        val keyPath = KeyPath("**")
        val callback: LottieValueCallback<ColorFilter> = LottieValueCallback(filter)
        view.addValueCallback(keyPath, LottieProperty.COLOR_FILTER, callback)
        view.playAnimation()
    } else {
        view.cancelAnimation()
    }
}

@BindingAdapter("loadingDotsAnim")
fun loadingDotsAnim(view: LottieAnimationView, run: Boolean?) {
    if (true == run) {
        view.setAnimation(R.raw.loading_dots_white)
        val filter = SimpleColorFilter(view.context.getColor(R.color.fg_activate))
        val keyPath = KeyPath("**")
        val callback: LottieValueCallback<ColorFilter> = LottieValueCallback(filter)
        view.addValueCallback(keyPath, LottieProperty.COLOR_FILTER, callback)
        view.playAnimation()
    } else {
        view.cancelAnimation()
    }
}

@BindingAdapter("decoBattery")
fun decoBattery(view: BatteryView, device: Device?) {
    device ?: return

    if (device.hasBatteryInfo()) {
        view.visible()

        when (device) {
            is OneDevice -> {
                view.setBattery(device.batteryLevel)
            }

            is PartyBoxDevice -> {
                view.setBattery(device.batteryLevel, device.isCharging)
            }

            is PortableDevice -> {
                view.setBattery(device.batteryLevel)
            }

            is PartyBandDevice -> {
                view.setBattery(device.batteryLevel, device.isCharging)
            }

            else -> {
                // do nothing
            }
        }
    } else {
        view.gone()
    }
}


@BindingAdapter("setAuraCastNearbyDevicesAdapter")
fun setAuraCastNearbyDevicesAdapter(
    recyclerView: RecyclerView,
    adapter: AuraCastNearbyDeviceAdapter?
) {
    // remove flash anim after invoke notifyItemChanged
    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }
    recyclerView.itemAnimator = null
    recyclerView.adapter = adapter ?: return
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.HORIZONTAL,
        false
    )
    recyclerView.addItemDecoration(AuraCastNearbyDeviceListDecoration(adapter))
}

@BindingAdapter(
    value = [
        "bind:device",
        "bind:connectingDevices",
        "bind:connectedDevices"
    ]
)
fun decoAuraCastOffItem(
    view: AuracastOffView,
    device: Device?,
    connectingDevices: List<Device>?,
    connectedDevices: List<Device>?
) {
    device ?: return

    val state = getAuraCastOffState(
        device = device,
        connectingDevices = connectingDevices,
        connectedDevices = connectedDevices
    )

    if (view.state != state) {
        view.setState(state)
    }

    deviceName(textView = view.tv_device_name, device = device)
    auraCastDeviceImgForce(iv = view.iv_device, device = device)
}

fun getAuraCastOffState(
    device: Device?,
    connectingDevices: List<Device>?,
    connectedDevices: List<Device>?
): EnumAuraCastOffState = when {
    !connectingDevices.isNullOrEmpty() && (connectingDevices.getOrNull(0) == device) -> {
        EnumAuraCastOffState.CONNECTING
    }

    !connectingDevices.isNullOrEmpty() && connectingDevices.contains(device) -> {
        EnumAuraCastOffState.WAITING
    }

    !connectedDevices.isNullOrEmpty() && connectedDevices.contains(device) -> {
        EnumAuraCastOffState.CONNECTED
    }

    else -> {
        EnumAuraCastOffState.NORMAL
    }
}

@BindingAdapter("setAuraCastMoreNearbyDevicesAdapter")
fun setAuraCastMoreNearbyDevicesAdapter(
    recyclerView: RecyclerView,
    adapter: AuraCastMoreNearbyDeviceAdapter?
) {
    // remove flash anim after invoke notifyItemChanged
    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }
    recyclerView.itemAnimator = null
    recyclerView.adapter = adapter ?: return
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
    recyclerView.addItemDecoration(AuraCastMoreNearbyDeviceListDecoration(adapter))
}

@BindingAdapter("decoAuraCastExitDemoImg")
fun decoAuraCastExitDemoImg(iv: ImageView, device: Device?) {
    val pid = device?.pid ?: return

    val partyIcon = AppConfigurationUtils.getAuraCastPartyIcon(pid)
    iv.setImageResource(
        when {
            HmAuraCastPartyIcon.SoundStick == partyIcon -> R.mipmap.placeholder_with_volume // SS5
            HmAuraCastPartyIcon.Horizon == partyIcon -> R.mipmap.placeholder_with_horizon // horizon 3
            HmAuraCastPartyIcon.Play == partyIcon -> R.mipmap.placeholder_with_play
            else -> R.mipmap.placeholder_with_auracast
        }
    )
}

@BindingAdapter("decoAuraCastExitDemoTxt")
fun decoAuraCastExitDemoTxt(tv: TextView, device: Device?) {
    val pid = device?.pid ?: return
    val partyIcon = AppConfigurationUtils.getAuraCastPartyIcon(pid)
    tv.setText(
        when {
            HmAuraCastPartyIcon.Horizon == partyIcon -> R.string.turn_off_the_PartyTogether
            else -> R.string.press_the_shown_button_on_the_product_to_quit
        }
    )
}

@BindingAdapter(
    value = [
        "bind:currentIndex",
        "bind:models"
    ]
)
fun bindAuraCastGuidePagerIndicator(
    indicator: HMPagerIndicator,
    currentIndex: Int?,
    models: List<AuraCastGuideModel>?
) {
    currentIndex ?: return
    if (models.isNullOrEmpty()) {
        return
    }

    if (indicator.totalIndex != models.size) {
        indicator.totalIndex = models.size
    }

    if (indicator.currentIndex != currentIndex) {
        indicator.currentIndex = currentIndex
    }
}

@BindingAdapter("setAdapter")
fun setAdapter(vp2: ViewPager2, adapter: RecyclerView.Adapter<*>?) {
    vp2.adapter = adapter ?: return
}

@BindingAdapter("setOnPageChangeCallback")
fun setOnPageChangeCallback(vp2: ViewPager2, callback: ViewPager2.OnPageChangeCallback?) {
    callback ?: return
    vp2.registerOnPageChangeCallback(callback)
}

@BindingAdapter("setImgRes")
fun setImgRes(iv: ImageView, resId: Int?) {
    resId ?: run {
        iv.setImageResource(0)
        return
    }
    iv.setImageResource(resId)
}

@BindingAdapter(
    value = [
        "bind:device",
        "bind:selectedDevice"
    ]
)
fun decoSelected(
    view: View,
    device: Device?,
    selectedDevice: Device?
) {
    if (null == selectedDevice) {
        view.decoNormal()
        return
    }

    if (device == selectedDevice) {
        view.decoSelected()
    } else {
        view.decoUnSelected()
    }
}

private fun View.decoNormal() {
    scaleX = 1.0f
    scaleY = 1.0f
    alpha = 1.0f
}

private fun View.decoSelected() {
    scaleX = 1.1f
    scaleY = 1.1f
    alpha = 1.0f
}

private fun View.decoUnSelected() {
    scaleX = 0.9f
    scaleY = 0.9f
    alpha = 0.7f
}

@BindingAdapter("setOnNegativeButtonClickListener")
fun setOnNegativeButtonClickListener(v: ComponentButtonDoubleMini, l: View.OnClickListener?) {
    v.setOnNegativeButtonClickListener(l)
}

@BindingAdapter("setOnPositiveButtonClickListener")
fun setOnPositiveButtonClickListener(v: ComponentButtonDoubleMini, l: View.OnClickListener?) {
    v.setOnPositiveButtonClickListener(l)
}

@BindingAdapter("onSeekBarProgressChanged")
fun onSeekBarProgressChanged(view: DJEffectBar, listener: DJEffectBar.ProgressListener) {
    view.setListener { progress, djEffectBar ->
        listener.onProgressChanged(progress, djEffectBar)
    }
}

@BindingAdapter("onBarSlideStop")
fun onBarSlideStop(view: DJEffectBar, listener: DJEffectBar.BarSlideStopListener) {
    view.setBarSlideStopListener { stop, isLongClick, djEffectBar ->
        listener.onBarSlideStop(stop, isLongClick, djEffectBar)
    }
}

@BindingAdapter("addRecyclerViewOnGlobalLayoutListener")
fun addRecyclerViewOnGlobalLayoutListener(
    view: RecyclerView,
    listener: ViewTreeObserver.OnGlobalLayoutListener
) {
    view.viewTreeObserver.addOnGlobalLayoutListener(listener)
}

@BindingAdapter("musicServiceIc")
fun musicServiceIc(iv: ImageView, mainItem: MainItem?) {
    val res = mainItem?.Key?.toIconResource() ?: run {
        iv.setImageResource(0)
        return
    }
    iv.setImageResource(res)
}

@BindingAdapter("musicServiceSubIc")
fun musicServiceSubIc(iv: ImageView, mainItem: MainItem?) {
    val res = mainItem?.Key?.toSubIconResource()
    if (null != res) {
        iv.isVisible = true
        iv.setImageResource(res)
    } else {
        iv.isVisible = false
        iv.setImageResource(0)
    }
}


@BindingAdapter("musicServiceSwitcher")
fun musicServiceSwitcher(iv: ImageView, mainItem: MainItem?) {
    val open = mainItem?.bOpen ?: return
    iv.setImageResource(
        if (open) {
            R.drawable.ic_round_remove_red_1
        } else {
            R.drawable.ic_round_add_fg_primary
        }
    )
}

@BindingAdapter("momentServiceSwitcher")
fun momentServiceSwitcher(iv: ImageView, mainItem: MainItem?) {
    val open = mainItem?.bOpen ?: return
    iv.setImageResource(
        if (open) {
            R.drawable.ic_arrow_forward_square
        } else {
            R.drawable.ic_round_add_fg_primary
        }
    )
}

@BindingAdapter("setMusicServiceListAdapter")
fun setMusicServiceListAdapter(
    recyclerView: RecyclerView,
    adapter: BaseAdapter<*, *>?
) {
    // remove flash anim after invoke notifyItemChanged
    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }
    recyclerView.itemAnimator = null
    recyclerView.adapter = adapter ?: return
    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
    recyclerView.addItemDecoration(MusicServiceListDecoration(adapter))
}

@BindingAdapter("submitListMainItem")
fun submitListMainItem(recyclerView: RecyclerView, list: List<MainItem>?) {
    val adapter = (recyclerView.adapter as? BaseAdapter<*, MainItem>) ?: return
    adapter.updateData(list ?: emptyList())
}

@BindingAdapter("decoRemoveMusicService")
fun decoRemoveMusicService(tv: TextView, device: OneDevice?) {
    device ?: return

    tv.text = tv.context.resources.getString(R.string.newStructure_Removing_this_music_service)
        .format(device.deviceName)
}

@BindingAdapter("setBackgroundResource")
fun setBackgroundResource(v: View, src: Int?) {
    if (null == src || src <= 0) {
        v.setBackgroundResource(0)
        return
    }
    v.setBackgroundResource(src)
}

@BindingAdapter("setBackgroundColor")
fun setBackgroundColor(v: View, color: Int?) {
    if (null == color || color <= 0) {
        v.setBackgroundColor(Color.TRANSPARENT)
        return
    }
    v.setBackgroundColor(color)
}

@BindingAdapter("setTextColorResource")
fun setTextColorResource(tv: TextView, src: Int?) {
    if (null == src || src <= 0) {
        return
    }
    tv.setTextColor(tv.resources.getColor(src))
}

@BindingAdapter("setTextResource")
fun setTextResource(tv: TextView, src: Int?) {
    tv.text = if (null == src || src <= 0) {
        ""
    } else {
        tv.resources.getString(src)
    }
}

@BindingAdapter("setTextResource")
fun setTextResource(btn: ComponentButton, src: Int?) {
    if (null == src || src <= 0) return
    btn.setBtnText(btn.resources.getString(src))
}

@BindingAdapter("btn_text")
fun setTextResource(btn: ComponentButton, txt: String?) {
    btn.setBtnText(txt)
}

@BindingAdapter("setThumbResource")
fun setThumbResource(sb: AppCompatSeekBar, src: Int?) {
    if (null == src || src <= 0) return
    sb.thumb = ResourcesCompat.getDrawable(sb.resources ?: return, src, sb.context?.theme)
    sb.thumbOffset = ScreenUtil.dip2px(sb.context ?: return, -7.0f)
}

@BindingAdapter(
    value = [
        "bind:selfMoment",
        "bind:previewMoment"
    ]
)
fun animPreviewMoment(lottie: LottieAnimationView, selfMoment: EnumMoment?, previewMoment: EnumMoment?) {
    if (null != selfMoment && null != previewMoment && selfMoment == previewMoment) {
        lottie.playLottieAmbientSoundPreview()
    } else {
        lottie.pauseLottieAmbientSoundPreview()
    }
}

@BindingAdapter("animPreviewMoment")
fun animPreviewMoment(lottie: LottieAnimationView, isPlay: Boolean?) {
    if (true == isPlay) {
        lottie.playLottieAmbientSoundPreview()
    } else {
        lottie.pauseLottieAmbientSoundPreview()
    }
}

private fun LottieAnimationView.playLottieAmbientSoundPreview() {
    setAnimation(R.raw.lottie_ambient_sound_preview)
    repeatMode = LottieDrawable.RESTART
    repeatCount = LottieDrawable.INFINITE
    progress = 0f
    playAnimation()
}

private fun LottieAnimationView.pauseLottieAmbientSoundPreview() {
    cancelAnimation()
    progress = 1.0f
}

@BindingAdapter("decoMomentNameDesc")
fun decoMomentNameDesc(tv: TextView, musicId: String?) {
    if (musicId.isNullOrBlank()) {
        tv.text = tv.resources.getString(R.string.moment_your_playlist_desc)
        tv.setTextAppearance(R.style.Text_Body_Regular)
        tv.setTextColor(tv.resources.getColor(R.color.fg_primary))
    } else {
        tv.text = musicId
        tv.setTextAppearance(R.style.Text_Body_Strong)
        tv.setTextColor(tv.resources.getColor(R.color.fg_activate))
    }
}

@BindingAdapter("decoMomentIcon")
fun decoMomentIcon(iv: TextView, musicId: String?) {
    if (musicId.isNullOrBlank()) {
        iv.isClickable = true
        iv.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.ic_round_info_fg_primary, 0)
    } else {
        iv.isClickable = false
        iv.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.ic_round_switch_fg_activate, 0)
    }
}

fun buildGlideImgLoadConfig(width: Int, height: Int): ImageLoadConfig {
    val overrideSize = if (width > 0 && height > 0) {
        ImageLoadConfig.OverrideSize(width, height)
    } else {
        ImageLoadConfig.OverrideSize(100, 100)
    }

    return ImageLoadConfig.parseBuilder(GlideMgtUtil.defConfig)
        .setSkipMemoryCache(false)
        .setAsBitmap(true)
        .setRoundedCorners(true)
        .setCropType(ImageLoadConfig.FIT_CENTER)
        .setRadius(smallRadius)
        .setDiskCacheStrategy(ImageLoadConfig.DiskCache.ALL)
        .setMargin(0)
        .setSize(overrideSize)
        .build()
}

@BindingAdapter(
    value = [
        "bind:albumUrl",
        "bind:musicServiceSource",
        "bind:deviceItem",
        "bind:width",
        "bind:height"
    ]
)
fun loadAlbumCover(
    iv: ImageView,
    albumUrl: String?,
    source: EnumMusicServiceSource?,
    deviceItem: DeviceItem?,
    width: Int?,
    height: Int?
) {
    val realWidth = width ?: 75
    val realHeight = height ?: 75

    Tools.loadAlbumCover(
        context = iv.context, listener = object : BitmapLoadingListener {
            override fun onSuccess(bitmap: Bitmap?) {
                if (null != bitmap) {
                    iv.setImageBitmap(bitmap)
                } else {
                    iv.setImageResource(R.drawable.shape_round_8_bg_sp_player)
                }
            }

            override fun onError() {
                iv.setImageResource(R.drawable.shape_round_8_bg_sp_player)
            }
        }, albumUrl = albumUrl, source = source, deviceItem = deviceItem,
        width = realWidth, height = realHeight
    )
}

@BindingAdapter("loadMomentAlbumCover")
fun loadMomentAlbumCover(iv: ImageView, url: String?) {
    if (url.isNullOrBlank()) {
        iv.setImageBitmap(null)
        return
    }

    GlideMgtUtil.loadBitmap(
        iv.context,
        url,
        buildGlideImgLoadConfig(width = iv.width, height = iv.height),
        object : BitmapLoadingListener {
            override fun onSuccess(bitmap: Bitmap?) {
                iv.setImageBitmap(bitmap)
            }

            override fun onError() {
                iv.setImageResource(R.drawable.shape_round_8_bg_sp_player)
            }
        }
    )
}

@BindingAdapter("decoMomentPager")
fun decoMomentPager(pager2: ViewPager2, activity: MomentActivity?) {
    activity ?: return

    val padding = ScreenUtil.dip2px(activity, 28f)

    (pager2.getChildAt(0) as? RecyclerView)?.let { recyclerView ->
        recyclerView.setPadding(padding, 0, padding, 0)
        recyclerView.clipToPadding = false
        recyclerView.overScrollMode = View.OVER_SCROLL_NEVER
    }

    pager2.offscreenPageLimit = 2
    pager2.clipChildren = false
    pager2.clipToPadding = false

    pager2.setPageTransformer(CompositePageTransformer().apply {
        addTransformer(MarginPageTransformer(padding)) // 添加边距Transformer
        addTransformer(ScaleInTransformer()) // 添加缩放效果的Transformer
    })

    pager2.registerOnPageChangeCallback(activity.pagerChangeCallback)
}

@BindingAdapter("bindSoundscapes")
fun bindSoundscapes(indicator: HMPagerIndicator, soundscapes: List<EnumMoment>?) {
    indicator.totalIndex = soundscapes?.size ?: 0
}

@BindingAdapter("bindSelectedMomentIndex")
fun bindSelectedMomentIndex(indicator: HMPagerIndicator, index: Int?) {
    index ?: return

    if (index in 0 until indicator.totalIndex) {
        indicator.currentIndex = index
    }
}

@BindingAdapter(
    value = [
        "bind:remoteUrl",
        "bind:placeHolder",
        "bind:error"
    ]
)
fun loadRemoteImgWithException(
    iv: ImageView,
    remoteUrl: String?,
    placeHolder: Drawable?,
    error: Drawable?
) {
    if (remoteUrl.isNullOrBlank()) {
        if (null != placeHolder) {
            iv.setImageDrawable(placeHolder)
        } else if (null != error) {
            iv.setImageDrawable(error)
        } else {
            iv.setImageDrawable(null)
        }

        return
    }

    Glide.with(iv.context.applicationContext)
        .load(remoteUrl)
        .asBitmap()
        .apply {
            if (null != placeHolder) {
                placeholder(placeHolder)
            }
        }
        .into(object : SimpleTarget<Bitmap>() {
            override fun onResourceReady(
                resource: Bitmap?,
                glideAnimation: GlideAnimation<in Bitmap>?
            ) {
                iv.setImageBitmap(resource)
            }

            override fun onLoadFailed(e: Exception?, errorDrawable: Drawable?) {
                if (null != error) {
                    iv.setImageDrawable(error)
                }
            }
        })
}

@BindingAdapter(
    value = [
        "bind:selfIndex",
        "bind:selectedIndex"
    ]
)
fun decoSelectedMoment(tv: TextView, selfIndex: Int?, selectedIndex: Int?) {
    val selected = isSelected(selfIndex = selfIndex, selectedIndex = selectedIndex)

    tv.setTextColor(tv.resources.getColor(if (selected) R.color.fg_activate else R.color.fg_primary, tv.context.theme))
    tv.setTextAppearance(if (selected) R.style.Text_Body_Strong else R.style.Text_Body_Regular)
}

@BindingAdapter(
    value = [
        "bind:selfIndex",
        "bind:selectedIndex"
    ]
)
fun decoSelectedMoment(cb: CheckBox, selfIndex: Int?, selectedIndex: Int?) {
    cb.isChecked = isSelected(selfIndex = selfIndex, selectedIndex = selectedIndex)
}

@BindingAdapter("collapsingToolBarNavigation")
fun collapsingToolBarNavigation(toolBar: CollapsingToolBar, fragment: FragTabBackBase) {
    toolBar.setNavigationListener(object : CollapsingToolBar.NavigationListener {
        override fun onBack() {
            fragment.onBackKeyDown()
        }

        override fun onNext() {
        }
    })
}

@BindingAdapter("startImg")
fun startImg(tv: AppCompatTextView, resId: Int) {
    try {
        tv.setCompoundDrawablesRelativeWithIntrinsicBounds(
            ResourceUtils.getDrawable(resId), null, null, null
        )
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun isSelected(selfIndex: Int?, selectedIndex: Int?): Boolean {
    selfIndex ?: return false
    selectedIndex ?: run {
        return 0 == selfIndex
    }

    return selfIndex == selectedIndex
}

@BindingAdapter("setProgressByMomentElement")
fun setProgressByMomentElement(sb: HmCustomVerticalSeekBar, data: ProgressBarData?) {
    data ?: run {
        sb.min = 0
        sb.max = 100
        sb.progress = 0
        return
    }

    sb.min = data.min
    sb.max = data.max
    sb.progress = data.progress
}

@BindingAdapter(
    value = [
        "bind:elementData",
        "bind:elementDataChangeListener"
    ]
)
fun setElementProgressBarListener(
    sb: AppCompatSeekBar,
    elementData: ProgressBarData?,
    elementDataChangeListener: IMomentElementDataListener?
) {
    elementDataChangeListener ?: return
    elementData?.id ?: return

    sb.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            elementDataChangeListener.onChanged(elementId = elementData.id, progress = progress)
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {
            // no impl
        }

        override fun onStopTrackingTouch(seekBar: SeekBar?) {
            // no impl
        }
    })
}

@BindingAdapter(
    value = [
        "bind:momentAdapter",
        "bind:activeSoundscape"
    ]
)
fun bindMomentPagerAdapter(
    pager2: ViewPager2,
    momentAdapter: BaseMomentAdapter?,
    moment: EnumMoment?
) {
    momentAdapter ?: return
    moment ?: return

    if (null == pager2.adapter) {
        pager2.adapter = momentAdapter
    }

    val index = momentAdapter.moments.indexOf(moment)

    if (index in 0 until momentAdapter.moments.size) {
        pager2.currentItem = index
    }
}

@BindingAdapter("addItemDecoration")
fun addItemDecoration(recyclerView: RecyclerView, decoration: RecyclerView.ItemDecoration?) {
    decoration ?: return
    recyclerView.removeItemDecoration(decoration)
    recyclerView.addItemDecoration(decoration)
}

@BindingAdapter("setDeviceModelNameDescText")
fun setDeviceModelNameDescText(item: ComponentCardStandardItem, device: Device?) {
    (device?.pid ?: device?.dummyPid)?.let { AppConfigurationUtils.getDefaultFullName(it) }?.also { item.setDesc(it) }
}

@BindingAdapter("setSerialNumberDescText")
fun setSerialNumberDescText(item: ComponentCardStandardItem, device: Device?) {
    when (device) {
        is OneDevice -> {
            device.serialNumber?.also { item.setDesc(it) }
            if (device.serialNumber.isNullOrBlank()) item.visibility = View.GONE else item.visibility = View.VISIBLE
        }

        is PartyBoxDevice -> {
            device.serialNumber?.also { item.setDesc(it) }
        }

        is PartyLightDevice -> {} // TODO: PartyLightDevice has serial number

        is PartyBandDevice -> {
            device.bleDevice.serialNumber?.also { item.setDesc(it) }
        }
    }
}

@BindingAdapter("setTitleForSettingsOptionsItem")
fun setTitleForSettingsOptionsItem(item: ComponentCardStandardItem, content: String?) {
    content?.let { item.setTitle(it) }
}

@BindingAdapter("setDescForSettingsOptionsItem")
fun setDescForSettingsOptionsItem(item: ComponentCardStandardItem, content: String?) {
    content?.also {
        item.setDesc(it)
        item.setDescVisible(true)
    } ?: run {
        item.setDescVisible(false)
    }
}

@BindingAdapter("setItemDescText")
fun setItemDescText(item: ComponentCardStandardItem, str: String?) {
    str?.also { item.setDesc(it) }
}

@BindingAdapter("setDeviceMacDescText")
fun setDeviceMacDescText(item: ComponentCardStandardItem, device: Device?) {
    if (device is OneDevice) {
        device.wlan0?.also {
            item.visible()
            item.setDesc(it.uppercase())
        } ?: {
            item.gone()
        }
    } else {
        item.gone()
    }
}

@BindingAdapter("setDeviceImages")
fun setDeviceImages(multiChannelSpeakerView: MultiChannelSpeakerView, modelNameAndColors: List<android.util.Pair<String?, String?>?>?) {
    multiChannelSpeakerView.setDeviceImagesByPairList(modelNameAndColors)
}

@BindingAdapter("marginBottom")
fun marginBottom(v: View, pixel: Int?) {
    pixel ?: return

    v.layoutParams = (v.layoutParams as? ViewGroup.MarginLayoutParams)?.also { params ->
        params.bottomMargin = pixel
    }
}

@BindingAdapter("bindChildView")
fun bindChildView(vg: FrameLayout, childView: View?) {
    childView ?: return

    vg.removeAllViews()
    vg.addView(
        childView, FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
    )
}

@BindingAdapter("decoCalibrationAbnormalTips")
fun decoCalibrationAbnormalTips(tv: TextView, count: Int?) {
    count ?: return
    val sizeStr = count.toString()

    val formatStr = tv.resources.getString(R.string.calibration_result_number_of_tips).format(sizeStr)
    val ssb = SpannableStringBuilder(formatStr)

    val startIndex = formatStr.indexOf(sizeStr)
    val endIndex = startIndex + sizeStr.length

    if (startIndex >= 0 && endIndex <= formatStr.length) {
        ssb.setSpan(ForegroundColorSpan(tv.resources.getColor(R.color.orange_2)), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        ResourcesCompat.getFont(tv.context, R.font.poppins_semibold)?.let { font ->
            ssb.setSpan(CustomTypefaceSpan(tv.context, font), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    tv.text = ssb
}

@BindingAdapter("setBitmapSource")
fun setBitmapSource(iv: ImageView, bitmap: Bitmap?) {
    iv.setImageBitmap(bitmap)
}

@BindingAdapter("setMultichannelImages")
fun setMultichannelImages(multiChannelSpeakerView: MultiChannelSpeakerView, combinationItem: CombinationItem?) {
    multiChannelSpeakerView.setDeviceImagesByModelNameList(combinationItem?.modelList?.filterIndexed { index, s -> (index > 0) }
        ?.map { item -> item.modelName.get(0) })
}

@BindingAdapter("setMaxConstraintHeight")
fun setMaxConstraintHeight(v: View, height: Float?) {
    height ?: return
    v.layoutParams = (v.layoutParams as? ConstraintLayout.LayoutParams)?.also { params ->
        params.matchConstraintMaxHeight
    }
}

@BindingAdapter("setDiagnosisReportAdapter")
fun setDiagnosisReportAdapter(recyclerView: RecyclerView, adapter: DiagnosisReportProductsAdapter?) {
    recyclerView.adapter = adapter ?: return

    (recyclerView.itemAnimator as? SimpleItemAnimator)?.let { siAnimator ->
        siAnimator.supportsChangeAnimations = false
    }

    recyclerView.layoutManager = LinearLayoutManager(
        recyclerView.context,
        RecyclerView.VERTICAL,
        false
    )
}

@BindingAdapter("setOnTouchListener")
fun setOnTouchListener(v: View, listener: View.OnTouchListener?) {
    listener ?: return
    v.setOnTouchListener(listener)
}

@BindingAdapter("setTopMargin")
fun setTopMargin(v: View, margin: Int?) {
    margin ?: return

    v.layoutParams = (v.layoutParams as? ViewGroup.MarginLayoutParams)?.also { params ->
        params.topMargin = margin
    }
}

@BindingAdapter(
    value = [
        "bind:currentDevice",
        "bind:entryPoint",
        "bind:selectedDevices"
    ]
)
fun renderSelectDeviceView(
    view: ImageView,
    currentDevice: Device?,
    entryPoint: Device?,
    selectedDevices: List<OneDevice>?
) {
    currentDevice ?: return
    entryPoint ?: return
    if (currentDevice == entryPoint) {
        view.setImageResource(R.drawable.dev_grouping)
    } else if (selectedDevices?.contains(currentDevice) == true) {
        view.setImageResource(R.drawable.svg_icon_selected_round_hook)
    } else {
        view.setImageResource(R.drawable.svg_icon_unselect_ring)
    }
}

@BindingAdapter("isEntryPoint")
fun isEntryPoint(v: View, status: Boolean) {
    v.isEnabled = !status

}

@BindingAdapter(
    value = [
        "bind:currentCombinationItem",
        "bind:selectedDevices"
    ]
)
fun renderNextGroupBtn(
    view: RoundCornerButton,
    currentCombinationItem: CombinationItem,
    selectedDevices: List<OneDevice>?
) {
    decoEnable(
        view,
        !selectedDevices.isNullOrEmpty() && HarmancastManager.getInstance().sameComCombinationModel(currentCombinationItem, selectedDevices)
    )

}

@BindingAdapter("reportIcon")
fun reportIcon(v: ImageView, bean: DiagnosisReportProductUIBean) {
    if (bean.hasValidReportID) {
        v.setImageResource(R.drawable.icon_sync)
    } else {
        v.setImageResource(R.drawable.icon_stethoscope_check)
    }
}

@BindingAdapter("customerServiceTextColor")
fun customerServiceTextColor(v: TextView, bean: DiagnosisReportProductUIBean) {
    if (bean.isWiFiOnline && bean.supportDiagnosisReport) {
        v.setTextColor(v.context.getColor(R.color.fg_primary))
    } else {
        v.setTextColor(v.context.getColor(R.color.fg_disabled))
    }
}

@BindingAdapter("showToastTextByError")
fun showToastTextByError(v: TextView, errorType: ErrorType?) {
    errorType?.also {
        if (it == ErrorType.Offline) {
            v.setText(R.string.make_sure_the_product_is_connected_to_the_same_network_with_your_phone)
        } else if (it == ErrorType.GenerateError) {
            v.setText(R.string.something_went_wrong_please_try_again_later)
        } else if (it == ErrorType.NotSupport) {
            v.setText(R.string.feature_is_not_supported_by_the_current_software_version)
        }
    }
}

@BindingAdapter("setCurrentItem")
fun setAdapter(vp2: ViewPager2, currentItem: Int) {
    vp2.setCurrentItem(currentItem, true)
}

@BindingAdapter("userScrollEnabled")
fun setAdapter(vp2: ViewPager2, isUserInputEnabled: Boolean) {
    vp2.isUserInputEnabled = isUserInputEnabled
}


@BindingAdapter("setOnLongClick")
fun setOnLongClick(v: View, invoker: () -> Unit) {
    v.setOnLongClickListener {
        invoker.invoke()
        true
    }
}

@BindingAdapter("languageSettingsItemTextColor")
fun languageSettingsItemTextColor(v: TextView, isSelected: Boolean) {
    if (isSelected) {
        v.setTextColor(v.context.getColor(R.color.fg_activate))
        v.typeface = ResourcesCompat.getFont(v.context, R.font.poppins_semibold)
    } else {
        v.setTextColor(v.context.getColor(R.color.fg_primary))
        v.typeface = ResourcesCompat.getFont(v.context, R.font.poppins_regular)
    }
}

@BindingAdapter("languageSettingsItemSelectIcon")
fun languageSettingsItemSelectIcon(v: ImageView, isSelected: Boolean) {
    if (isSelected) {
        v.setImageResource(R.drawable.svg_icon_security_selected)
    } else {
        v.setImageResource(R.drawable.svg_icon_security_unselected)
    }
}

@BindingAdapter("setLayoutHeight")
fun setLayoutHeight(v: View, height: Int?) {
    height ?: return

    v.layoutParams = v.layoutParams?.also { params ->
        params.height = max(height, 0)
    }
}

@BindingAdapter("measureHeight")
fun measureHeight(v: View, mutableLiveData: MutableLiveData<Int>) {
    v.viewTreeObserver.addOnGlobalLayoutListener(
        object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                v.viewTreeObserver.removeOnGlobalLayoutListener(this)
                mutableLiveData.value = v.height
            }
        }
    )
}

@BindingAdapter("deviceOOBEAuthDemo")
fun deviceOOBEAuthDemo(iv: ImageView, device: Device?) {
    val imgPath = device.getOOBEAuthImgPath()
    Logger.d("imgPath", "deviceOOBEAuthDemo() >>> $imgPath")

    DeviceImageUtil.loadAsBitmap(
        iv = iv,
        imgPath = imgPath,
        placeHolder = getOOBEAuthPlaceHolder(device?.pid),
        error = getOOBEAuthPlaceHolder(device?.pid),
        scaleType = ImageView.ScaleType.CENTER_CROP,
        pid = device?.pid,
        hasFadeInEffect = true
    )
}

@DrawableRes
fun getOOBEAuthPlaceHolder(pid: String?): Int {
    val button = pid?.let { AppConfigurationUtils.getAuthButton(it) }

    return if (AuthButton.Moment == button) {
        R.drawable.img_oobe_auth_placeholder_moment
    } else if (AuthButton.Source == button) {
        R.drawable.img_oobe_auth_placeholder_source
    } else if (AuthButton.Plus == button) {
        R.drawable.img_oobe_auth_placeholder_plus
    } else {
        R.drawable.img_oobe_auth_placeholder_default
    }
}


@BindingAdapter(
    value = [
        "bind:currentDeviceBle",
        "bind:selectedDevicesBle"
    ]
)
fun renderSelectDeviceViewBle(
    view: ImageView,
    currentDevice: Device?,
    selectedDevices: List<OneDevice>?
) {
    currentDevice ?: return
    if (selectedDevices?.contains(currentDevice) == true) {
        view.setImageResource(R.drawable.svg_icon_selected_single)
    } else {
        view.setImageResource(R.drawable.svg_icon_unselect_ring)
    }
}

@BindingAdapter(
    value = [
        "bind:currentDeviceBT",
        "bind:selectedDevicesBT"
    ]
)
fun renderSelectDeviceViewBT(
    view: ImageView,
    currentDevice: Device?,
    selectedDevices: List<Device>?
) {
    currentDevice ?: return
    if (selectedDevices?.contains(currentDevice) == true) {
        view.setImageResource(R.drawable.svg_icon_selected_single)
    } else {
        view.setImageResource(R.drawable.svg_icon_unselect_ring)
    }
}

@BindingAdapter("setButtonStyle")
fun setButtonStyle(v: ComponentLoadingButton, style: ButtonStyle) {
    if (style == ButtonStyle.UN_NORMAL) {
        v.setBtnType(ComponentConfig.BTN_UN_NORMAL)
        v.stopLoading()
        v.setBtnTextVisible(true)
        v.isEnabled = false

    } else if (style == ButtonStyle.NORMAL) {
        v.setBtnType(ComponentConfig.BTN_REGULAR)
        v.stopLoading()
        v.setBtnTextVisible(true)
        v.isEnabled = true
    } else if (style == ButtonStyle.LOADING) {
        v.setBtnType(ComponentConfig.BTN_REGULAR)
        v.startLoading()
        v.setBtnTextVisible(false)
        v.isEnabled = false
    }

}

@BindingAdapter("setLayoutManager")
fun setLayoutManager(recyclerView: RecyclerView, layoutManger: LayoutManager?) {
    layoutManger ?: return
    recyclerView.layoutManager = layoutManger
}

@BindingAdapter("shortName")
fun shortName(tv: TextView, device: Device?) {
    device?.also {
        tv.text = device.shortName(context = tv.context)
    }
}

@BindingAdapter("setRedirectMarginBottom")
fun setRedirectMarginBottom(v: View, redirectionMarginBottom: Int) {
    if (v.layoutParams is ViewGroup.MarginLayoutParams) {
        (v.layoutParams as ViewGroup.MarginLayoutParams).bottomMargin = redirectionMarginBottom
    }
}

@BindingAdapter("mask")
fun mask(v: View, isMask: Boolean) {
    v.alpha = if (isMask) 0.5f else 1.0f
}

@BindingAdapter("miniPlayerPaddingHorizontal")
fun miniPlayerPaddingHorizontal(v: View, horizontalPadding: Int) {
    v.setPadding(horizontalPadding, horizontalPadding, horizontalPadding, 0)
}

@BindingAdapter("miniPlayerPaddingTop")
fun miniPlayerPaddingTop(v: RecyclerView, marginTop: Int) {
    if (v.layoutParams is ViewGroup.MarginLayoutParams) {
        (v.layoutParams as ViewGroup.MarginLayoutParams).topMargin = marginTop
    }
    v.invalidateItemDecorations()
}

private val resourceWidth = SizeUtils.dp2px(24f)
private val pinMovingDistance = resourceWidth + SizeUtils.dp2px(12f) // resource width + margin end

@BindingAdapter("decoPinIcon")
fun decoPinIcon(iv: ImageView, @IntRange(from = 0L, to = 100L) percentage: Int?) {
    percentage ?: return
    if (percentage < 0 || percentage > 100) {
        return
    }

    iv.alpha = percentage.toFloat() / 100
    iv.layoutParams = (iv.layoutParams as? ViewGroup.MarginLayoutParams)?.also { params ->
        params.marginEnd = pinMovingDistance * percentage / 100 - resourceWidth
    }
}

@BindingAdapter("setEnable")
fun setEnable(btn: ComponentButton, enable: Boolean) {
    btn.setBtnType(if (enable) ComponentConfig.BTN_REGULAR else ComponentConfig.BTN_DISABLE)
    btn.isEnabled = enable
}

@BindingAdapter("decoAuracastQualityButton")
fun decoAuracastQualityButton(btn: ComponentButton, isAuraCastSqEnabled: Boolean) {
    if (isAuraCastSqEnabled) {
        btn.setBtnTextColor(btn.context, R.color.red_1)
        btn.setBtnText(btn.context.getString(R.string.disable))
        btn.setBtnType(ComponentConfig.BTN_WARNING)
    } else {
        btn.setBtnTextColor(btn.context, R.color.fg_inverse)
        btn.setBtnText(btn.context.getString(R.string.jbl_Enable))
        btn.setBtnType(ComponentConfig.BTN_REGULAR)
    }
}

@BindingAdapter("setEndImgClickListener")
fun setEndImgClickListener(item: ComponentCardStandardItem, listener: View.OnClickListener) {
    item.setEndImgClickListener(listener = listener)
}

@BindingAdapter(
    value = [
        "bind:batteryIndex",
        "bind:batteryAreaUiBean"
    ]
)
fun renderBatteryList(
    view: BatteryView,
    batteryIndex: Int,
    batteryAreaUiBean: BatteryAreaUiBean?
) {
    batteryAreaUiBean ?: return
    val size = batteryAreaUiBean.items.size
    if (batteryIndex >= size) {
        view.gone()
    }
    val battery = batteryAreaUiBean.items[batteryIndex]
    view.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
}

@BindingAdapter("renderBattery")
fun renderBattery(
    view: BatteryView,
    battery: BatteryAreaUiBean.Item?
) {
    battery ?: return

    view.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
}

@BindingAdapter("loadImageByModelName")
fun loadImageByModelName(iv: ImageView, modelName: String?) {
    modelName?.also {
        var deviceImage = AppConfigurationUtils.getModelRenderPathByModelName(it)
        CoroutineScope(DISPATCHER_FAST_MAIN).launch {
            if (deviceImage.isNullOrBlank()) {
                CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                    delay(1500)
                    deviceImage = AppConfigurationUtils.getModelRenderPathByModelName(it)
                    Glide.with(iv.context).load(deviceImage).fitCenter().into(iv)
                }
            } else {
                Glide.with(iv.context).load(deviceImage).fitCenter().into(iv)
            }
        }
    }

}

@BindingAdapter(
    value = [
        "bind:playStateImgRes",
        "bind:isTransitioning",
        "bind:transitioningGifRes"
    ]
)
fun decoPlayerStateWithTransitioning(
    iv: ImageView,
    playStateImgRes: Int?,
    isTransitioning: Boolean?,
    transitioningGifRes: Int?
) {
    if (true == isTransitioning && null != transitioningGifRes) {
        ImageLoadUtil.loadGif(transitioningGifRes, iv)
        return
    }

    setImgRes(iv = iv, resId = playStateImgRes)
}

@BindingAdapter("setGif")
fun setGif(iv: ImageView, transitioningGifRes: Int?) {
    transitioningGifRes ?: return
    ImageLoadUtil.loadGif(transitioningGifRes, iv)
}

@BindingAdapter("translationText")
fun text(tv: TextView, key: String) {
    val newKey = key.replace("@string/", "")
    val result = SkinResourcesUtils.getString(newKey)
    Logger.d("translationText", "key = $key    newKey = $newKey    result = $result")
    tv.text = result
}

@BindingAdapter("android:layout_marginStart")
fun setLeftMargin(view: View, leftMargin: Float) {
    val layoutParams = view.layoutParams as MarginLayoutParams
    layoutParams.setMargins(
        Math.round(leftMargin), layoutParams.topMargin,
        layoutParams.marginEnd, layoutParams.bottomMargin
    )
    view.layoutParams = layoutParams
}

@BindingAdapter("android:layout_marginEnd")
fun setRightMargin(view: View, rightMargin: Float) {
    val layoutParams = view.layoutParams as MarginLayoutParams
    layoutParams.setMargins(
        layoutParams.marginStart, layoutParams.topMargin,
        Math.round(rightMargin), layoutParams.bottomMargin
    )
    view.layoutParams = layoutParams
}

@BindingAdapter("decoPresetStationName")
fun decoPresetStationName(tv: TextView, station: RadioInfo.Station?) {
    tv.text = if (null == station) {
        tv.resources.getString(R.string.no_preset)
    } else if (true == station.isPreset) {
        station.stationName
    } else {
        station.frequency?.toFMString()
    }
}

@BindingAdapter("decoPresetStationTag")
fun decoPresetStationTag(tv: TextView, station: RadioInfo.Station?) {
    tv.text = if (true == station?.isPreset) {
        tv.resources.getString(R.string.dab)
    } else {
        tv.resources.getString(R.string.fm)
    }
}


@BindingAdapter(
    value = [
        "bind:currentStation",
        "bind:selectedStation"
    ]
)
fun decoSelectedStation(
    iv: ImageView,
    currentStation: RadioInfo.Station?,
    selectedStation: RadioInfo.Station?
) {
    currentStation ?: run {
        iv.setImageResource(R.drawable.svg_icon_security_unselected)
        return
    }

    iv.setImageResource(
        if (currentStation == selectedStation) {
            R.drawable.svg_icon_security_selected
        } else {
            R.drawable.svg_icon_security_unselected
        }
    )
}

@BindingAdapter(
    value = [
        "bind:selfItem",
        "bind:selectedItem"
    ]
)
fun decoSelectedTextColor(tv: TextView, selfItem: ItemQueue?, selectedItem: ItemQueue?) {
    if (null == selfItem || null == selectedItem) {
        tv.setTextColor(tv.context.getColor(R.color.fg_primary))
        return
    }

    tv.setTextColor(tv.context.getColor(if (selfItem == selectedItem) R.color.fg_activate else R.color.fg_primary))
}

private val px48 = SizeUtils.dp2px(48f)

private val smallRadius = SizeUtils.dp2px(4f) // 8dp

private val queueAlbumGlideConfig = ImageLoadConfig.parseBuilder(GlideMgtUtil.defConfig)
    .setSkipMemoryCache(false)
    .setAsBitmap(true)
    .setRoundedCorners(true)
    .setRadius(smallRadius)
    .setPlaceHolderResId(R.drawable.radius_medium_bg_on_card)
    .setErrorResId(R.drawable.radius_medium_bg_on_card)
    .setDiskCacheStrategy(ImageLoadConfig.DiskCache.SOURCE)
    .setSize(ImageLoadConfig.OverrideSize(px48, px48))
    .build()

@BindingAdapter("loadAlbumCover")
fun loadAlbumCover(iv: ImageView, item: ItemQueue?) {
    iv.setImageResource(R.drawable.radius_medium_bg_on_card)
    item ?: return

    val url = item.albumUrl
    if (url.isNullOrBlank()) {
        return
    }

    GlideMgtUtil.loadBitmap(iv.context, url, queueAlbumGlideConfig, object : BitmapLoadingListener {
        override fun onSuccess(bitmap: Bitmap) {
            iv.setImageBitmap(bitmap)
        }

        override fun onError() {
            iv.setImageResource(R.drawable.radius_medium_bg_on_card)
        }
    })
}