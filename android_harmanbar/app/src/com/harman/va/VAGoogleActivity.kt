package com.harman.va

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.MainThread
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.databinding.ActivityVaGoogleBinding
import com.harman.partylight.util.fitSystemBar
import com.harman.portalUrl
import com.harman.discover.DeviceStore
import com.harman.discover.bean.OneDevice
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.UIUtils
import com.wifiaudio.view.dlg.GoogleLogoutDialog
import com.wifiaudio.view.oobe.SetupGoogleAssistantAndChromecastDialogFragment
import com.wifiaudio.view.oobe.SetupGoogleAssistantDialogFragment
import com.wifiaudio.view.oobe.VaAreReadyDialogFragment
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import com.wifiaudio.view.pagesdevcenter.devicesetting.calibration.ChromecastBuiltInAvailableDlgView
import config.AppLogTagUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by sky on 2024/7/25.
 */
class VAGoogleActivity : AppCompatActivity() {


    private var viewModel: VAGoogleViewModel? = null

    private val _viewStyle = MutableLiveData<EnumViewStyle>()
    val viewStyle: LiveData<EnumViewStyle>
        get() = _viewStyle
    private val _viewStyleLoading = MutableLiveData<EnumViewStyle>()
    val viewStyleLoading: LiveData<EnumViewStyle>
        get() = _viewStyleLoading

    private val _viewStyleDetail = MutableLiveData<EnumViewStyle>()
    val viewStyleDetail: LiveData<EnumViewStyle>
        get() = _viewStyleDetail

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Utils.decorateHarmanWindow(this@VAGoogleActivity)

        val device = parseBundle() ?: run {
            setResult(RESULT_CODE)
            finish()
            return
        }

        val binding = ActivityVaGoogleBinding.inflate(layoutInflater)
        binding.activity = this@VAGoogleActivity
        binding.lifecycleOwner = this@VAGoogleActivity
        fitSystemBar()
        setContentView(binding.root)

        bindingData(device = device)
//        binding.collapsingToolbar?.setExpanded(false)
//        binding.collapsingToolbar?.setNavigationListener(object : CollapsingToolBar.NavigationListener {
//            override fun onBack() {
//                setResult(RESULT_CODE)
//                finish()
//            }
//
//            override fun onNext() {
//            }
//        })

    }

    override fun onBackPressed() {
        super.onBackPressed()
        setResult(RESULT_CODE)
    }

    override fun onResume() {
        super.onResume()
        syncDeviceStatus()
    }

    @MainThread
    private fun syncDeviceStatus() {
        _viewStyleLoading.value = EnumViewStyle.SHOW
        _viewStyleDetail.value = EnumViewStyle.HIDE
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val isEnable = viewModel?.checkGoogleCastEnable() ?: false
            Logger.i(TAG, "requestDeviceStatus() >>> check result[$isEnable]")
            _viewStyleLoading.value = EnumViewStyle.HIDE
            if (isEnable) {
                _viewStyleDetail.value = EnumViewStyle.SHOW
                updateContentStatue()
            } else {
                _viewStyleDetail.value = EnumViewStyle.HIDE
                showGoogleSetupDialog()
            }
        }
    }

    private fun updateContentStatue() {



        LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "updateContentStatue isClickChromecastSetupButton :$isClickChromecastSetupButton")
        if (isClickChromecastSetupButton) {

            showChromecastSetupDone()
        }

        //show aler
        var vaAreReadyDialogFragment = VaAreReadyDialogFragment()
        vaAreReadyDialogFragment.onCloseListener = object : VaAreReadyDialogFragment.OnCloseListener {
            override fun onClose() {
//                setResult(RESULT_CODE)
//                finish()
            }
        }
        vaAreReadyDialogFragment.show(supportFragmentManager, VaAreReadyDialogFragment::class.java.simpleName)
    }

    private var availableView: ChromecastBuiltInAvailableDlgView? = null
    private fun showChromecastSetupDone() {
        if (availableView != null) {
            availableView = if (availableView!!.isShowing()!!) return else {
                availableView!!.dismissDialog()
                null
            }
        }
        availableView = ChromecastBuiltInAvailableDlgView(this, 2)

        availableView?.show()
    }


    private var setupChromecastDialogFragment: SetupGoogleAssistantAndChromecastDialogFragment? = null
    private var setupGoogleAssistantDialogFragment: SetupGoogleAssistantDialogFragment? = null
    private var isClickChromecastSetupButton = false
    private fun showGoogleSetupDialog() {
        if (setupGoogleAssistantDialogFragment == null) {
            setupGoogleAssistantDialogFragment = SetupGoogleAssistantDialogFragment()
            setupGoogleAssistantDialogFragment?.onCloseListener = object : SetupGoogleAssistantDialogFragment.OnCloseListener {
                override fun onClose() {
                    setResult(RESULT_CODE)
                    finish()
                }
            }
        }

        if (setupGoogleAssistantDialogFragment?.isAdded == false) {
            setupGoogleAssistantDialogFragment?.show(supportFragmentManager, SetupGoogleAssistantDialogFragment::class.java.simpleName)
        }
//
//        if (setupChromecastDialogFragment == null) {
//            setupChromecastDialogFragment = SetupGoogleAssistantAndChromecastDialogFragment()
//        }
//        if (setupChromecastDialogFragment?.isAdded == false) {
//            setupChromecastDialogFragment?.onCloseListener = object : SetupGoogleAssistantAndChromecastDialogFragment.OnCloseListener {
//                override fun onClose() {
//                  setResult(RESULT_CODE)
    //             finish()
//                }
//            }
//            setupChromecastDialogFragment?.setOnSetupListener(object : SetupGoogleAssistantAndChromecastDialogFragment.OnSetupListener {
//                override fun onSetup() {
//                    isClickChromecastSetupButton = true
//                }
//
//            })
//            setupChromecastDialogFragment?.show(supportFragmentManager, SetupGoogleAssistantAndChromecastDialogFragment::class.java.simpleName)
//        }
    }

    override fun onDestroy() {
        super.onDestroy()
//        googleCastEnableDialog?.dismiss()
    }

    private fun parseBundle(): OneDevice? {
        val uuid = intent.getStringExtra(BUNDLE_UUID)
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "parseBundle() >>> missing uuid")
            return null
        }

        val device = DeviceStore.findOne(uuid = uuid)
        if (null == device) {
            Logger.e(TAG, "parseBundle() >>> can't find one device based on uuid:$uuid")
        }

        return device
    }

    private fun bindingData(device: OneDevice) {
        viewModel = ViewModelProvider(
            this,
            VAGoogleViewModelFactory(device = device)
        )[VAGoogleViewModel::class.java]


    }

    fun clickGoToGoogleApp() {
        UIUtils.openAppPage(this, UIUtils.PACKAGE_NAME_GOOGLE_HOME_APP, "googlehome://devices")
    }
    fun clickLogout() {
        showGoogleLogoutDlg()
    }
    fun clickChromecastBuildIn() {
        portalUrl(
            config = AppConfigurationUtils.getChromecastBuiltInUrl(),
            default = chromecast_built_in
        )
//        UIUtils.openWebUrl(this, "https://www.google.com/chromecast/built-in/audio/")
    }
    fun clickMultiRoomMusic() {
        portalUrl(
            config = AppConfigurationUtils.getMultiroomMusicGoogleUrl(),
            default = multiroom_music_google
        )
//            UIUtils.openWebUrl(this, "https://support.google.com/assistant/answer/9210727?hl")
    }
    fun clickMyAssistantActivity() {
        portalUrl(
            config = AppConfigurationUtils.getMyAssistantActivity(),
            default = my_assistant_activity
        )
//        UIUtils.openWebUrl(this, "https://myactivity.google.com/product/assistant?utm_source=assistant-3p&utm_medium=jblauthentics")
    }

    private var logoutDialog: GoogleLogoutDialog? = null
    private fun showGoogleLogoutDlg() {
        if (logoutDialog != null && logoutDialog?.isShowing() == true) {
            Log.d(TAG, "showGoogleLogoutDlg: return")
            return
        }
        logoutDialog = this?.let { GoogleLogoutDialog(it) }
        logoutDialog?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
            override fun onConfirm(any: Any?) {
                logoutGoogle()
            }

            override fun onCancel() {
            }

        })
        logoutDialog?.show()
    }

    private fun logoutGoogle() {

        WAApplication.me.showProgDlg(this, true, "")
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val flag = viewModel?.googleLogoutWithTimeout()
            Logger.i(TAG, "logoutGoogle() >>> check result[$flag]")
            delay(5000)
            WAApplication.me.showProgDlg(this@VAGoogleActivity, false, "")
            setResult(RESULT_CODE)
            finishAfterTransition()
        }


    }






    companion object {

        fun portal(context: Context?, device: OneDevice?): Boolean {
            context ?: return false
            device ?: return false

            val intent = Intent(context, VAGoogleActivity::class.java)
            intent.putExtra(BUNDLE_UUID, device.UUID)
            context.startActivity(intent)
            return true
        }

        fun portal(launcher: ActivityResultLauncher<Intent>, activity: Activity?, device: OneDevice?): Boolean {
            activity ?: return false
            device ?: return false
            val intent = Intent(activity, VAGoogleActivity::class.java)
            intent.putExtra(BUNDLE_UUID, device.UUID)
            launcher.launch(intent)
            return true
        }

        private const val BUNDLE_UUID = "Bundle_UUID"

        private const val TAG = "VAGoogleActivity"

        private const val chromecast_built_in = "https://www.google.com/chromecast/built-in/audio/"

        private const val multiroom_music_google = "https://support.google.com/assistant/answer/9210727?hl"

        private const val my_assistant_activity = "https://myactivity.google.com/product/assistant?utm_source=assistant-3p&utm_medium=jblauthentics"

        const val RESULT_CODE = 33
    }
}