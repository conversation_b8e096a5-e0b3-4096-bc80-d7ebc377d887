package com.harman.calibration

import android.view.LayoutInflater
import android.view.View
import com.harman.bar.app.databinding.LayoutCalibrationResultBinding
import com.harman.bar.app.databinding.LayoutCalibrationSodFirstPlacementStepOneBinding
import com.harman.bar.app.databinding.LayoutCalibrationSodFirstPlacementStepTwoBinding
import com.harman.bar.app.databinding.LayoutCalibrationSodFirstTuningBinding
import com.harman.bar.app.databinding.LayoutCalibrationSodSecondPlacementBinding
import com.harman.bar.app.databinding.LayoutCalibrationSodSecondTuningBinding
import com.harman.bar.app.databinding.LayoutCalibrationTuningBinding

/**
 * Created by gerrar<PERSON><PERSON><PERSON> on 2024/8/8.
 */
sealed interface IState {

    fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View

    val calibrating: Boolean
}

// -------------- Non SOD device state --------------
object Setup : IState {

    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationTuningBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity as? CalibrationActivity
        }.root
    }

    override val calibrating: Boolean = true
}

// -------------- Both SOD and Non SOD device state --------------
object Complete : IState {

    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationResultBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity
        }.root
    }

    override val calibrating: Boolean = false
}

// -------------- SOD device state --------------
object SodFirstPlacementStepOne : IState {
    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationSodFirstPlacementStepOneBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity as? CalibrationSODActivity
        }.root
    }

    override val calibrating: Boolean = true
}

object SodFirstPlacementStepTwo : IState {
    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationSodFirstPlacementStepTwoBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity as? CalibrationSODActivity
        }.root
    }

    override val calibrating: Boolean = true
}

object SodFirstTuning : IState {
    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationSodFirstTuningBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity as? CalibrationSODActivity
        }.root
    }

    override val calibrating: Boolean = true
}

object SodSecondPlacement : IState {
    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationSodSecondPlacementBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity as? CalibrationSODActivity
        }.root
    }
    override val calibrating: Boolean = true
}

object SodSecondTuning : IState {
    override fun genRootView(activity: CalibrationBaseActivity, inflater: LayoutInflater): View {
        return LayoutCalibrationSodSecondTuningBinding.inflate(inflater).also { binding ->
            binding.lifecycleOwner = activity
            binding.activity = activity as? CalibrationSODActivity
        }.root
    }
    override val calibrating: Boolean = true
}
