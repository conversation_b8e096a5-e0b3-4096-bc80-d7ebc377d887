package com.harman;

public enum VAResulType {
    SUCCESS_NONE(EventUtils.success_none,0),
    SUCCESS_ALEXA(EventUtils.success_alexa,1),
    SUCCESS_GVA(EventUtils.success_gva,2),
    SUCCESS_GVA_ALEXA(EventUtils.success_gva_alexa,3);

    private String name;
    private int value;
    VAResulType(String name, int value){
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }



    public static VAResulType getItemByName(String name){
        for(VAResulType item : values()){
            if(item.getName().equals(name)){
                return item;
            }
        }
        return null;
    }

    public static VAResulType getItemByValue(int value){
        for(VAResulType item : values()){
            if(item.getValue() == value){
                return item;
            }
        }
        return null;
    }
}
