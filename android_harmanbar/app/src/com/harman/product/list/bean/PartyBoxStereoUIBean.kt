package com.harman.product.list.bean

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.blankj.utilcode.util.ObjectUtils
import com.harman.bar.app.R
import com.harman.deviceImgPath
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.info.AudioChannel
import com.harman.displayShortDeviceName
import com.harman.hkone.EnumBatteryViewChannel
import com.harman.log.Logger
import com.harman.product.list.EnumUIConnectStatus
import com.harman.product.list.EnumViewHolderType
import com.harman.supportBattery
import com.harman.uiConnectStatus

/**
 * Created by sky on 2024/11/26.
 *
 * Bean type represent an  one stereo which displayed on my product list.
 */
class PartyBoxStereoUIBean(
    device: PartyBoxDevice
) : BaseProductUIBean<PartyBoxDevice>(
    dataSource = device
) {
    init {
        Logger.d("PartyBoxStereoUIBean", "PartyBoxStereoUIBean init channel = ${dataSource.audioChannel}")
    }
    private val stereoImageBean = PBStereoImageBean(device)
    override val firstTouchTime: Long = dataSource.firstTouchTime

    override val isOffline = dataSource.isOffline

    override val viewType: Int = EnumViewHolderType.PARTY_BOX_STEREO.ordinal

    override val pid = dataSource.pid

    override val uiConnectStatus: EnumUIConnectStatus = dataSource.uiConnectStatus()

    val deviceName = dataSource.groupName ?: dataSource.displayShortDeviceName()

    val icConnectStatusVisible: Boolean = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE,
        EnumUIConnectStatus.BLUETOOTH_CONNECTED,
        EnumUIConnectStatus.READY_TO_CONNECT -> true
        EnumUIConnectStatus.OFFLINE, EnumUIConnectStatus.BLE_CONNECTED -> false
    }

    @get:ColorRes
    val statusDeviceNameTextColor = when (uiConnectStatus) {
        EnumUIConnectStatus.OFFLINE -> R.color.fg_disabled
        else -> R.color.fg_primary
    }

    @get:DrawableRes
    val icConnectStatusRes = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE -> R.drawable.component_svg_icon_connect_type_wifi
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.drawable.component_svg_icon_connect_type_bt
        EnumUIConnectStatus.READY_TO_CONNECT -> R.drawable.component_svg_icon_ready_to_connect
        EnumUIConnectStatus.OFFLINE, EnumUIConnectStatus.BLE_CONNECTED -> null
    }

    @get:ColorRes
    val tvConnectStatusColor = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE -> R.color.fg_activate
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.color.fg_activate
        EnumUIConnectStatus.OFFLINE -> R.color.fg_disabled
        EnumUIConnectStatus.READY_TO_CONNECT -> R.color.orange_2
        EnumUIConnectStatus.BLE_CONNECTED -> null
    }

    @get:StringRes
    val tvConnectStatusText = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE -> R.string.online
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.string.jbl_Connected
        EnumUIConnectStatus.OFFLINE -> R.string.unavailable
        EnumUIConnectStatus.READY_TO_CONNECT -> R.string.ready_to_connect
        EnumUIConnectStatus.BLE_CONNECTED -> null
    }

    val batteryRelatedVisible = dataSource.isOnline && dataSource.supportBattery()

    val imgUrl = dataSource.deviceImgPath()

    val leftImgUrl = stereoImageBean.getLeftImgUrl()

    val rightImgUrl = stereoImageBean.getRightImgUrl()

    val offset = stereoImageBean.getOffset()

     fun getDeviceBattery(): BatteryAreaUiBean {
        var leftIsCharging: Boolean
        var leftBatteryLevel: Int

        var rightIsCharging: Boolean
        var rightBatteryLevel: Int
        if (dataSource.audioChannel == AudioChannel.STEREO_LEFT) {
            leftIsCharging = dataSource.isCharging
            leftBatteryLevel = dataSource.batteryLevel
            rightIsCharging = dataSource.secondaryInfo?.isCharging ?: false
            rightBatteryLevel = dataSource.secondaryInfo?.batteryLevel ?: 0

        } else {
            leftIsCharging = dataSource.secondaryInfo?.isCharging ?: false
            leftBatteryLevel = dataSource.secondaryInfo?.batteryLevel ?: 0

            rightIsCharging = dataSource.isCharging
            rightBatteryLevel = dataSource.batteryLevel
        }
        val leftBatteryItem =
            BatteryAreaUiBean.Item(leftIsCharging, leftBatteryLevel, EnumBatteryViewChannel.LEFT)
        val rightBatteryItem =
            BatteryAreaUiBean.Item(rightIsCharging, rightBatteryLevel, EnumBatteryViewChannel.RIGHT)
        return BatteryAreaUiBean(listOf(leftBatteryItem, rightBatteryItem))
    }

    override fun hashCode(): Int {
        return ObjectUtils.hashCode(dataSource.groupID)
    }

    override fun equals(obj: Any?): Boolean {
        if (this === obj) return true
        if (obj == null || javaClass != obj.javaClass) return false
        val other: PartyBoxStereoUIBean = obj as PartyBoxStereoUIBean

        return dataSource.groupID == other.dataSource.groupID
    }


}