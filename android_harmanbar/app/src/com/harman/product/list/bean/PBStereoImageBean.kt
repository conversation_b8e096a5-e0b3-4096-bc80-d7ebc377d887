package com.harman.product.list.bean

import com.harman.colorId
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.info.AudioChannel
import com.harman.log.Logger
import com.jbl.one.configuration.AppConfigurationUtils

/**
 * Created by sky on 2025/6/19.
 */
class PBStereoImageBean(override val dataSource: PartyBoxDevice) : StereoImageBean(
    dataSource = dataSource
) {

    override fun getLeftImgUrl() =
        if (dataSource.audioChannel == AudioChannel.STEREO_LEFT) getMainDeviceImgPath() else getSecondaryDeviceImgPath()

    override fun getRightImgUrl() =
        if (dataSource.audioChannel == AudioChannel.STEREO_RIGHT) getMainDeviceImgPath() else getSecondaryDeviceImgPath()


    private fun getMainDeviceImgPath(): String? {
        Logger.d(TAG, "getMainDeviceImgPath      start")
        val pid = dataSource.pid ?: return null
        val colorID = dataSource.colorId() ?: return null
        val path = AppConfigurationUtils.getModelRenderPath(
            pid, colorID
        )
        Logger.d(TAG, "getMainDeviceImgPath      url: $path")
        return path
    }

    private fun getSecondaryDeviceImgPath(): String? {
        Logger.d(TAG, "getSecondaryDeviceImgPath start")
        val pid = dataSource.secondaryInfo?.pid ?: dataSource.pid ?: return null
        val colorID = dataSource.secondaryInfo?.colorID ?: dataSource.colorId() ?: return null
        val path = AppConfigurationUtils.getModelRenderPath(
            pid, colorID
        )
        Logger.d(TAG, "getSecondaryDeviceImgPath url: $path")
        return path
    }

    companion object {
        private const val TAG = "PartyBoxStereoImageBean"
    }
}
