package com.harman.product.list.bean

import com.harman.command.one.bean.GetGroupInfo
import com.harman.deviceImgPath
import com.harman.discover.bean.OneDevice

/**
 * Created by sky on 2025/6/19.
 */
class OneStereoImageBean(override val dataSource: OneDevice, val getGroupInfo: GetGroupInfo) :
    StereoImageBean(
        dataSource = dataSource
    ) {

    override fun getLeftImgUrl() = getGroupInfo.groupInfo?.getLeftChannelMember()?.deviceImgPath()

    override fun getRightImgUrl() = getGroupInfo.groupInfo?.getRightChannelMember()?.deviceImgPath()

}
