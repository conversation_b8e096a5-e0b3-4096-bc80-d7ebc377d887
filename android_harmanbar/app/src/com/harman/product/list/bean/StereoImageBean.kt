package com.harman.product.list.bean

import com.harman.discover.bean.Device
import com.harman.dpToPx
import com.harman.isOrientationVertical

/**
 * Created by sky on 2025/6/18.
 *
 */
abstract class StereoImageBean(
    open val dataSource: Device?
) {

    abstract fun getLeftImgUrl(): String?

    abstract fun getRightImgUrl(): String?

    fun getOffset() = (if(this.dataSource?.isOrientationVertical() == true) OFFSET_VERTICAL else OFFSET_HORIZONTAL).dpToPx()


    companion object {
        /**
         * When the stereo group is displayed, the distance between the two speakers is updated according to the direction of the speaker.
         * The distance standards are as follows:https://www.figma.com/design/t13IV0OHfqUw3EgipGtEzx/%F0%9F%9F%A8-One-Asset-Library?node-id=1121-4772&t=nNnbMEKPpRmBAEWU-4
         */
        private const val OFFSET_HORIZONTAL: Float = 120f
        private const val OFFSET_VERTICAL: Float = 60f
    }

}
