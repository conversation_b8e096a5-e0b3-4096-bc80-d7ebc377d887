package com.harman.product.list.bean

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.harman.bar.app.R
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GroupMode
import com.harman.deviceImgPath
import com.harman.discover.bean.OneDevice
import com.harman.displayShortDeviceName
import com.harman.hkone.EnumBatteryViewChannel
import com.harman.product.list.EnumUIConnectStatus
import com.harman.product.list.EnumViewHolderType
import com.harman.supportBattery
import com.harman.uiConnectStatus

/**
 * Created by sky on 2024/10/11.
 *
 * Bean type represent an  one stereo which displayed on my product list.
 */
class OneHarmanCastStereoUIBean(
    private val device: OneDevice,
    val getGroupInfo: GetGroupInfo
) : BaseProductUIBean<OneDevice>(
    dataSource = device
) {
    private val stereoImageBean = OneStereoImageBean(device,getGroupInfo)
    val rightBatteryUi: BatteryAreaUiBean.Item? = run {
        if (device.supportBattery()) {
            try {
                val groupInfo = getGroupInfo.groupInfo
                val groupBattery = device.groupParameterExt?.groupParameter?.battery

                val rightBatteryItem = groupInfo?.getRightChannelMember()?.let { m ->
                    groupBattery?.find { m.id == it.id }
                }?.let {
                    BatteryAreaUiBean.Item(
                        it.isACMode,
                        it.batteryLevel,
                        EnumBatteryViewChannel.RIGHT
                    )
                }
                rightBatteryItem
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        } else {
            null
        }
    }

    val leftBatteryUi: BatteryAreaUiBean.Item? = run {
        if (device.supportBattery()) {
            try {
                val groupInfo = getGroupInfo.groupInfo
                val groupBattery = device.groupParameterExt?.groupParameter?.battery
                val leftBatteryItem = groupInfo?.getLeftChannelMember()!!.let { m ->
                    groupBattery?.find { m.id == it.id }
                }!!.let {
                    BatteryAreaUiBean.Item(
                        it.isACMode,
                        it.batteryLevel,
                        EnumBatteryViewChannel.LEFT
                    )
                }
                leftBatteryItem

            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        } else {
            null
        }
    }

    val batteryUi: BatteryAreaUiBean? = run {
        if (device.supportBattery()) {
            try {
                val groupInfo = getGroupInfo.groupInfo
                val groupBattery = device.groupParameterExt!!.groupParameter.battery!!
                val leftBatteryItem = groupInfo?.getLeftChannelMember()!!.let { m ->
                    groupBattery.find { m.id == it.id }
                }!!.let {
                    BatteryAreaUiBean.Item(
                        it.isACMode,
                        it.batteryLevel,
                        EnumBatteryViewChannel.LEFT
                    )
                }
                val rightBatteryItem = groupInfo.getRightChannelMember()!!.let { m ->
                    groupBattery.find { m.id == it.id }
                }!!.let {
                    BatteryAreaUiBean.Item(
                        it.isACMode,
                        it.batteryLevel,
                        EnumBatteryViewChannel.RIGHT
                    )
                }
                BatteryAreaUiBean(listOf(leftBatteryItem, rightBatteryItem))
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        } else {
            null
        }
    }

    override val firstTouchTime: Long = dataSource.firstTouchTime

    override val isOffline = dataSource.isOffline

    override val viewType: Int = EnumViewHolderType.ONE_HARMANCAST_STEREO.ordinal

    override val pid = dataSource.pid

    override val uiConnectStatus: EnumUIConnectStatus = dataSource.uiConnectStatus()

    val deviceName = if(getGroupInfo.groupMode == GroupMode.GROUP.value) getGroupInfo?.groupInfo?.group?.name?: dataSource.displayShortDeviceName() else dataSource.displayShortDeviceName()


    val icConnectStatusVisible = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE,
        EnumUIConnectStatus.BLUETOOTH_CONNECTED,
        EnumUIConnectStatus.READY_TO_CONNECT -> true
        EnumUIConnectStatus.OFFLINE, EnumUIConnectStatus.BLE_CONNECTED -> false
    }

    @get:ColorRes
    val statusDeviceNameTextColor = when (uiConnectStatus) {
        EnumUIConnectStatus.OFFLINE -> R.color.fg_disabled
        else -> R.color.fg_primary
    }


    @get:DrawableRes
    val icConnectStatusRes = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE -> R.drawable.component_svg_icon_connect_type_wifi
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.drawable.component_svg_icon_connect_type_bt
        EnumUIConnectStatus.READY_TO_CONNECT -> R.drawable.component_svg_icon_ready_to_connect
        EnumUIConnectStatus.OFFLINE, EnumUIConnectStatus.BLE_CONNECTED -> null
    }

    @get:ColorRes
    val tvConnectStatusColor = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE -> R.color.fg_activate
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.color.fg_activate
        EnumUIConnectStatus.OFFLINE -> R.color.bg_s3
        EnumUIConnectStatus.READY_TO_CONNECT -> R.color.orange_2
        EnumUIConnectStatus.BLE_CONNECTED -> null
    }

    @get:StringRes
    val tvConnectStatusText = when (uiConnectStatus) {
        EnumUIConnectStatus.WIFI_ONLINE -> R.string.online
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.string.jbl_Connected
        EnumUIConnectStatus.OFFLINE -> R.string.unavailable
        EnumUIConnectStatus.READY_TO_CONNECT -> R.string.ready_to_connect
        EnumUIConnectStatus.BLE_CONNECTED -> null
    }



    val batteryRelatedVisible = dataSource.isOnline && dataSource.supportBattery()

    val imgUrl = dataSource.deviceImgPath()
    val offset = stereoImageBean.getOffset()

    val leftImgUrl = stereoImageBean.getLeftImgUrl()

    val rightImgUrl = stereoImageBean.getRightImgUrl()

}