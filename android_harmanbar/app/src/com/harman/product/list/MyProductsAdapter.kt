package com.harman.product.list

import androidx.core.view.isVisible
import androidx.databinding.ViewDataBinding
import com.harman.bar.app.databinding.LayoutItemMyProductGeneralBinding
import com.harman.bar.app.databinding.LayoutItemMyProductOneBinding
import com.harman.bar.app.databinding.LayoutItemMyProductOneMultichannelBinding
import com.harman.bar.app.databinding.LayoutItemMyProductOneStereoBinding
import com.harman.bar.app.databinding.LayoutItemMyProductPartybandBinding
import com.harman.bar.app.databinding.LayoutItemMyProductPartyboxStereoBinding
import com.harman.bar.app.databinding.LayoutItemMyProductPartylightBinding
import com.harman.BaseMultiAdapter
import com.harman.bar.app.R
import com.harman.deviceImgPath
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible
import com.harman.product.list.bean.BaseProductUIBean
import com.harman.product.list.bean.GeneralProductUIBean
import com.harman.product.list.bean.OneHarmanCastMultiChannelUIBean
import com.harman.product.list.bean.OneHarmanCastStereoUIBean
import com.harman.product.list.bean.OneProductUIBean
import com.harman.product.list.bean.PartyBoxStereoUIBean
import com.harman.product.list.bean.PartyLightGroupUIBean
import com.harman.uiConnectStatus
import com.harman.discover.util.Tools.hasWhatsNew
import com.harman.loadImage
import com.harman.hkone.BatteryView
import com.harman.hkone.EnumBatteryViewChannel
import com.harman.loadingStyle2
import com.harman.partylight.util.invisible
import com.harman.product.list.bean.PartyBandUIBean

/**
 * Created by gerardzhang on 2024/2/26.
 */
class MyProductsAdapter(
    private val fragment: MyProductsFragment
) : BaseMultiAdapter<BaseProductUIBean<*>>(
    data = fragment.productBeans.value
) {

    override fun viewType(position: Int): Int =
        getOrNull(position)?.viewType ?: EnumViewHolderType.GENERAL.ordinal

    override fun layoutId(viewType: Int): Int =
        EnumViewHolderType.getLayoutId(viewType) ?: EnumViewHolderType.GENERAL.layoutId

    override fun bind(binding: ViewDataBinding, position: Int) {
        val bean = getOrNull(position) ?: return

        if (binding is LayoutItemMyProductGeneralBinding && bean is GeneralProductUIBean) {
            binding.fragment = fragment
            binding.bean = bean
            binding.lifecycleOwner = fragment
        } else if (binding is LayoutItemMyProductOneBinding && bean is OneProductUIBean) {
            binding.fragment = fragment
            binding.bean = bean
            binding.lifecycleOwner = fragment
            buildOneProduct(binding, bean)
        } else if (binding is LayoutItemMyProductOneStereoBinding && bean is OneHarmanCastStereoUIBean) {
            binding.fragment = fragment
            binding.bean = bean
            binding.lifecycleOwner = fragment
        } else if (binding is LayoutItemMyProductOneMultichannelBinding && bean is OneHarmanCastMultiChannelUIBean) {
            binding.fragment = fragment
            binding.bean = bean
            binding.lifecycleOwner = fragment
        } else if (binding is LayoutItemMyProductPartylightBinding && bean is PartyLightGroupUIBean) {
            binding.fragment = fragment
            binding.bean = bean
            binding.lifecycleOwner = fragment
        } else if (binding is LayoutItemMyProductPartyboxStereoBinding && bean is PartyBoxStereoUIBean) {
            binding.fragment = fragment
            binding.bean = bean
            binding.lifecycleOwner = fragment
            buildPartyStereoProduct(binding, bean)
        } else if (binding is LayoutItemMyProductPartybandBinding && bean is PartyBandUIBean) {
            buildPartyBandProduct(binding, bean)
        }
    }

    override fun isSameItem(old: BaseProductUIBean<*>, new: BaseProductUIBean<*>) =
        old.dataSource == new.dataSource

    override fun isSameContent(old: BaseProductUIBean<*>, new: BaseProductUIBean<*>): Boolean =
        if (old is GeneralProductUIBean && new is GeneralProductUIBean) {
            old.uiConnectStatus == new.uiConnectStatus &&
                    old.deviceName == new.deviceName &&
                    old.pid == new.pid &&
                    old.uiConnectStatus == new.uiConnectStatus &&
                    old.batteryRelatedVisible == new.batteryRelatedVisible &&
                    old.dataSource.batteryLevel == new.dataSource.batteryLevel &&
                    old.dataSource.isCharging == new.dataSource.isCharging &&
                    old.imgUrl == new.imgUrl
        } else if (old is OneProductUIBean && new is OneProductUIBean) {
            old.uiConnectStatus == new.uiConnectStatus &&
                    old.pid == new.pid &&
                    old.deviceName == new.deviceName &&
                    old.dataSource.hasWhatsNew() == new.dataSource.hasWhatsNew() &&
                    old.uiConnectStatus == new.uiConnectStatus &&
                    old.batteryRelatedVisible == new.batteryRelatedVisible &&
                    old.imgUrl == new.imgUrl &&
                    old.batteryUi == new.batteryUi
        } else if (old is OneHarmanCastStereoUIBean && new is OneHarmanCastStereoUIBean) {
            old.uiConnectStatus == new.uiConnectStatus &&
                    old.pid == new.pid &&
                    old.deviceName == new.deviceName &&
                    old.dataSource.hasWhatsNew() == new.dataSource.hasWhatsNew() &&
                    old.uiConnectStatus == new.uiConnectStatus &&
                    old.batteryRelatedVisible == new.batteryRelatedVisible &&
                    old.offset == new.offset &&
                    old.leftBatteryUi == new.leftBatteryUi &&
                    old.rightBatteryUi == new.rightBatteryUi
        } else if (old is OneHarmanCastMultiChannelUIBean && new is OneHarmanCastMultiChannelUIBean) {
            old.uiConnectStatus == new.uiConnectStatus &&
                    old.displayDeviceName == new.displayDeviceName &&
                    old.uiConnectStatus == new.uiConnectStatus &&
                    old.deviceImgPath == new.deviceImgPath &&
                    old?.getGroupInfo?.onlineDevices?.size == new?.getGroupInfo?.onlineDevices?.size
        } else if (old is PartyLightGroupUIBean && new is PartyLightGroupUIBean) {
            old.uiConnectStatus == new.uiConnectStatus &&
                    old == new
        } else if (old is PartyBoxStereoUIBean && new is PartyBoxStereoUIBean) {
            old.uiConnectStatus == new.uiConnectStatus &&
                    old.deviceName == new.deviceName &&
                    old.pid == new.pid &&
                    old.batteryRelatedVisible == new.batteryRelatedVisible &&
                    old.leftImgUrl == new.leftImgUrl &&
                    old.rightImgUrl == new.rightImgUrl &&
                    old.offset == new.offset &&
                    old.getDeviceBattery() == new.getDeviceBattery() &&
                    old.dataSource.partyConnectStatus == new.dataSource.partyConnectStatus
        } else if (old is PartyBandUIBean && new is PartyBandUIBean) {
            old == new
        } else {
            false
        }

    private fun buildPartyBandProduct(binding: LayoutItemMyProductPartybandBinding, item: PartyBandUIBean) {
        binding.llRoot.setOnClickListener {
            fragment.onDeviceItemClick(item)
        }
        binding.tvDeviceName.text = item.deviceName
        binding.batteryLeft.apply {
            item.batteryAreaUiBean.items.firstOrNull()?.also {
                setBattery(it.batteryLv, it.isCharging, it.channel)
            }
        }
        binding.ivDevImg.apply {
            loadImage(this, item.device.deviceImgPath())
        }
        binding.connectStatusIcon.apply {
            item.icConnectStatusRes?.also {
                visible()
                setImageResource(it)
            } ?: gone()
        }
        binding.connectStatusTitle.apply {
            runCatching {
                setTextColor(context.getColor(item.tvConnectStatusColor!!))
                text = context.getString(item.tvConnectStatusText!!)
                visible()
            }.onFailure {
                gone()
            }
        }
        binding.batteryDivider.apply {
            if (binding.connectStatusTitle.isVisible) visible() else gone()
        }

        binding.lottieLoading.isVisible = item.isBLEConnecting
        loadingStyle2(binding.lottieLoading, item.isBLEConnecting)
    }

    private fun buildOneProduct(binding: LayoutItemMyProductOneBinding, item: OneProductUIBean) {
        item.batteryUi?.also {
            binding.batteryDivider.visible()
            binding.batteryLeft.visible()
            when (it.items.size) {
                1 -> {
                    it.items[0].also { battery ->
                        decoBattery(
                            device = item.dataSource, batteryView = binding.batteryLeft,
                            batteryLv = battery.batteryLv, isCharging = battery.isCharging,
                            channel = battery.channel
                        )
                    }
                    binding.batteryRight.gone()
                }

                2 -> {
                    it.items[0].also { battery ->
                        decoBattery(
                            device = item.dataSource, batteryView = binding.batteryLeft,
                            batteryLv = battery.batteryLv, isCharging = battery.isCharging,
                            channel = battery.channel
                        )
                    }
                    it.items[1].also { battery ->
                        decoBattery(
                            device = item.dataSource, batteryView = binding.batteryRight,
                            batteryLv = battery.batteryLv, isCharging = battery.isCharging,
                            channel = battery.channel
                        )
                    }
                    binding.batteryRight.visible()
                }

                else -> {
                    //not compatible
                }
            }
        } ?: run {
            binding.batteryDivider.gone()
            binding.batteryLeft.gone()
            binding.batteryRight.gone()
        }
    }

    private fun decoBattery(
        device: OneDevice, batteryView: BatteryView, isCharging: Boolean,
        batteryLv: Int, channel: EnumBatteryViewChannel
    ) {

        if (OneProductUIBean.isSoundBarSodState(device = device)) {
            batteryView.setSODBattery(tempLevel = batteryLv, charging = isCharging, channel = channel)
        } else {
            batteryView.setBattery(tempLevel = batteryLv, charging = isCharging, channel = channel)
        }
    }

    private fun buildOneStereoProduct(binding: LayoutItemMyProductOneStereoBinding, item: OneHarmanCastStereoUIBean) {
        item.batteryUi?.also {
            binding.batteryDivider.visible()
            binding.batteryLeft.visible()
            binding.batteryRight.visible()
            it.items[0].also { battery ->
                binding.batteryLeft.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
            }
            it.items[1].also { battery ->
                binding.batteryRight.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
            }
        } ?: run {
            binding.batteryDivider.gone()
            binding.batteryLeft.gone()
            binding.batteryRight.gone()
        }
    }

    private fun buildPartyStereoProduct(binding: LayoutItemMyProductPartyboxStereoBinding, item: PartyBoxStereoUIBean) {
        item.getDeviceBattery().also {
            if (item.batteryRelatedVisible) {
                binding.batteryDivider.visible()
                binding.batteryLeft.visible()
                binding.batteryRight.visible()
                it.items[0].also { battery ->
                    binding.batteryLeft.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
                }
                it.items[1].also { battery ->
                    binding.batteryRight.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
                }
            } else {
                binding.batteryDivider.invisible()
                binding.batteryLeft.invisible()
                binding.batteryRight.invisible()
            }
        }
    }

    private fun buildPartyProduct(binding: LayoutItemMyProductGeneralBinding, item: GeneralProductUIBean) {
        if (item.dataSource is PartyBoxDevice) {
            item.getDeviceBattery().also {
                binding.batteryDivider.visible()
                binding.batteryLeft.visible()
                binding.batteryRight.gone()
                it.items[0].also { battery ->
                    binding.batteryLeft.setBattery(battery.batteryLv, battery.isCharging, battery.channel)
                }

            }
        }

    }
}