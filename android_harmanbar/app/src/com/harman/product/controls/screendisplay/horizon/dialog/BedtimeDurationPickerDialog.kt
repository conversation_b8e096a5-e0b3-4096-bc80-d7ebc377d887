package com.harman.product.controls.screendisplay.horizon.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import com.harman.BottomPopUpDialog
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogBedtimeDurationTimePickerBinding
import com.harman.log.Logger
import com.skin.font.FontUtils
import java.util.Calendar

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.product.controls.screendisplay.horizon.dialog
 * @ClassName: BedtimeDurationPickerDialog
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/3/18 10:19
 * @UpdateUser:
 * @UpdateDate: 2025/3/18 10:19
 * @UpdateRemark:
 * @Version: 1.0
 */
class BedtimeDurationPickerDialog(context: Context) : BottomPopUpDialog(context) {

    companion object {
        const val TAG = "BedtimeDurationPickerDialog"
    }

    private val binding by lazy {
        DialogBedtimeDurationTimePickerBinding.inflate(layoutInflater)
    }
    private var focusColor: Int? = 0

    var onTimeCallback: ((hour: Int, min: Int) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        focusColor = context.getColor(R.color.fg_activate)
        setCancelable(true)
        setCanceledOnTouchOutside(true)
        setContentView(binding.root)
        setupTimePicker()
    }

    override fun onStart() {
        super.onStart()
        build()
    }

    private fun setupTimePicker() {//copy from history code
        binding.hmWheelTimePicker.apply {
            val calendar = Calendar.getInstance()
            val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
            val currentMinute = calendar.get(Calendar.MINUTE)
            selectedHour = currentHour
            selectedMinute = currentMinute
            Logger.d(TAG, "${selectedHour}:${selectedMinute}")
            setAtmospheric(true)
            setCurtain(true)
            curtainColor = Color.TRANSPARENT
            setHourFrame(0, 23)
            setMinuteFrame(0, 59)
            this.selectedItemTextColor = focusColor ?: 0
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }
    }

    private fun build() {
        binding.viewTouchOutside.setOnClickListener {
            this.dismiss()
        }
        binding.btnPositive.setOnClickListener { //save
            Logger.d(
                TAG,
                "starHourtime=${binding.hmWheelTimePicker.selectedHour} endMintime=${binding.hmWheelTimePicker.selectedMinute}"
            )
            onTimeCallback?.invoke(
                binding.hmWheelTimePicker.selectedHour,
                binding.hmWheelTimePicker.selectedMinute
            )
            this.dismiss()
        }
        binding.btnNegative.setOnClickListener { //cancel
            this.dismiss()
        }
    }
}