package com.harman

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.databinding.DataBindingUtil
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityDebugBinding
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyLightDevice

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/2/26.
 */
class DebugActivity : AppCompatActivity() {

    private val viewModel: DebugActivityViewModel by viewModels()

    private lateinit var deviceAdapter : DeviceListAdapter

    private val mDebugDashboardDialog: DebugDashboardDialog by lazy {
        DebugDashboardDialog()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val binding =
            DataBindingUtil.setContentView<ActivityDebugBinding>(this, R.layout.activity_debug)
        binding.lifecycleOwner = this
        binding.activity = this
        binding.viewModel = viewModel
        binding.adapter = DeviceListAdapter(this@DebugActivity, viewModel).also { adapter ->
            deviceAdapter = adapter
        }

        lifecycle.addObserver(viewModel)
    }

    override fun onResume() {
        super.onResume()
        deviceAdapter.notifyDataSetChanged()
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(viewModel)
    }

    fun onDeviceClick(baseDevice: Device?) {
        baseDevice ?: return

        mDebugDashboardDialog.updateDevice(baseDevice)
        mDebugDashboardDialog.show()
    }

    fun onDeviceLongClick(device: Device?): Boolean {
        when (device) {
            is PartyLightDevice -> {
                if (!viewModel.multiSelectedDevices.contains(device)) {
                    viewModel.multiSelectedDevices.add(device)
                } else {
                    viewModel.multiSelectedDevices.remove(device)
                }
            }
            is OneDevice -> {
                // TODO: Test OOBE
                WiFiOOBEDialog(
                    activity = this@DebugActivity,
                    device = device,
                    deviceName = device.displayDeviceName(),
                    mode = EnumMode.FULL
                ).show()
            }
        }

        return true
    }

    fun onClickPartyLightGroupOta() {
        val devices = viewModel.multiSelectedDevices.filterIsInstance<PartyLightDevice>()
        if (devices.isEmpty()) {
            return
        }

        val dialog = PartyLightGroupOtaDialog(devices)
        dialog.show()
    }

    private fun AppCompatDialogFragment.show() {
        show(supportFragmentManager, TAG)
    }

    companion object {
        private const val TAG = "DebugActivity"
    }
}