package com.harman.partylight.widget

import android.content.Context
import com.harman.bar.app.databinding.DialogCommBottomSheetBinding

/**
 * @Description common text and action bottom sheet dialog
 * <AUTHOR>
 * @Time 2024/8/12
 */
class CommBottomSheet(
    context: Context,
    val content: String = "",
    val action1Text: String = "",
    val onAction1Tap: () -> Unit = {},
    val action2Text: String = "",
    val onAction2Tap: () -> Unit = {},
    cancelable: Boolean = false,
    canceledOnTouchOutside: Boolean = false,
) : HMBottomSheet<DialogCommBottomSheetBinding>(context, cancelable, canceledOnTouchOutside) {

    override fun inflateBinding(): DialogCommBottomSheetBinding {
        return DialogCommBottomSheetBinding.inflate(layoutInflater)
    }

    override fun buildContent(binding: DialogCommBottomSheetBinding) {
        binding.tvMain.text = content
        binding.action1.text = action1Text
        binding.action1.setOnClickListener {
            dismiss()
            setOnDismissListener {
                onAction1Tap.invoke()
            }
        }
        binding.action2.text = action2Text
        binding.action2.setOnClickListener {
            dismiss()
            setOnDismissListener {
                onAction2Tap.invoke()
            }
        }
    }
}