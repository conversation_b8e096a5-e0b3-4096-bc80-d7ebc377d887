package com.harman.ota.partybox

import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Bundle
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.viewModels
import androidx.annotation.AnyThread
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.BarUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityPartyboxOtaBinding
import com.harman.deviceImgPath
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.info.EnumProductLine
import com.harman.discover.util.Tools.hasValidGroupId
import com.harman.log.Logger
import com.harman.ota.RemoteUpdateModel
import com.harman.ota.WhatsNewDialog
import com.harman.product.info.ProductInfoActivity
import com.harman.product.list.bean.PBStereoImageBean
import com.harman.task.DisconnectException
import com.harman.webview.BluetoothSettingDialogFragment
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.utils.AppPermissionUtils
import config.LogTags
import kotlin.math.ceil

/**
 * Created by gerrardzhang on 2024/4/16.
 */
class PartyBoxOtaActivity : AppCompatActivity(), IViewStyleListener {

    private var whatsNewDialog: WhatsNewDialog? = null

    private val viewModel: PartyBoxOtaViewModel by viewModels()

    private val _viewStyle = MutableLiveData<ViewStyle>(ViewStyle.Ready)
    val viewStyle: LiveData<ViewStyle>
        get() = _viewStyle

    private val _isContentOverflow = MutableLiveData<Boolean>(false)
    val isContentOverflow: LiveData<Boolean>
        get() = _isContentOverflow

    private val _leftImgUrl = MutableLiveData<String?>()
    val leftImgUrl: LiveData<String?>
        get() = _leftImgUrl

    private val _rightImgUrl = MutableLiveData<String?>()
    val rightImgUrl: LiveData<String?>
        get() = _rightImgUrl

    private val _offset = MutableLiveData<Float>()
    val offset: LiveData<Float>
        get() = _offset

    private val _showStereoLayout = MutableLiveData<Boolean>(false)
    val showStereoLayout: LiveData<Boolean>
        get() = _showStereoLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (!parseBundle(intent = intent)) {
            finish()
            return
        }

        BarUtils.setStatusBarColor(this, ContextCompat.getColor(this, R.color.transparent), true)

        val binding = genBinding()
        setContentView(binding.root)

        viewModel.viewStyleListener = this@PartyBoxOtaActivity
        lifecycle.addObserver(viewModel)

        listenSysBroadcast()
        dataBinding(binding = binding)
    }

    private fun dataBinding(binding: ActivityPartyboxOtaBinding) {
        viewModel.whatsNewContents.observe(this) {
            val textView = binding.layoutPartyboxOtaReady.tvContentsContent
            textView.viewTreeObserver.addOnGlobalLayoutListener(LayoutObserver(textView = textView))
        }
    }

    override fun onResume() {
        super.onResume()
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onPause() {
        super.onPause()
        window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    private fun genBinding() = ActivityPartyboxOtaBinding.inflate(layoutInflater).also { binding ->
        binding.viewModel = viewModel
        binding.activity = this@PartyBoxOtaActivity
        binding.lifecycleOwner = this@PartyBoxOtaActivity
    }

    private fun parseBundle(intent: Intent?): Boolean {
        intent ?: return false

        val uuid = intent.getStringExtra(BUNDLE_UUID) ?: run {
            Logger.e(TAG, "parseBundle() >>> missing UUID")
            return false
        }

        val model = intent.getParcelableExtra<RemoteUpdateModel>(BUNDLE_UPDATE_MODEL) ?: run {
            Logger.e(TAG, "parseBundle() >>> missing RemoteUpdateModel")
            return false
        }

        val device = DeviceStore.find(
            uuid = uuid,
            productLine = EnumProductLine.PARTY_BOX
        ) as? PartyBoxDevice ?: run {
            Logger.w(TAG, "parseBundle() >>> can't find device based on UUID[$uuid]")
            return false
        }

        viewModel.updateModel(model)
        viewModel.updatePartyBoxDevice(device)

        if (device.hasValidGroupId()) {
            _showStereoLayout.value = true
            PBStereoImageBean(device).apply {
                _leftImgUrl.value = getLeftImgUrl()
                _rightImgUrl.value = getRightImgUrl()
                _offset.value = getOffset()
            }
        } else {
            _showStereoLayout.value = false
        }

        return true
    }

    private fun listenSysBroadcast() {
        if (VERSION.SDK_INT >= VERSION_CODES.TIRAMISU) {
            registerReceiver(mReceiver, intentFilters, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(mReceiver, intentFilters)
        }
    }

    private val intentFilters = IntentFilter().apply {
        // BT on/off
        addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
    }

    private val mReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            // It means the user has changed his bluetooth state.
            when (intent.action) {
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    checkBTState()
                }
            }
        }
    }

    override fun onBackPressed() {
        if (viewStyle.notUpgrading()) {
            super.onBackPressed()
        } else {
            // block sys onBackPressed while in upgrading state.
        }
    }

    private fun LiveData<ViewStyle>.notUpgrading(): Boolean = when (value) {
        ViewStyle.Ready,
        ViewStyle.Success,
        ViewStyle.UpdateFail,
        ViewStyle.ReconnectError,
        ViewStyle.TransError -> true

        ViewStyle.Trans,
        ViewStyle.Install -> false

        else -> true
    }

    fun onUpdateBtnClick() {
        //check current device
        val shouldShowBTDialog = viewModel.isBTDeviceA2DPNotConnected()
        Logger.d(
            TAG,
            "onUpdateBtnClick >>> missing remoteUpdateModel shouldShowBTDialog:$shouldShowBTDialog"
        )
        if (shouldShowBTDialog) {
            showBTConnectGuideDialog(this@PartyBoxOtaActivity)
            return
        }
        viewModel.startFlow(context = this@PartyBoxOtaActivity)
    }

    private var bluetoothSettingDialogFragment: BluetoothSettingDialogFragment? = null
    private fun showBTConnectGuideDialog(activity: AppCompatActivity) {
        if (activity.isFinishing || activity.isDestroyed) {
            LogsUtil.d(LogTags.UI, "$TAG showBTConnectGuideDialog on isFinishing or isDestroyed")
            return
        }
        bluetoothSettingDialogFragment?.dismiss()
        val titleString =
            SkinResourcesUtils.getString("connected_the_product_to_bluetooth_to_continue")
        if (bluetoothSettingDialogFragment == null) {
            bluetoothSettingDialogFragment =
                BluetoothSettingDialogFragment(context = activity, titleString) {
                    LogsUtil.d(LogTags.UI, "$TAG showBTConnectGuideDialog on cancel")
                }
        }
        bluetoothSettingDialogFragment?.show()
    }


    fun onUpdateDoneBtnClick() {
        viewModel.terminateTasks()

        setResult(RESULT_OK, Intent().apply {
            putExtra(ProductInfoActivity.BUNDLE_OTA_RESULT, true)
        })
        finish()
    }

    fun onTryAgainBtnClick() {
        if (!viewModel.isBTDeviceA2DPNotConnected()) {
            Logger.d(TAG, "onTryAgainBtnClick() >>> Classic BT not connected with device")
            showBTConnectGuideDialog(activity = this)
            return
        }

        if (!AppPermissionUtils.isBluetoothEnabled()) {
            Logger.d(TAG, "onTryAgainBtnClick() >>> Phone's BT not enabled")
            showBTConnectGuideDialog(activity = this)
            return
        }

        viewModel.terminateTasks()
        viewModel.startFlow(context = this@PartyBoxOtaActivity)
    }

    fun onUpdateLaterBtnClick() {
        viewModel.terminateTasks()
        finish()
    }

    fun onGoBackToDashboardBtnClick() {
        viewModel.terminateTasks()
        finish()
    }

    fun onLearnMoreClick() {
        val model = viewModel.updateModel.value ?: return

        val contents: String? = model.findWhatsNewByLang(lan = LocaleLanConfigUtil.getLocaleLan())
            ?.points?.let { pts ->
                val sb = StringBuilder()

                pts.forEachIndexed { i, s ->
                    sb.append(i + 1).append(". ").append(s)

                    if (i < pts.size - 1) {
                        sb.append('\n').append('\n')
                    }
                }

                sb.toString()
            }

        val dialog = whatsNewDialog ?: WhatsNewDialog(
            activity = this,
            whatsNewContents = contents,
            viewModel.device.value!!.deviceImgPath(),
            model.releaseVersion,
        ).also { d ->
            whatsNewDialog = d
        }

        dialog.show()
    }

    override fun onDestroy() {
        super.onDestroy()
        whatsNewDialog?.dismiss()
        lifecycle.removeObserver(viewModel)
    }

    @AnyThread
    override fun onChange(viewStyle: ViewStyle) {
        Logger.d(TAG, "ViewStyle.onChange() >>> [$viewStyle]")
        _viewStyle.postValue(viewStyle)

        when (viewStyle) {
            ViewStyle.Success -> {
                viewModel.terminateTasks()
            }

            ViewStyle.TransError,
            ViewStyle.UpdateFail,
            ViewStyle.ReconnectError -> {
                viewModel.terminateTasks()
            }

            else -> {
                // no impl
            }
        }
    }

    private fun checkBTState() {
        if (!AppPermissionUtils.isBluetoothEnabled()) {
            Logger.d(TAG, "checkBTState() >>> Phone's BT not enabled")
            // terminateTasks() will clear lastOtaTask so syncIdleStatusWithException must invoke first
            viewModel.syncIdleStatusWithException(e = DisconnectException)
            viewModel.terminateTasks()
        } else {
            Logger.d(TAG, "checkBTState() >>> Phone's BT enabled")
            bluetoothSettingDialogFragment?.dismiss()
        }
    }

    private inner class LayoutObserver(private val textView: TextView) :
        ViewTreeObserver.OnGlobalLayoutListener {
        private fun TextView.isEllipsized(): Boolean {
            val textPixelLength = paint.measureText(text.toString())
            val numberOfLines = ceil((textPixelLength / measuredWidth).toDouble())
            return numberOfLines > 4
        }

        override fun onGlobalLayout() {
            textView.viewTreeObserver.removeOnGlobalLayoutListener(this@LayoutObserver)
            _isContentOverflow.value = textView.isEllipsized()
        }
    }

    companion object {
        private const val TAG = "PartyBoxOtaActivity"

        private const val BUNDLE_UUID = "Bundle_UUID"
        private const val BUNDLE_UPDATE_MODEL = "Bundle_Update_Model"

        fun launchOtaPage(
            activity: Activity,
            launcher: ActivityResultLauncher<Intent>?,
            device: PartyBoxDevice,
            model: RemoteUpdateModel
        ) {
            val intent = Intent(activity, PartyBoxOtaActivity::class.java).apply {
                putExtra(BUNDLE_UUID, device.UUID)
                putExtra(BUNDLE_UPDATE_MODEL, model)
            }

            if (null != launcher) {
                launcher.launch(intent)
            } else {
                activity.startActivity(intent)
            }
        }
    }
}