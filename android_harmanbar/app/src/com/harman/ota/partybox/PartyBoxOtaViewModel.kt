package com.harman.ota.partybox

import android.bluetooth.BluetoothDevice
import android.content.Context
import androidx.annotation.FloatRange
import androidx.annotation.MainThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.harman.connect.PartyBoxGattSession
import com.harman.connect.PartyBoxSppSession
import com.harman.connect.disconnectBrEdr
import com.harman.connect.isBTDeviceA2DPNotConnected
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.syncBrEdrConnectWithTimeout
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetFirmwareVersionWithTimeout
import com.harman.connect.syncSPPConnectWithTimeout
import com.harman.connect.syncWaitPartyBoxOnlineWithTimeout
import com.harman.discover.DeviceScanner
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.round
import com.harman.discover.util.Tools.roundPercentage
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.ota.RemoteUpdateModel
import com.harman.task.CancelDfuFlowException
import com.harman.task.DisconnectException
import com.harman.task.partybox.EnumPartyBoxDfuStatus
import com.harman.task.partybox.IPartyBoxDfuTaskListener
import com.harman.task.partybox.IPartyBoxTask
import com.harman.task.partybox.PartyBoxBaseOtaTaskV2
import com.harman.task.partybox.PartyBoxOtaTask
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.toMin
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 * Created by gerrardzhang on 2024/4/16.
 */
var isPbOtaProcessing = false
    private set

class PartyBoxOtaViewModel : ViewModel(), DefaultLifecycleObserver {

    private val _device = MutableLiveData<PartyBoxDevice>()
    val device: LiveData<PartyBoxDevice>
        get() = _device

    private val _updateModel = MutableLiveData<RemoteUpdateModel>()
    val updateModel: LiveData<RemoteUpdateModel>
        get() = _updateModel

    private val _ableUpdate = MutableLiveData<Boolean>()
    val ableUpdate: LiveData<Boolean>
        get() = _ableUpdate

    val whatsNewTitle: LiveData<String?> = _updateModel.map { model ->
        model.releaseVersion
    }

    val whatsNewContents: LiveData<String?> = _updateModel.map { model ->
        model.findWhatsNewByLang(lan = LocaleLanConfigUtil.getLocaleLanForWeb())
            ?.points?.let { pts ->
                val sb = StringBuilder()

                pts.forEachIndexed { i, s ->
                    sb.append(i + 1).append(". ").append(s)

                    if (i < pts.size - 1) {
                        sb.append('\n')
                    }
                }

                sb.toString()
            }
    }

    val targetVersion: LiveData<String?> = _updateModel.map { model ->
        model.releaseVersion
    }

    private val _transProgress = MutableLiveData<Int>(0)
    val transProgress: LiveData<Int>
        get() = _transProgress

    private val _installProgress = MutableLiveData<Int>(0)
    val installProgress: LiveData<Int>
        get() = _installProgress

    private val _transRemainingMinutes = MutableLiveData<Int>(INIT_TRANS_REMAINING_MIN)
    val transRemainingMinutes: LiveData<Int>
        get() = _transRemainingMinutes

    private val _installRemainingMinutes = MutableLiveData<Int>(INSTALL_WAITING_TIMEOUT_MILLS.toMin())
    val installRemainingMinutes: LiveData<Int>
        get() = _installRemainingMinutes

    private var lastOtaTask: IPartyBoxTask? = null
    private var installRemainingTimerJob: Job? = null
    private var flowJob: Job? = null

    init {
        isPbOtaProcessing = true
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        DeviceScanner.registerObserver(scanListener)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        _device.value?.unregisterDeviceListener(deviceListener)
        DeviceScanner.unregisterObserver(scanListener)
    }

    override fun onCleared() {
        super.onCleared()
        isPbOtaProcessing = false
    }

    @MainThread
    internal fun updatePartyBoxDevice(device: PartyBoxDevice) {
        Logger.i(TAG, "updatePartyBoxDevice() >>> \n$device")
        _device.value?.unregisterDeviceListener(deviceListener)

        device.registerDeviceListener(deviceListener)
        _device.value = device
        updateAbleUpdate()
    }

    @MainThread
    internal fun updateModel(model: RemoteUpdateModel) {
        Logger.i(TAG, "updateModel() >>> \n$model")
        _updateModel.value = model
    }

    internal var viewStyleListener: IViewStyleListener? = null

    @MainThread
    fun startFlow(context: Context) {
        DeviceScanner.stopScan(context = context, clearStore = false)

        val device = _device.value ?: run {
            Logger.e(TAG, "startFlow() >>> missing device")
            viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
            return
        }

        val uuid = device.UUID
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "startFlow() >>> missing device UUID")
            viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
            return
        }

        Logger.d(TAG, "startFlow() >>> device:\n$device")
        val targetVersion = targetVersion.value ?: run {
            Logger.e(TAG, "startFlow() >>> missing target version")
            viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
            return
        }

        flowJob = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            if (syncTrans(context = context, device = device)) {
                syncInstallWaiting(context = context, uuid = uuid, targetVersion = targetVersion)
            }
        }
    }

    @MainThread
    private suspend fun syncTrans(context: Context, device: PartyBoxDevice): Boolean {
        Logger.i(TAG, "syncTrans() >>> prepare")
        viewStyleListener?.onChange(viewStyle = ViewStyle.Trans)

        Logger.d(TAG, "syncTrans() >>> device:\n$device")

        val (version, fPath) = getLocalOtaInfo() ?: return false

        val task = if (device.supportGattOverBrEdr) {
            if (device.isBrEdrConnected) {
                Logger.d(TAG, "syncTrans() >>> disconnect last br/edr session")
                device.disconnectBrEdr()
            }

            if (!device.syncBrEdrConnectWithTimeout(context)) {
                // if close bt force on the phone. the OS might not callback onConnectionStateChange
                // so the isConnected state might not be updated in time.
                Logger.e(TAG, "syncTrans() >>> connect BR/EDR timeout")
                viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
                return false
            }
            if (!device.isBrEdrConnected) {
                Logger.e(TAG, "syncTrans() >>> this connection is not br/edr")
                return false
            }
            Logger.d(TAG, "syncTrans() >>> this connection is  br/edr")
            PartyBoxBaseOtaTaskV2(device)
        } else {
            if (!device.syncSPPConnectWithTimeout(context = context)) {
                Logger.e(TAG, "syncTrans() >>> connect spp timeout")
                viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
                return false
            }

            Logger.d(TAG, "syncTrans() >>> spp connect connected")
            val sppSession = device.sppSession as? PartyBoxSppSession ?: run {
                Logger.e(TAG, "syncTrans() >>> can't get spp session")
                viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
                return false
            }
            PartyBoxOtaTask(sppSession = sppSession, gattSession = device.gattSession as? PartyBoxGattSession)
        }
        task.registerListener(dfuListener)
        lastOtaTask = task
        Logger.i(TAG, "syncTrans() >>> task start")
        return task.startOrResumeDfuFlow(
            platform = device.bleDevice?.platform,
            remoteFirmwareVersion = version,
            otaFilePath = fPath
        )
    }

    private fun getLocalOtaInfo(): LocalOtaInfo? {
        val version = _updateModel.value?.releaseVersion
        if (version.isNullOrBlank()) {
            Logger.e(TAG, "startUpdate() >>> can't get target ota firmware version")
            viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
            return null
        }

        val fPath = _updateModel.value?.localFilePath
        if (fPath.isNullOrBlank()) {
            Logger.e(TAG, "startUpdate() >>> can't get local file path")
            viewStyleListener?.onChange(viewStyle = ViewStyle.TransError)
            return null
        }

        Logger.d(TAG, "startUpdate() >>> ota version[$version] file path:$fPath")
        return LocalOtaInfo(version = version, fPath = fPath)
    }

    private data class LocalOtaInfo(
        val version: String,
        val fPath: String
    )

    @MainThread
    private suspend fun syncInstallWaiting(context: Context, uuid: String, targetVersion: String) {
        Logger.d(TAG, "syncInstallWaiting() >>> prepare")
        launchInstallWaitingTimer()

        //Wait for the device to actively release the connection
        delay(7000)
        Logger.d(TAG, "syncInstallWaiting() >>> target version[$targetVersion]")
        val newDevice = uuid.syncWaitPartyBoxOnlineWithTimeout(
            context = context,
            timeoutMills = INSTALL_WAITING_TIMEOUT_MILLS
        ) ?: run {
            Logger.e(TAG, "syncInstallWaiting() >>> timeout waiting BLE adv")
            viewStyleListener?.onChange(viewStyle = ViewStyle.ReconnectError)
            return
        }

        val protocol = newDevice.getBLEProtocol()

        Logger.d(TAG, "syncInstallWaiting() >>> try to connect gatt with device on protocol[$protocol]:\n$newDevice")
        when (protocol) {
            BluetoothDevice.TRANSPORT_LE -> {
                if (!newDevice.syncGattConnectWithTimeout(context = context)) {
                    Logger.e(TAG, "syncInstallWaiting() >>> timeout connect gatt")
                    viewStyleListener?.onChange(viewStyle = ViewStyle.ReconnectError)
                    return
                }
            }
            BluetoothDevice.TRANSPORT_BREDR -> {
                if (!newDevice.syncBrEdrConnectWithTimeout(context = context)) {
                    Logger.e(TAG, "syncInstallWaiting() >>> timeout connect BR/EDR")
                    viewStyleListener?.onChange(viewStyle = ViewStyle.ReconnectError)
                    return
                }
            }
        }

        // update device instance to the latest one
        updatePartyBoxDevice(newDevice)
        delay(2000) // delay to avoid gatt command stuck

        Logger.d(TAG, "syncInstallWaiting() >>> try to connect get device info")
        if (!newDevice.syncGetFirmwareVersionWithTimeout(protocol = protocol)) {
            Logger.e(TAG, "syncInstallWaiting() >>> timeout get device info")
            viewStyleListener?.onChange(viewStyle = ViewStyle.UpdateFail)
            return
        }

        if (0 != Tools.compareVersion(newDevice.firmwareVersion, targetVersion)) {
            Logger.e(TAG, "syncInstallWaiting() >>> actual ver.[${newDevice.firmwareVersion}] target ver.[$targetVersion]")
            viewStyleListener?.onChange(viewStyle = ViewStyle.UpdateFail)
        } else {
            Logger.i(TAG, "syncInstallWaiting() >>> success ver.[${newDevice.firmwareVersion}]")
            viewStyleListener?.onChange(viewStyle = ViewStyle.Success)
        }
    }

    private fun launchInstallWaitingTimer() {
        installRemainingTimerJob = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            var remainMills = INSTALL_WAITING_TIMEOUT_MILLS

            while (remainMills >= 0) {
                val remainMin = remainMills.toMin()
                Logger.d(TAG, "launchInstallWaitingTimer() >>> remainMin[$remainMin]")

                _installRemainingMinutes.value = remainMin
                _installProgress.value = calInstallRemainingProgress(
                    remainMills = remainMills, total = INSTALL_WAITING_TIMEOUT_MILLS
                )
                delay(INSTALL_WAITING_GAP_MILLS)
                remainMills -= INSTALL_WAITING_GAP_MILLS
            }

            viewStyleListener?.onChange(viewStyle = ViewStyle.ReconnectError)
        }
    }

    private fun calInstallRemainingProgress(remainMills: Long, total: Long): Int {
        if (remainMills >= total) {
            return 0
        }

        if (remainMills <= 0) {
            return 100
        }

        return (((total - remainMills).toFloat() / total).round(2) * 100).toInt()
    }

    fun terminateTasks() {
        Logger.w(TAG, "terminateTasks() >>> start")
        lastPackReceiveTime = 0L

        val task = lastOtaTask
        lastOtaTask = null

        flowJob?.cancel()
        flowJob = null

        installRemainingTimerJob?.cancel()
        installRemainingTimerJob = null
        viewModelScope.launch {
            if (true == _device.value?.isSPPOnline) {
                task?.cancelOta()
            }
        }
        Logger.w(TAG, "terminateTasks() >>> finished")
    }

    private val dfuListener = object : IPartyBoxDfuTaskListener {
        override fun onDfuStatusChanged(status: EnumPartyBoxDfuStatus, e: Exception?) {
            Logger.i(TAG, "onDfuStatusChanged() >>> [${status.name}] ex:$e")

            when (status) {
                EnumPartyBoxDfuStatus.FINISH -> {
                    viewStyleListener?.onChange(ViewStyle.Install)
                }

                EnumPartyBoxDfuStatus.IDLE -> {
                    when {
                        (e is DisconnectException) -> {
                            viewStyleListener?.onChange(ViewStyle.ReconnectError)
                        }
                        (null != e && e !is CancelDfuFlowException) -> {
                            viewStyleListener?.onChange(ViewStyle.TransError)
                        }
                    }
                }

                else -> {
                    // do nothing
                }
            }
        }

        override fun onProgress(
            current: Int,
            total: Int,
            @FloatRange(from = 0.0, to = 1.0) progress: Float
        ) {
            val roundProgress = (progress * 100).roundToInt().roundPercentage()
            _transProgress.postValue(roundProgress)

            if (current % REMAINING_TIME_CALCULATE_PACKAGES_GAP == 0) {
                //The remaining time is calculated every REMAINING_TIME_CALCULATE_PACKAGES_GAP packets
                val remainingMin = calTransRemainingMills(current = current, total = total).toMin()
                Logger.d(
                    TAG, "onProgress() >>> progress[${progress.round(2)}] " +
                            "roundProgress[$roundProgress] " +
                            "current[$current] total[$total] remainingMin[$remainingMin]"
                )
                _transRemainingMinutes.postValue(remainingMin)
            }
        }
    }

    /**
     * @return latest time that trans state will cost by estimating.
     */
    private var lastPackReceiveTime = 0L
    private fun calTransRemainingMills(current: Int, total: Int): Long {
        val currentTimeMills = System.currentTimeMillis()
        val eachPackDepleteMills = currentTimeMills - lastPackReceiveTime
        val totalRemainingMills =
            if (lastPackReceiveTime == 0L) -1L else ((total - current) * eachPackDepleteMills) / REMAINING_TIME_CALCULATE_PACKAGES_GAP
        lastPackReceiveTime = currentTimeMills
        return totalRemainingMills
    }

    private fun PartyBoxDevice?.mapAbleUpdate(): Boolean {
        val device = this ?: run {
            Logger.d(TAG, "ableUpdate >>> missing device")
            return false
        }

        if (!AppConfigurationUtils.isSupportBattery(device.pid)) {
            Logger.d(TAG, "ableUpdate >>> device[${device.UUID}][${device.pid}] didn't support battery.")
            return true
        }

        Logger.d(TAG, "ableUpdate >>> device[${device.UUID}][${device.pid}] " +
                "isCharging[${device.isCharging}] batteryLv[${device.batteryLevel}]")
        return device.isCharging || device.batteryLevel >= MIN_BATTERY_LV_ALLOW_UPDATE_WITHOUT_CHARGING
    }

    private fun PartyBoxDevice?.secondaryMapAbleUpdate(): Boolean {
        val device = this ?: run {
            Logger.d(TAG, "ableUpdate >>> missing device")
            return false
        }

        if (!AppConfigurationUtils.isSupportBattery(device.pid)) {
            Logger.d(TAG, "ableUpdate >>> device[${device.UUID}][${device.pid}] didn't support battery.")
            return true
        }
        val secondaryIsCharging = device.secondaryInfo?.isCharging
        val secondaryBatteryLevel = device.secondaryInfo?.batteryLevel

        Logger.d(TAG, "ableUpdate >>> secondary device[${device.UUID}][${device.pid}] " +
            "isCharging[${secondaryIsCharging}] batteryLv[${secondaryBatteryLevel}]")
        return (secondaryIsCharging == true || (secondaryBatteryLevel != null && secondaryBatteryLevel >= MIN_BATTERY_LV_ALLOW_UPDATE_WITHOUT_CHARGING))
    }

    fun isBTDeviceA2DPNotConnected(): Boolean = _device.value?.isBTDeviceA2DPNotConnected() == true

    fun syncIdleStatusWithException(e: Exception) {
        Logger.d(TAG, "syncIdleStatusWithException() >>> $e")
        lastOtaTask?.syncIdleStatusWithException(e = e)
    }

    private val deviceListener = object : IPartyBoxDeviceListener {
        override fun onDeviceInfoUpdate() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                updateAbleUpdate()
            }
        }
    }

    private val scanListener = object : IHmDeviceObserver {
        override fun onDeviceOnlineOrUpdate(device: Device) {
            if (_device.value == device) {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    updateAbleUpdate()
                }
            }
        }
    }

    @MainThread
    private fun updateAbleUpdate() {
        val device = _device.value ?: run {
            Logger.e(TAG, "updateAbleUpdate() >>> missing device instance")
            return
        }

        Logger.d(TAG, "updateAbleUpdate() >>> main[${device.mapAbleUpdate()}] " +
                "secondary exists[${null != device.secondaryInfo}] " +
                "secondary[${device.secondaryMapAbleUpdate()}]")
        _ableUpdate.value = if (device.secondaryInfo != null) {
            device.mapAbleUpdate() && device.secondaryMapAbleUpdate()
        } else {
            device.mapAbleUpdate()
        }
    }

    companion object {
        private const val TAG = "PartyBoxOtaViewModel"

        private const val INIT_TRANS_REMAINING_MIN = 10

        private const val MIN_BATTERY_LV_ALLOW_UPDATE_WITHOUT_CHARGING = 30

        private const val REMAINING_TIME_CALCULATE_PACKAGES_GAP = 200

        private const val INSTALL_WAITING_TIMEOUT_MILLS = 4 * 60 * 1000L

        private const val INSTALL_WAITING_GAP_MILLS = 5 * 1000L
    }
}
