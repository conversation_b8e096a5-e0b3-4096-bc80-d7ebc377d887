<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="jbl_agree_with_google_terms_of_service_and_privacy_policy_to_start_using_google_cast">By clicking \"Enable Service\" below, you are indicating your agreement to the \"Google Terms Of Service\" and \"Google Privacy Policy\", which govern your use of Google Cast.</string>
    <string name="jbl_privacy_policy">\"Google Privacy Policy\"</string>
    <string name="jbl_remote_controller">Remote Controller</string>
    <string name="jbl_learn_more">Learn more</string>
    <string name="jbl_do_you_want_to_help_improve_everyone_experience_by_sharing_">Do you want to help improve everyone\'s experience by sharing device stats and crash reports with Google?\nLearn more</string>
    <string name="jbl_ota_success_You_Are_All_Set">You are all set</string>
    <string name="getting_ready_please_wait">Getting ready. Please wait.</string>
    <string name="jbl_software_update_available">Software Update available!</string>
    <string name="jbl_google_cast">Google Cast</string>
    <string name="jbl_general">General</string>
    <string name="jbl_google_terms_of_service">\"Google Terms Of Service\"</string>
    <string name="jbl_continue">CONTINUE</string>
    <string name="jbl_support_text">Support</string>
    <string name="jbl_restore_uppercase">RESTORE</string>
    <string name="amazon_alexa_text">Amazon Alexa</string>
    <string name="amazon_cast_text" translatable="false">Amazon Cast</string>
    <string name="jbl_power_on">Power On</string>
    <string name="jbl_my_products">My Products</string>
    <string name="jbl_put_your_speaker_in_setup_mode">Put your product in setup mode.</string>
    <string name="jbl_ready_to_play_some_music_">Ready to play some music?</string>
    <string name="jbl_discover_product">DISCOVER PRODUCT</string>
    <string name="jbl_connect_product">Connect Product</string>
    <string name="jbl_enter_wi_fi_setup_mode">Enter Wi-Fi Setup Mode</string>
    <string name="jbl_make_sure_product_is_powered_on">Make sure product is powered on.</string>
    <string name="jbl_listen_on_your_speakers_or_tv__using_the_spotify_app_as_a_remote_">Listen on your speakers or TV, using the Spotify app as a remote.</string>
    <string name="jbl_discovering">Discovering</string>
    <string name="google_home_tm" translatable="false">Google Home™</string>
    <string name="enable_google_cast">Enable Google Cast</string>
    <string name="multi_channel_For_better_acoustics_performance_not_all_products_tips">For better sound performance, not all products can be grouped as an immersive system. You can still play music on these products using AirPlay / Google Cast.</string>
    <string name="Position_all_speakers_at_a_similar_height">Position all speakers at a similar height.</string>
    <string name="party_together">PartyTogether</string>
    <string name="how_to_get_product_connected_horizon_3">How to get product connected</string>
    <string name="text_edit">Edit</string>
    <string name="eq_signature" translatable="false">SIGNATURE</string>
    <string name="eq_jbl_signature" translatable="false">JBL SIGNATURE</string>
    <string name="eq_signature_sound" translatable="false">SIGNATURE SOUND</string>
    <string name="eq_vocal" translatable="false">VOCAL</string>
    <string name="eq_energetic" translatable="false">ENERGETIC</string>
    <string name="eq_party" translatable="false">PARTY</string>
    <string name="eq_chill" translatable="false">CHILL</string>
    <string name="eq_relaxing" translatable="false">RELAXING</string>
    <string name="eq_movie" translatable="false">MOVIE</string>
    <string name="eq_customize" translatable="false">CUSTOMIZE</string>
    <string name="edit_equalizer">Edit Equalizer</string>
    <string name="CANT_FIND_PRODUCT__">CANNOT FIND PRODUCT ?</string>
    <string name="H5_To_redeem_later">To redeem later, select [Product Settings] > [Roon Ready] from product card to continue.</string>
    <!--20240603 for Auracast-->
    <string name="text_add">Add</string>
    <string name="Add_speakers">Add speakers</string>
    <string name="Create_party">CREATE PARTY</string>
    <string name="start_party_tips">Play music on your speaker to start a party.</string>
    <string name="text_dismiss">Dismiss</string>
    <string name="This_speaker_is_in_daisy_chain_mode">This speaker is in daisy chain mode. Please disconnect the daisy chain before using the PartyTogether function.</string>
    <string name="You_are_listening_from_others_You_can_play_music_when_the_current_playback_stops">You are listening from others.You can play music when the current playback stops.</string>
    <string name="press_image_button_on_the_speaker_to_quit_party">Press %s button on the speaker to quit the party.</string>
    <string name="press_image_button_on_the_speaker_to_stop_listening_from_party">Press %s button on the speaker to stop listening from the party.</string>
    <string name="other_speakers">Other Speakers</string>
    <string name="Product_Support">Product Support</string>
    <string name="cannot_find_my_speaker">Can’t find my speaker</string>
    <string name="Party_Together_vs_Stereo">PartyTogether vs. Stereo</string>
    <string name="party_together_allows_you_to_link_multiple_speakers_together">PartyTogether allows you to link multiple speakers together to pump up your party.</string>
    <string name="Current_Party_Together_compatible_models_are">Current PartyTogether compatible models are:</string>
    <string name="The_speakers_must_be_updated_to_the_latest_software_to_enjoy_the_feature">The speakers must be updated to the latest software to enjoy the feature.</string>
    <string name="Make_sure_the_speakers_are_powered_on">Make sure the speakers are powered on.</string>
    <string name="Tap_on_the_speaker_icon_from_the_Nearby_speaker_">Tap on the speaker icon from the Nearby speaker list to join the party.</string>
    <string name="Start_music_playback_on_any_of_the_speakers">Start music playback on any of the speakers.</string>
    <string name="A_new_speaker_can_be_added_to_the_party_">A new speaker can be added to the party at any time after it shows up as a Nearby speaker.</string>
    <string name="Quit_from_app">Quit from app</string>
    <string name="Tap_on_the_icon_of_the_speaker_to_be_removed_">Tap on the icon of the speaker to be removed from the party and follow the on-screen instructions.</string>
    <string name="Quit_from_product">Quit from product</string>
    <string name="Press_the_Party_Together_button_on_the_speaker_">Press the PartyTogether button on the speaker to be removed from the party.</string>
    <string name="If_your_speakers_are_setup_as_a_stereo_multi_channel_audio_system_">If your speakers are setup as a stereo/ multi-channel audio system, please ungroup them first before they can join any party.</string>
    <string name="Under_Party_Together_all_the_speakers_">Under PartyTogether, all the speakers will play the same music in sync.</string>
    <string name="Stereo_group">Stereo group</string>
    <string name="Only_two_of_the_same_speakers_can_be_">Only two of the same speakers can be set up as stereo, one speaker will play the left channel and the other plays the right channel. You may set up this group through product control page.</string>
    <string name="For_non_Party_Together_products_you_may_">For non-PartyTogether products, you may check its own product control page for other multi-speaker connection methods.</string>
    <string name="Current_PartyTogether_Image_compatible_models_are">Current PartyTogether %s compatible models are:</string>
    <string name="left_channel" translatable="false">L</string>
    <string name="right_channel" translatable="false">R</string>
    <!-- 20240806 Translated Strings ================================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ======================================================   -->
    <string name="redeem_code">Redeem code:</string>
    <string name="title_bt_pair_guide">Press %s Button and go to Settings to pair with this speaker.</string>
    <string name="cannot_setup_bluetooth_now_set_up_later">Cannot setup Bluetooth now? %1$s</string>
    <string name="pair_stereo">Pair Stereo</string>
    <string name="harman_close">Close</string>
    <string name="power_on_other_speakers_desc">Make sure the other speaker of the same model is powered on and placed nearby</string>
    <string name="rename_feedback_tone_factory_reset">Rename, Feedback tone, Factory reset</string>
    <string name="format_version">Version: %1$s</string>
    <string name="select_the_products_you_want_to_use">Select the products you want to use:</string>
    <string name="make_sure_the_product_is_connected_to_bluetooth">Make sure the product is connected to Bluetooth.</string>
    <string name="check_pairing_guide">Check Pairing Guide</string>
    <string name="put_your_product_in_pairing_mode">Put your product in pairing mode</string>
    <string name="press_bluetooth_button_on_speaker_desc">Press Bluetooth button on your speaker to enter pairing mode, and you will hear a feedback tone.</string>
    <string name="go_to_bluetooth_in_settings_desc">Go to “Bluetooth” in Settings and select the speaker from the available paired device list.</string>
    <string name="disconnect_product">Disconnect Product</string>
    <string name="disconnect_product_desc">If the product is connected to another mobile device, disconnect from others to take over.</string>
    <string name="forget_this_device">Forget This Device</string>
    <string name="forget_this_device_desc">Go to your Bluetooth settings, select “Forget This Device” and then reconnect.</string>
    <string name="if_still_cannot_discover_please_reboot_your_product_to_continue">If still cannot discover, please reboot your product to continue.</string>
    <string name="ungrouping_cannot_be_done_at_the_moment">Sorry, ungrouping cannot be done at the moment, please try again later.</string>
    <string name="login_session_has_expired_prompt">Your login session has expired. Please sign in again to continue accessing your account.</string>
    <string name="one_product_is_missing_desc">%d product in the system is disconnected with other products, group control is temporarily unavailable.\n\nGroup control will be available again when this product reconnects to group.</string>
    <string name="unknown_artist">Unknown</string>
    <string name="ota_remaining_min">%s min remaining</string>
    <string name="speakers_in_the_system">Speakers In The System</string>
    <string name="feedback_tone_desc">Enable/disable speaker button sound effect.</string>
    <string name="cannot_find_available_products_to_create_the_system">Cannot find available products to create the system.</string>
    <string name="place_the_speakers_facing_the_listener_desc">Place the speakers in front, one on the left and the other on the right, facing the listener.</string>
    <string name="your_system_is_ready_let_s_name_it">Your system is ready ! Let’s name it :</string>
    <string name="is_the_speaker_that_is_playing_a_tone_desc">Is the speaker that is playing a tone on the left or on the right? Play Again</string>
    <string name="system_controls">System Controls</string>
    <string name="turn_on_gps_to_discover_nearby_products">Turn on GPS to discover nearby products.</string>
    <string name="bt_device_connect_with_others_desc">Disconnect Bluetooth from another mobile device to take over control.</string>
    <string name="jbl_transferring">Transferring</string>
    <string name="Getting_ready__please_wait">Getting ready, please wait</string>
    <string name="qobuz_Last_update">Last update</string>
    <string name="qobuz_Custom_order">Custom order</string>
    <string name="What_s_new">What’s new?</string>
    <string name="still_cannot_connect_to_your_product_learn_more">Still cannot connect to your product? <bold>Learn More</bold></string>
    <string name="learn_more_placeholder">Learn More</string>
    <string name="Canot_connect_your_product">Can’t connect your product ?</string>
    <string name="How_to_get_system_connected">How to get system connected ?</string>
    <string name="Make_sure_all_products_in_this_group_are_in_operation_mode">Make sure all products in this group are in operation mode.</string>
    <string name="Make_sure_one_of_the_product_in_the_group_is_connected_to_this_mobile_device_via_Bluetooth">Make sure one of the product in the group is connected to this mobile device via Bluetooth.</string>
    <string name="If_the_group_still_cannot_be_found__please_reset_products_to_connect">If the group still cannot be found, please reset products to connect.</string>
    <string name="System_cannot_be_connected_at_this_moment">System cannot be connected at this moment.</string>
    <string name="The_product_is_now_playing_a_tone_press_the_button_to_continue">The product is now playing a tone, press the button to continue.</string>
    <string name="Feedback_Tone">Feedback Tone</string>
    <string name="Enable_disable_speaker_button_sound_effect">Enable / disable speaker button sound effect.</string>
    <string name="Enable_this_mode_for_quiet_close_range_listening_environments">Enable this mode for quiet, close-range listening environments.</string>
    <string name="The_main_soundbar_and_subwoofer_will_be_muted_to_optimize_the_audio_output_for_a_more_quiet_audio_environment">The main soundbar and subwoofer will be muted to optimize the audio output for a more quiet audio environment.</string>
    <string name="The_main_soundbar_and_subwoofer_have_been_muted">The main soundbar and subwoofer have been muted.</string>
    <string name="Try_restarting_your_product">Try restarting your product. If you still cannot connect to product via AirPlay after power cycle, try resetting.</string>
    <string name="Continue_to_reset_AirPlay_if_you_see_the_below_system_message">Continue to reset AirPlay if you see the below system message.</string>
    <string name="Your_soundbar_will_automatically_exit_this_mode_when">Your soundbar will automatically exit this mode when:</string>
    <string name="Multi_speaker_Connection_via_Auracast">Multi-speaker Connection via Auracast™</string>
    <string name="Amplify_your_party_by_wirelessly_connecting_multiple_Auracast_compatible_JBL_speakers_Find_out_more_from_the_section">Amplify your party by wirelessly connecting multiple Auracast™ compatible JBL speakers. Find out more from the %@ section.</string>
    <string name="Equalizer_Upgrade">Equalizer Upgrade</string>
    <string name="With_the_upgraded_equalizer_design_you_can_now_instantly_apply_a_pre_configured_EQ">With the upgraded equalizer design, you can now instantly apply a pre-configured EQ to optimize the audio output for specific scenarios, or further fine-tune and personalize the sound to your preferences.</string>
    <string name="The_new_Personal_Listening_Mode_allows_you_to_enjoy_a_more_focused_and_intimate_audio_environment_that_better_suits_your_late">The new Personal Listening Mode allows you to enjoy a more focused and intimate audio environment that better suits your late-night or close-range listening needs.</string>
    <string name="Surround_Speaker_Battery_Indication">Surround Speaker Battery Indication</string>
    <string name="Get_peace_of_mind_with_a_clear_and_easily_accessible_battery_indication_on_current_battery_level_and_charging_status_of_the_rear_surround_speakers">Get peace of mind with a clear and easily accessible battery indication on current battery level and charging status of the rear surround speakers. Check it from the top of the product control section.</string>
    <string name="Spotify_HiFi">Spotify HiFi</string>
    <string name="Spotify_HiFi_is_now_supported_on_your_product">Spotify HiFi is now supported on your product! Get the most from your Spotify streaming experience with lossless, high-quality audio.</string>
    <!-- 20240911 Translated Strings ================================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ============================================================   -->
    <string name="network_factory_reset">Network, Factory reset</string>
    <string name="speakers_will_be_calibrated_for_better_performance">Speakers will be calibrated for better performance</string>
    <string name="Night_Listening">Night Listening</string>
    <string name="Mute_soundbar_and_subwoofer_to_make_a_more_silent_sound_environment">Mute soundbar and subwoofer to make a more silent sound environment.</string>
    <string name="Activate_this_feature_to_mute_soundbar_and_subwoofer">Activate this feature to mute soundbar and subwoofer.</string>
    <string name="Mute_soundbar_and_subwoofer_to_create_a_quiet_environment">Mute soundbar and subwoofer to create a quiet environment.</string>
    <string name="Night_listening_will_automatically_turn_off_when">Night listening will automatically turn off when:</string>
    <string name="after_10_minutes_of_no_audio_input">after 10 minutes of no audio input.</string>
    <string name="Place_rear_speakers_in_front_of_you">Place rear speakers in front of you.</string>
    <string name="Enjoy_movies_at_night_without_disturbing_others">Enjoy movies at night without disturbing others</string>
    <string name="the_new_light_listening_feature_desc">The new Night Listening feature allows you to enjoy a more focused and intimate audio environment that better suits your late-night or close-range listening needs.</string>
    <string name="Reset_Failed_cannot_connect_to_the_product">Reset Failed, cannot connect to the product. Please try again.</string>
    <string name="ble_rename_tips">To update the product’s Bluetooth name, go to system Bluetooth list, select \"Forgot this device\" and re-pair it.</string>
    <string name="performance_and_general_software_improvement">Performance and general software improvement</string>
    <string name="with_the_upgraded_equalizer_design_you_can_now_further">With the upgraded equalizer design, you can now further fine-tune and personalize the sound to your preferences.</string>
    <string name="title_60_day_free_trial">60-day free trial</string>
    <string name="music_lovers_and_audiophiles">Music lovers and audiophiles</string>
    <string name="ios_system_restart_required">System restart required</string>
    <string name="ios_restart_your_mobile_device">Please restart your mobile device to connect with product.</string>
    <string name="jbl_google_terms_of_service_no_quotes">Google Terms of Service</string>
    <string name="jbl_privacy_policy_no_quotes">Google Privacy Policy</string>
    <string name="google_cast_terms_of_service_and_google_privacy_policy">Your use of Google Cast is subject to the Google Terms of Service. The Google Privacy Policy describes how your data is handled by Google Cast.</string>
    <string name="do_you_want_to_help_improve_everyone_experience_by_">Do you want to help improve everyone\'s experience by sharing device stats and crash reports with Google?</string>
    <string name="ready_to_start_casting_desc">To setup voice control from a Google Assistant-enabled speaker and multiroom groups with Cast-enabled speakers, download the Google Home app.</string>
    <!-- 20241205 Translated Strings ================================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ============================================================   -->
    <string name="play_audio_and_broadcast_to_other_products_">Play audio and broadcast to other products in standard quality.</string>
    <string name="text_disable_uppercase">DISABLE</string>
    <string name="text_enable_uppercase">ENABLE</string>
    <string name="how_to_create_party_content9">Your speaker can only be discovered while active. After 10 mins of inactivity, please wake up speaker and try joining Party again.</string>
    <string name="connect_at_least_one_speaker_to_the_app_to_start_a_party">Connect at least one speaker to the app to start a party.</string>
    <string name="smart_details" translatable="false">SmartDetails</string>
    <string name="pure_voice" translatable="false">PureVoice</string>
    <!-- 20250226 Translated Strings ================================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ============================================================   -->
    <string name="smart_details_desc">Enhanced level of audio detail while maintaining a perfectly balanced sound.</string>
    <string name="pure_voice_desc">Provides clear, natural dialogue without compromising immersive audio effects.</string>
    <string name="smart_details_tips">The feature is optimized for HDMI and Optical sources, providing an enhanced experience.\n\nPlease note that it\'s not available in music modes, such as Bluetooth or other music casting options.</string>
    <string name="turn_on_broadcasting_then_select_preferred_audio_channel_output">Turn on broadcasting then select preferred audio channel output.</string>
    <string name="text_mono">Mono</string>
    <string name="enable_broadcasting">Enable broadcasting.</string>
    <string name="enable_night_listening">Enable Night Listening.</string>
    <string name="broadcasting">Broadcasting</string>
    <string name="broadcasting_desc">Enjoy TV audio from the main bar in the living room and continue listening by moving the detachable speakers to another space.</string>
    <string name="detach_detachable_speakers">Detach detachable speakers.</string>
    <string name="place_detachable_speakers_anywhere_in_the_same_room">Place detachable speakers anywhere in the same room.</string>
    <string name="detachable_speaker_output">Detachable Speaker Output</string>
    <string name="broadcasting_will_automatically_turn_off_when">Broadcasting will automatically turn off when you:</string>
    <string name="dock_detachable_speakers_back_to_soundbar">dock the detachable speakers back to the main bar.</string>
    <string name="Keep_quiet_and_do_not_block_the_speakers_during_the_process">Keep quiet and do not block your speakers during the process.</string>
    <string name="Calibration_Succeed">Calibration Succeed !</string>
    <string name="Please_re_calibrate_if_the_environment_or_the_position_of_the_product_changed">Please re-calibrate after the position of the product is changed.</string>
    <string name="Placement">Placement</string>
    <string name="Detach_rear_speakers_and_place_them_in_the_sitting_position">Detach rear speakers and place them in the sitting position.</string>
    <string name="i_have_placed_them">I\'VE PLACED THEM</string>
    <string name="Make_sure_the_rear_speakers_orientation_is_matching_with_the_illustration_guide">Ensure the rear speakers\' orientation matches the illustration guide.</string>
    <string name="previous_uppercase">PREVIOUS</string>
    <string name="Something_went_wrong._Please_try_again_later">Something went wrong. Please try again later.</string>
    <string name="back_uppercase">BACK</string>
    <string name="Undock_the_rear_speakers_to_continue_audio_calibration">Undock the rear speakers to continue audio calibration.</string>
    <string name="Detachable_Speakers">Detachable Speakers\nUsage Tips</string>
    <string name="Go_to_Night_Listening">Go to Night Listening</string>
    <string name="This_Soundbar_model_has_detachable_speakers_that_can_be_used_for_different_use_cases">This soundbar model has detachable speakers that can be used for different use cases.</string>
    <string name="Detach_for_Rear_Channel_Use">1. Detach for Rear Channel Use</string>
    <string name="Detach_speakers_from_the_main_bar_Place_them_on_the_left">Detach speakers from the main bar. Place them on the left &amp; right behind the listening area, facing the audience. Enjoy a cinematic surround sound experience for movies.</string>
    <string name="Detach_for_broadcasting">2. Detach for broadcasting</string>
    <string name="Detach_the_speakers_from_the_main_bar_Enable_flex_listening_and_set_the_speaker_output_in_audio_settings">Detach the speakers from the main bar. Enable broadcasting and set the speaker output in audio settings. Place them freely as desired to enjoy either mono or stereo sound.</string>
    <string name="Detach_for_Night_Listening">3. Detach for Night Listening</string>
    <string name="When_night_listening_is_enabled_the_main_bar_and_subwoofer_are_muted">When night listening is enabled, the main bar and subwoofer are muted, and only the detachable speakers output stereo sound. Enjoy clear audio without disturbing others.</string>
    <string name="Detach_for_Stand_Alone_Product">4. Detach for Stand Alone Product</string>
    <string name="Detach_the_speakers_from_the_main_bar_Switch_to_stand_alone_mode_by_short_pressing_Bluetooth_button_to_pair_with_your_mobile_device">Detach the speakers from the main bar. Switch to standalone mode by short pressing the Bluetooth button to pair with your mobile device. The standalone speaker supports Auracast multi-speaker connection.</string>
    <string name="Detach_for_Stereo_System">5. Detach for Stereo System</string>
    <string name="Detach_the_speakers_from_the_main_bar__Pair_them_as_a_stereo_system_by_long_pressing_Bluetooth_buttons_on_both_speakers_for_5_seconds">Detach the speakers from the main bar.  Pair them as a stereo system by long pressing the Bluetooth buttons on both speakers for 5 seconds.</string>
    <string name="Go_to_Broadcasting">Go to Broadcasting</string>
    <!-- 20250226 Translated Strings ================================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ============================================================   -->
    <string name="preset_stations">Preset Stations</string>
    <string name="to_format_mhz">%s MHz</string>
    <string name="alarm">Alarm</string>
    <string name="sleep_mode">Sleep Mode</string>
    <string name="sleep_mode_desc">Sleep mode lets you customize lighting and ambient sounds. Long press the knob to activate.</string>
    <string name="ambient_light">Ambient Light</string>
    <string name="screen_display">Screen Display</string>
    <string name="bedtime_screen_brightness">Bedtime screen brightness</string>
    <string name="screen_saver">Screen saver</string>
    <string name="text_light_on">Light on</string>
    <string name="wake_up_at">Wake up at</string>
    <string name="weekend">Weekend</string>
    <string name="everyday">Everyday</string>
    <string name="once">Once</string>
    <string name="customize">Customize</string>
    <string name="bedtime_screen_brightness_uppercase">Bedtime screen brightness</string>
    <string name="clock_theme_desc">The display will switch to selected screen saver after the product is idle for 10 minutes.</string>
    <string name="set_sleep_mode">Set Sleep Mode</string>
    <string name="ambient_sound">Ambient Sound</string>
    <string name="ambient">Ambient</string>
    <string name="title_format">Format</string>
    <string name="title_time">Time</string>
    <string name="title_date">Date</string>
    <string name="title_time_format_24">24-hour time</string>
    <string name="title_time_format_12">12-hour time</string>
    <string name="fm_radio">FM</string>
    <string name="dab_radio">DAB</string>
    <string name="stations">Stations</string>
    <string name="scanning">Scanning...</string>
    <string name="connect_product_with_bluetooth_to_continue_software_update">Connect product with Bluetooth to continue software update.</string>
    <string name="to_authorize_control_of_this_product_please_press_the_knob_to_continue">To authorize control of this product, please press the knob to continue.</string>
    <string name="unplug_power_cable_and_plug_in_again_to_restart">Unplug power cable and plug in again to reboot the product.\nMake sure it’s not connected to other mobile devices.</string>
    <string name="one_hour_format">%s hour</string>
    <string name="please_turn_off_party_together_on_your_product">Please turn off PartyTogether on your product to continue the connection.</string>
    <string name="set_the_product_to_pairing_mode">Set The Product to Pairing Mode</string>
    <string name="wake_up">Wake Up</string>
    <string name="sleep">Sleep</string>
    <string name="set_bedtime_tips">During Bedtime, press knob will light up the screen for 5 seconds.</string>
    <string name="the_change_of_ambient_sound_tip">The change of ambient sound requires Bluetooth connection to your mobile device.</string>
    <string name="preset_x_empty">Preset %1$s - Empty</string>
    <string name="preset_x_station_x">Preset %1$s - %2$s</string>
    <string name="time_language_rename_supported_service">Time, Language, Rename, Supported service</string>
    <string name="if_radio_is_selected_as_the_alarm_sound_source_tip">If radio is selected as the alarm sound source, the product will switch on the radio at the alarm time.</string>
    <string name="disconnect_with_other_app">Disconnect with other’s APP</string>
    <string name="disconnect_with_other_app_desc">If the product is connected with other\'s ONE APP, please quit that APP.</string>
    <string name="turn_off_party_together">Turn off PartyTogether</string>
    <string name="turn_off_party_together_desc">If PartyTogether is on, please turn it off on your product to continue the connection.</string>
    <string name="if_the_product_cannot_be_connected_desc">If the product cannot be connected, power off then restart the product.</string>
    <string name="text_mhz">MHz</string>
    <string name="yyyy_mm_dd">yyyy-mm-dd</string>
    <string name="dd_mm_yyyy">dd-mm-yyyy</string>
    <string name="Short_press_the_product_knob_will_stop_the_alarm">Short press the product knob will stop the alarm.</string>
    <string name="hours_format">%s Hours</string>
    <string name="Setup_Product">Setup Product</string>
    <string name="Transferring_">Transferring...</string>
    <string name="During_Bedtime_press_any_buttons_will_light_up">During Bedtime, press any buttons will light up the screen for 5 seconds</string>
    <string name="wind_chime">Wind chime</string>
    <string name="camp_fire">Camp fire</string>
    <string name="preset_x">Preset %d</string>
    <string name="light_element_satellite">Satellite</string>
    <string name="brightness">Brightness</string>
    <string name="dynamic_level">Dynamic Level</string>
    <string name="light_saturation">Saturation</string>
    <string name="turn_off_the_PartyTogether">Turn off the PartyTogether on the product to quit</string>
    <string name="your_product_has_been_added_and_time_automatically_synced">Your product has been added, and the time and date have been automatically synced with your mobile device.</string>
    <!-- 20241205 Strings To Be Translated ================================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ======================================================   -->
    <!--New strings for moment generation 2 不用翻译-->
    <string name="title_edit_sound_scape">Edit %s elements</string>
    <string name="mix_with_music_desc">Mix with music playback as ambient atmosphere background sound.</string>
    <!--New strings for moment generation 2 不用翻译-->
    <!--New strings for SS5 不用翻译-->
    <string name="level_low">Low</string>
    <string name="level_mid">Mid</string>
    <string name="level_high">High</string>
    <string name="how_to_connect_with_hdmi">How to Connect with HDMI</string>
    <string name="light_pattern">Light Pattern</string>
    <string name="title_color">Color</string>
    <string name="title_pattern_pulse">Pulse</string>
    <string name="title_pattern_neon">Neon</string>
    <string name="title_pattern_bounce">Bounce</string>
    <string name="title_pattern_loop">Loop</string>
    <string name="title_pattern_static">Static</string>
    <string name="light_element_subwoofer">Subwoofer</string>
    <!--New strings for SS5 不用翻译-->
    <!-- 20250311 Strings To Be Translated === New strings for Bar MK2 ================= ☆☆☆☆☆☆☆Start☆☆☆☆☆☆☆ ============================= -->
    <string name="place_detachable_speakers_in_front_of_you">Place detachable speakers in front of you.</string>
    <!--devicelist-->
    <string name="harmanbar_devicelist_Manually_Syncing">Manually Syncing</string>
    <string name="harmanbar_devicelist_wifi_password">Wi-Fi Password</string>
    <string name="harmanbar_devicelist_Sync_Audio">Sync Audio</string>
    <string name="harmanbar_devicelist_Choose_Language">Choose Language</string>
    <string name="harmanbar_devicelist_Keep_your_sound_in_sync">Keep your sound in sync</string>
    <string name="harmanbar_devicelist_Wi_Fi_password">Wi-Fi password</string>
    <string name="harmanbar_devicelist_Update_App">

    </string>
    <string name="harmanbar_devicelist_For_a_better_experience_and_more_features__update_your_application">For a better experience and more features, update your application</string>
    <string name="harmanbar_devicelist_We_can_automatically_sync_your_speaker_s_timing__or_you_can_adjust_it_manuallly_on_your_own_">We can automatically sync your speaker\'s timing, or you can adjust it manuallly on your own.</string>
    <string name="harmanbar_devicelist_Auto_Sync">Auto Sync</string>
    <string name="harmanbar_devicelist_Manual_Sync">Manual Sync</string>
    <string name="harmanbar_devicelist_Reset_to_Default">Reset to Default</string>
    <string name="harmanbar_devicelist_The_hotspot_will_be_hidden_when_the_speaker_is_connected_to_the_home_LAN__and_will_visible_again_if_">The hotspot will be hidden when the speaker is connected to the home LAN, and will visible again if connection is lost or the speaker is restored to its factory settings.</string>
    <string name="harmanbar_devicelist_Manually_adjust_the_latency_of_your_speaker_s_audio_">Manually adjust the latency of your speaker\'s audio.</string>
    <string name="harmanbar_devicelist_To_Connect_Using_Wi_Fi_n1__Choose____in_your_Wi_Fi_settings_on_your_device_or_computer__n2__Enter_th">To Connect Using Wi-Fi\n1. Choose %s in your Wi-Fi settings on your phone or computer.\n2. Enter the password when prompted.</string>
    <string name="harmanbar_devicelist_Calibrating_your_speaker_s_audio_timing_">Calibrating your speaker\'s audio timing…</string>
    <string name="harmanbar_devicelist_Other_users_will_join_your_shared_Wi_Fi_network_using_this_password__nThe_password_must_contain_at_l">Other users will join your shared Wi-Fi network using this password.\nThe password must contain at least 8 characters.\nChanging the password will disconnect any currently connected users.</string>
    <string name="harmanbar_devicelist_Stop_Auto_Sync_">Stop Auto Sync?</string>
    <string name="harmanbar_devicelist_Personal_Hotspot">Personal Hotspot</string>
    <string name="harmanbar_devicelist_You_ll_need_to_start_the_process_over_again_">You\'ll need to start the process over again.</string>
    <string name="harmanbar_devicelist_Download_failed">Download failed</string>
    <string name="harmanbar_devicelist_Italian">Italian</string>
    <string name="harmanbar_devicelist_Stop">Stop</string>
    <string name="harmanbar_devicelist_Please_check_your_internet_connection_and_try_again_">Please check your internet connection and try again.</string>
    <string name="harmanbar_devicelist_Spanish">Spanish</string>
    <string name="harmanbar_devicelist_All_done_">All done!</string>
    <string name="harmanbar_devicelist_Find_new_version">Find new version</string>
    <string name="harmanbar_devicelist_Spanish__MS_">Spanish (Mexico)</string>
    <string name="harmanbar_devicelist_Play_some_music_to_make_sure_everything_s_ok__If_some_adjustment_is_still_needed__try_Manually_Synci">Play some music to make sure everything’s ok. If some adjustment is still needed, try Manually Syncing the audio of your speakers&#160;</string>
    <string name="harmanbar_devicelist_connect_fail">connect fail</string>
    <string name="harmanbar_devicelist_French__CA_">French (Canada)</string>
    <string name="harmanbar_devicelist_Your_speaker_and_app_will_both_play_a_sound_to_match_the_timing__n_nThis_will_take_about_30_seconds_">Your speaker and app will both play a sound to match the timing.\n\nThis will take about 30 seconds.&#160;</string>
    <string name="harmanbar_devicelist_Audio_Settings">Audio Settings</string>
    <string name="harmanbar_devicelist_Aux_out_level">Aux out level</string>
    <string name="harmanbar_devicelist_Volume_limit">Volume limit</string>
    <string name="harmanbar_devicelist_Quite">Quite</string>
    <string name="harmanbar_devicelist_Loud">Loud</string>
    <string name="harmanbar_devicelist_SPDIF_Output_Resolution">SPDIF Output Resolution</string>
    <string name="harmanbar_devicelist_Sample_rate_">Sample rate:</string>
    <string name="harmanbar_devicelist_Bit_depth_">Bit depth:</string>
    <string name="harmanbar_devicelist_Choose_the_maximal_optical_output_resolution_">Choose the maximal optical output resolution.</string>
    <string name="harmanbar_devicelist_1__Choose_the_maximal_sample_rate_or_bit_depth__n2__Click_the__Play_Test_Audio__button_to_check_if_y">1. Choose the maximal sample rate or bit depth.\n2. Click the “Play Test Audio” button to check if your downstream device can support this change.\n3. If you can’t hear any sound after this change, it may be due to the inability of your downstream device that can’t support the selected resolution, please change to lower sample rate or bit depth.</string>
    <string name="harmanbar_devicelist_Selected_output_resolution_________">Selected output resolution: %1$s, %2$s.</string>
    <string name="harmanbar_devicelist_Audio_output">Audio output</string>
    <string name="harmanbar_devicelist_SPDIF">SPDIF</string>
    <string name="harmanbar_devicelist_Please_select_a_way_to_audio_output">Please select a way to audio output</string>
    <string name="harmanbar_devicelist_Aux_Out">Aux Out</string>
    <string name="harmanbar_devicelist_Fixed_volume_output">Fixed volume output</string>
    <string name="harmanbar_devicelist_If_turn_on_it__the_volume_of_WiiM_Mini_will_be_fixed_at_100__you_can_only_use_the_audio_system_to_control_audio_output_volume__such_as_speaker__amplifier__TV_and_etc__you_can_t_change_it_from_this_app_and_WiiM_Mini__n_nIf_you_hear_some_noise_and_distortion_from_your_audio_system_after_turn_on_it__please_choose_aux_out_level_to____">If turn on it, the volume of WiiM Mini will be fixed at 100, you can only use the audio system to control audio output volume, such as speaker, amplifier, TV and etc, you can’t change it from this app and WiiM Mini.\n\nIf you hear some noise and distortion from your audio system after turn on it, please choose aux out level to %s.</string>
    <string name="harmanbar_devicelist_The_audio_output_is_fixed_at_one_level__so_you_can_t_change_it_now_">The audio output is fixed at one level, so you can’t change it now.</string>
    <string name="harmanbar_devicelist_Play_test_audio">Play test audio</string>
    <string name="harmanbar_devicelist_Fixed">Fixed</string>
    <string name="harmanbar_devicelist_Not_Connected">Not Connected</string>
    <string name="harmanbar_devicelist_Connect">Connect</string>
    <string name="harmanbar_devicelist_Pair_Bluetooth_device_with_your_WiiM_Mini">Pair Bluetooth device with your WiiM Mini</string>
    <string name="harmanbar_devicelist_Paired_Devices">Paired Devices</string>
    <string name="harmanbar_devicelist_Available_Devices">Available Devices</string>
    <string name="harmanbar_devicelist_Auto_Measurement_of_Audio_Delay">Auto Measurement of Audio Delay</string>
    <string name="harmanbar_devicelist_The_connected_speaker_or_receiver_may_have_different_audio_latency__The_latency_value_is_critical_to">The connected speaker or receiver may have different audio latency. The latency value is critical to maintain the perfect synchronization for multiroom audio. WiiM Mini measures the latency with the on-device MIC automatically.</string>
    <string name="harmanbar_devicelist_Calibration">Calibration</string>
    <string name="harmanbar_devicelist_Skip">Skip</string>
    <string name="harmanbar_devicelist_Latency_Measurement_Tips">Latency Measurement Tips</string>
    <string name="harmanbar_devicelist_1__Connect_WiiM_Mini_to_one_audio_system_through_AUX_Out_or_SPDIF__n_n2__WiiM_Mini_will_play_a_tunin">1. Connect WiiM Mini to one audio system through AUX Out or SPDIF.\n\n2. WiiM Mini will play a tuning sound for about 30 seconds. Please turn your speaker loud for more accurate measurement.\n\n3. Please put the WiiM Mini with your speaker as close as possible for more accurate measurement.</string>
    <string name="harmanbar_devicelist_Let_s_start">Let’s start</string>
    <string name="harmanbar_devicelist_Calibrating_">Calibrating…</string>
    <string name="harmanbar_devicelist_Later">Later</string>
    <string name="harmanbar_devicelist_Measurement_is_completed_successfully_">Measurement is completed successfully!</string>
    <string name="harmanbar_devicelist_Measurement_Failed">Measurement Failed</string>
    <string name="harmanbar_devicelist_Device__Settings__Sync_Audio__Manual_Sync">Device-&gt;Settings-&gt;Sync Audio-&gt;Manual Sync</string>
    <string name="harmanbar_devicelist_Audio_path_latency____ms">Audio path latency: %sms</string>
    <string name="harmanbar_devicelist_If_you_want_to_adjust_the_latency_manually__please_go_to_____to_adjust_the_audio_latency_manually_">If you want to adjust the latency manually, please go to  %s to adjust the audio latency manually.</string>
    <string name="harmanbar_devicelist_1__Ensure_WiiM_Mini_is_connected_with_your_audio_system_through_AUX_OUT__not_AUX_IN__or_SPDIF__n_n2__Ensure_your_speaker_is_loud__n_n3__Or_you_could_press_Later_button_below__go_to____to_adjust_the_audio_latency_manually_">1. Ensure WiiM Mini is connected with your audio system through AUX OUT (not AUX IN) or SPDIF.\n\n2. Ensure your speaker is loud.\n\n3. Or you could press Later button below, go to %s to adjust the audio latency manually.</string>
    <string name="harmanbar_devicelist_Bring_you_the_best_sound_">Bring you the best sound.</string>
    <string name="harmanbar_devicelist_Bit_Perfect_Audio_Playback">Bit Perfect Audio Playback</string>
    <string name="harmanbar_devicelist_Set_it_up__">Set it up &gt;</string>
    <string name="harmanbar_devicelist_Setting_Time">Setting Time</string>
    <string name="harmanbar_devicelist_WiiM_Mini_plays_a_24_bit_192_kHz_Hi_Res_file_and_offers_the_exactly_same_digital_output_via_its_opti">WiiM Mini plays a 24-bit/192 kHz Hi-Res file and offers the exactly same digital output via its optical SPDIF without making any changes to it.\n\nBy default, the maximal optical output is set to 48 kHz/16-bit to be compatible with legacy devices. If you have more powerful downstream device, please change the optical output of WiiM Mini to higher resolution. Tap the button below to make the change.</string>
    <string name="harmanbar_devicelist_Save">Save</string>
    <string name="harmanbar_devicelist_Set_optical_output">Set optical output</string>
    <string name="harmanbar_devicelist_Zipcode">Zipcode</string>
    <string name="harmanbar_devicelist_Yes">Yes</string>
    <string name="harmanbar_devicelist_Device_SSID">Device SSID</string>
    <string name="harmanbar_devicelist_Not_yet">Not yet</string>
    <string name="harmanbar_devicelist_Change_into_English_language">Change into English language</string>
    <string name="harmanbar_devicelist_Change_SPDIF_output_resolution_successfully_">Change SPDIF output resolution successfully!</string>
    <string name="harmanbar_devicelist_Do_you_want_to_change_type_into_English_language_">Do you want to change type into English language?</string>
    <string name="harmanbar_devicelist_Selected_output_resolution________">Selected output resolution: %1$s, %2$s</string>
    <string name="harmanbar_devicelist_Can_you_hear_the_sound_after_changing_sample_rate_or_bit_depth_">Can you hear the sound after changing sample rate or bit depth?</string>
    <string name="harmanbar_devicelist_Fixed_resolution_will_make_WiiM_Mini_offers_the_fixed_digital_output_via_optical_SPDIF_output_with_t">Fixed resolution will make WiiM Mini offers the fixed digital output via optical SPDIF output with the selected sample rate and bit depth.</string>
    <string name="harmanbar_devicelist_Getting_Bluetooth_status">Getting Bluetooth status</string>
    <string name="harmanbar_devicelist_Fixed_resolution">Fixed resolution</string>
    <string name="harmanbar_devicelist_Disconnect">Disconnect</string>
    <string name="harmanbar_devicelist_Forget_This_Device">Forget This Device</string>
    <string name="harmanbar_devicelist_WiiM_Mini_is_up_to_date">WiiM Mini is up to date</string>
    <string name="harmanbar_devicelist_Searching_for_your_media_server_or_DLNA_">Search your media server</string>
    <string name="harmanbar_devicelist_Make_sure_the_network_of_your_media_server_or_DLNA_is_same_as_your_smartphone_">Make sure your media server is at the same network with your mobile device.</string>
    <string name="harmanbar_devicelist_Choose_your_listening_quality">Choose your listening quality</string>
    <string name="harmanbar_devicelist_It_will_be_used_as_default_for_your_next_plays_">It will be used as default for your next plays.</string>
    <string name="harmanbar_devicelist_Excellent">Excellent</string>
    <string name="harmanbar_devicelist_Very_Good">Very Good</string>
    <string name="harmanbar_devicelist_Okay">Okay</string>
    <string name="harmanbar_devicelist_Weak">Weak</string>
    <string name="harmanbar_devicelist_Please_your_device_close_to_router_">Please your device close to router.</string>
    <string name="harmanbar_devicelist_Connected_to">Connected to</string>
    <string name="harmanbar_devicelist_Found_no_Wi_Fi">Found no Wi-Fi</string>
    <string name="harmanbar_devicelist_Drag_to_update">Drag to update</string>
    <string name="harmanbar_devicelist_Release_to_update">Release to update</string>
    <string name="harmanbar_devicelist_Loading___">Loading...</string>
    <string name="harmanbar_devicelist_Last_update">Last update</string>
    <string name="harmanbar_devicelist_Today">Today</string>
    <string name="harmanbar_devicelist_Line_In">Line In</string>
    <string name="harmanbar_devicelist_Bluetooth">Bluetooth</string>
    <string name="harmanbar_devicelist_USB_Devices">USB Devices</string>
    <string name="harmanbar_devicelist_Optical">Optical</string>
    <string name="harmanbar_devicelist_AirPlay">AirPlay</string>
    <string name="harmanbar_devicelist_Not_now">Not now</string>
    <string name="harmanbar_devicelist_Upgrade">Upgrade</string>
    <string name="harmanbar_devicelist_DEVICE_LIST">Devices</string>
    <string name="harmanbar_devicelist_Play_All">Play All</string>
    <string name="harmanbar_devicelist_Pause_All">Pause All</string>
    <string name="harmanbar_devicelist_Multi_room_mode_timed_out__refresh_system">Multi-room mode timed out, refresh system</string>
    <string name="harmanbar_devicelist_Finished">Finished</string>
    <string name="harmanbar_devicelist_Please_wait">Please wait</string>
    <string name="harmanbar_devicelist_Multi_room_mode_successful">Multi-room mode successful</string>
    <string name="harmanbar_devicelist_How_to_switch_between_solo_and_multi_mode_">How to switch between solo and multi mode?</string>
    <string name="harmanbar_devicelist_Drag_device_below_line_to_enter_single_speaker_mode">Drag device below line to enter single speaker mode</string>
    <string name="harmanbar_devicelist_Drop_device_below_this_line_to_cancel_operation">Drop device below this line to cancel operation</string>
    <string name="harmanbar_devicelist_Drag_and_drop_to_cancel">Drag and drop to cancel</string>
    <string name="harmanbar_devicelist_Drag_and_drop_to_enter_single_mode">Drag and drop to enter single mode</string>
    <string name="harmanbar_devicelist_Single_room_mode_successful">Single room mode successful</string>
    <string name="harmanbar_devicelist_Single_room_mode_timed_out__refresh_system">Single room mode timed out, refresh system</string>
    <string name="harmanbar_devicelist_Operation_failed">Operation failed</string>
    <string name="harmanbar_devicelist_Firmware_occurs_error__please_connect_to_the_Internet_to_upgrade">Firmware occurs error, please connect to the Internet to upgrade</string>
    <string name="harmanbar_devicelist_Firmware_occurs_error__please_upgrade">Firmware occurs error, please upgrade</string>
    <string name="harmanbar_devicelist_Confirm">Confirm</string>
    <string name="harmanbar_devicelist_Battery_is_low__please_connect_to_the_charger">Battery is low, please connect to the charger</string>
    <string name="harmanbar_devicelist_Do_you_want_to_change_type_into_Chinese_language_">Do you want to change type into Chinese language?</string>
    <string name="harmanbar_devicelist_This_device_is_in_single_play_mode">This device is in single play mode</string>
    <string name="harmanbar_devicelist_Change_into_Chinese_language">Change into Chinese language</string>
    <string name="harmanbar_devicelist_Switch">Switch</string>
    <string name="harmanbar_devicelist_Device_update">Device update</string>
    <string name="harmanbar_devicelist_Please_make_sure_you_are_connected_to_the_internet">Please make sure you are connected to the internet</string>
    <string name="harmanbar_devicelist_Updating">Updating</string>
    <string name="harmanbar_devicelist_Upgrade_failed">Upgrade failed</string>
    <string name="harmanbar_devicelist_Upgrade_failed__device_will_restart">Upgrade failed, device will restart</string>
    <string name="harmanbar_devicelist_Current_version_is_up_to_date">Current version is up to date</string>
    <string name="harmanbar_devicelist_Device_aren_t_online">Device aren\'t online</string>
    <string name="harmanbar_devicelist_Please_reconfigure_the_internet">Please reconfigure the internet</string>
    <string name="harmanbar_devicelist_Update_successful">Update successful</string>
    <string name="harmanbar_devicelist_Download">Download</string>
    <string name="harmanbar_devicelist_Update">Update</string>
    <string name="harmanbar_devicelist_Device_Reboot">Device Reboot</string>
    <string name="harmanbar_devicelist_Please_do_not_disconnect_device_from_power_source">Please do not disconnect device from power source</string>
    <string name="harmanbar_devicelist_Rename">Rename</string>
    <string name="rename_text">Rename</string>
    <string name="harmanbar_devicelist_OK">OK</string>
    <string name="harmanbar_devicelist_Speaker_Info">Speaker Info</string>
    <string name="harmanbar_devicelist_Preset_Content">Preset Content</string>
    <string name="harmanbar_devicelist_Alarm_Clock">Alarm Clock</string>
    <string name="harmanbar_devicelist_AMAZON_ALEXA_SETTINGS">Amazon Alexa Settings</string>
    <string name="harmanbar_devicelist_Sleep_Timer">Sleep Timer</string>
    <string name="harmanbar_devicelist_BACK">BACK</string>
    <string name="harmanbar_devicelist_Set_fail">Set fail</string>
    <string name="harmanbar_devicelist_Setting____">Setting....</string>
    <string name="harmanbar_devicelist_Successfully_Set">Successfully Set</string>
    <string name="harmanbar_devicelist_Wi_Fi_Status">Wi-Fi Status</string>
    <string name="harmanbar_devicelist_Hotspot_Status">Hotspot Status</string>
    <string name="harmanbar_devicelist_Other_Information">Other Information</string>
    <string name="harmanbar_devicelist_Wi_Fi_Strength">Wi-Fi Strength</string>
    <string name="harmanbar_devicelist_Need_Hide_SSID">Hide SSID</string>
    <string name="harmanbar_devicelist_Add_Password_Protection">Add Password Protection</string>
    <string name="harmanbar_devicelist_Speaker_Name">Speaker Name</string>
    <string name="harmanbar_devicelist_Firmware_Version">Firmware Version</string>
    <string name="harmanbar_devicelist_Build_Date">Build Date</string>
    <string name="harmanbar_devicelist_Audio_Prompts_Language">Audio Prompts Language</string>
    <string name="harmanbar_devicelist_Restore_Factory_Settings">Restore Factory Settings</string>
    <string name="harmanbar_devicelist_Device_Password">Device Password</string>
    <string name="harmanbar_devicelist_OFF">OFF</string>
    <string name="harmanbar_devicelist_ON">ON</string>
    <string name="harmanbar_devicelist_Change_the_device_language">Change the device language</string>
    <string name="harmanbar_devicelist_Cancel">Cancel</string>
    <string name="harmanbar_devicelist_The_password_has_closed">The password has closed</string>
    <string name="harmanbar_devicelist_Password_length_needs_to_be_at_least_8">Password length needs to be at least 8</string>
    <string name="harmanbar_devicelist_The_password_has_reset">The password has reset</string>
    <string name="harmanbar_devicelist_Rebooting____">Rebooting....</string>
    <string name="harmanbar_devicelist_Reboot_success">Reboot success</string>
    <string name="harmanbar_devicelist_The_speaker_s_SSID_is_hidden">The speaker\'s SSID is hidden</string>
    <string name="harmanbar_devicelist_The_speaker_s_SSID_is_not_hidden">The speaker\'s SSID is not hidden</string>
    <string name="harmanbar_devicelist_Password_">Password:</string>
    <string name="harmanbar_devicelist_Done">Done</string>
    <string name="harmanbar_devicelist_Min">Min</string>
    <string name="harmanbar_devicelist_Release_to_synchronous_play_with">Release to synchronous play with %s</string>
    <string name="harmanbar_devicelist_Loud__2_Vrms_">2 Vrms</string>
    <string name="harmanbar_devicelist_Normal__1_Vrms_">1 Vrms</string>
    <string name="harmanbar_devicelist_Generally__1_Vrms_is_used_for_headphone_out_and_2_Vrms_is_used_for_speaker_out__n_nIf_you_hear_some_">Generally, 1 Vrms is used for headphone out and 2 Vrms is used for speaker out.\n\n If you hear some noise and distortion from your audio system, please select lower level to solve it.</string>
    <string name="harmanbar_devicelist____Wi_Fi_strength_is_weak__only______">%1$s Wi-Fi strength is weak, only %2$s%%.</string>
    <string name="harmanbar_devicelist_Please_keep_it_close_to_router">Please keep it close to router</string>
    <string name="harmanbar_devicelist_1__Make_sure_the_power_adapter_is_plugged_in_the">1. Make sure the power adapter is plugged in the WiiM Mini \n\n2. Check and see if the LED is white and blinking on the device</string>
    <string name="harmanbar_devicelist_PRODUCT_NAME">JBL Bar</string>
    <string name="harmanbar_devicelist_PRODUCT_DEVICE_NAME">JBL Bar</string>

    <!--adddevice-->
    <string name="harmanbar_adddevice_Next">Next</string>
    <string name="harmanbar_adddevice_UH_OH_">UH OH!</string>
    <string name="harmanbar_adddevice_Tap_Next_to_try_an_alternate_way_of_connecting_">Tap Next to try an alternate way of connecting.</string>
    <string name="harmanbar_adddevice_Try_Again">Try Again</string>
    <string name="harmanbar_adddevice_Connect_Speaker">Connect Speaker</string>
    <string name="harmanbar_adddevice_Wi_Fi_Setup_Timeout">Wi-Fi Setup Timeout</string>
    <string name="harmanbar_adddevice_Device_is_connected_to____successfully">Your device is connected to %s successfully</string>
    <string name="harmanbar_adddevice_Current_Wi_Fi_strength_of_device___">Wi-Fi signal strength: %s</string>
    <string name="harmanbar_adddevice_Connected">Connected</string>
    <string name="harmanbar_adddevice_Wi_Fi_strength_is_too_low_to_guarantee_a_smooth_streaming_experience_Suggest_">Wi-Fi strength is too low to guarantee a smooth streaming experience，Suggest:</string>
    <string name="harmanbar_adddevice_2_Change_to_another_Wi_Fi_AP_to_connect">2.Change to another Wi-Fi AP to connect</string>
    <string name="harmanbar_adddevice_Loading____">Loading....</string>
    <string name="harmanbar_adddevice_Success">Success</string>
    <string name="harmanbar_adddevice_Add_Device">Add Device</string>
    <string name="harmanbar_adddevice_Press_the_WPS_key_on_device_to_put_the_device_into_network_setup_mode__nThen_click_the__Ne">Press the WPS key on device to put the device into network setup mode.\nThen click the “Next” button to continue.</string>
    <string name="harmanbar_adddevice_Press_to_Enter_Setup_Mode">Press to Enter Setup Mode</string>
    <string name="harmanbar_adddevice_Please_select_the_category_of_speakers_you_want_to_add">Please select the category of speakers you want to add</string>
    <string name="harmanbar_adddevice_Are_you_sure_">Are you sure?</string>
    <string name="harmanbar_adddevice_Add_other_MUZO_compatible_speakers">Add other MUZO compatible speakers</string>
    <string name="harmanbar_adddevice_Yes__change">Yes, change</string>
    <string name="harmanbar_adddevice_Set_Password">Set Password</string>
    <string name="harmanbar_adddevice_Confirm_passwords_field_should_be_equal_to_new_password_field">Confirm passwords field should be equal to new password field</string>
    <string name="harmanbar_adddevice_Wi_Fi_name_cannot_be_empty">Wi-Fi name cannot be empty</string>
    <string name="harmanbar_adddevice_Set_Password_for_your_device_">Set Password for your device:</string>
    <string name="harmanbar_adddevice_Please_enter_your_Wi_Fi_Name">Please enter your Wi-Fi Name</string>
    <string name="harmanbar_adddevice_For_safety__please_set_the_password_for_your_device_">For safety, please set the password for your device.</string>
    <string name="harmanbar_adddevice_Choose_to_hide_your_Wi_Fi_hotspot_">Choose to hide your Wi-Fi hotspot?</string>
    <string name="harmanbar_adddevice_Set_password_">Set password:</string>
    <string name="harmanbar_adddevice_Please_confirm_">Please confirm:</string>
    <string name="harmanbar_adddevice_Please_enter_the_password">Please enter the password</string>
    <string name="harmanbar_adddevice_Please_re_enter_the_password">Please re-enter the password</string>
    <string name="harmanbar_adddevice_Submit">Submit</string>
    <string name="harmanbar_adddevice_The_password_has_closed">The password has closed</string>
    <string name="harmanbar_adddevice_Password_length_needs_to_be_at_least_8">Password length needs to be at least 8</string>
    <string name="harmanbar_adddevice_The_password_has_reset">The password has reset</string>
    <string name="harmanbar_adddevice_The_speaker_s_SSID_is_hidden">The speaker\'s SSID is hidden</string>
    <string name="harmanbar_adddevice_The_speaker_s_SSID_is_not_hidden">The speaker\'s SSID is not hidden</string>
    <string name="harmanbar_adddevice_Cancel">Cancel</string>
    <string name="harmanbar_adddevice_Setting____">Setting....</string>
    <string name="harmanbar_adddevice_Successfully_Set">Successfully Set</string>
    <string name="harmanbar_adddevice_Set_fail">Set fail</string>
    <string name="harmanbar_adddevice_Finish">Finish</string>
    <string name="harmanbar_adddevice_Hint">Hint</string>
    <string name="harmanbar_adddevice_Please_enter_name">Please enter name</string>
    <string name="harmanbar_adddevice_Confirm">Confirm</string>
    <string name="harmanbar_adddevice_Name_Device">Name Device</string>
    <string name="harmanbar_adddevice_The_name_of_device_is_empty_">The name of device is empty.</string>
    <string name="harmanbar_adddevice_The_length_of_name_is_too_long">The length of name is too long</string>
    <string name="harmanbar_adddevice_Only_numbers__letters_and_underscore_are_allowed">Only numbers, letters and underscore are allowed</string>
    <string name="harmanbar_adddevice_Name_exists">Name exists</string>
    <string name="harmanbar_adddevice_Custom___">Custom...</string>
    <string name="harmanbar_adddevice_Turn_on_GPS">Turn on GPS</string>
    <string name="harmanbar_adddevice_Search">Search</string>
    <string name="harmanbar_adddevice_Please_turn_on_GPS_in_your_phone_settings_first__then_return_to_this_app_">Please turn on GPS in your phone settings first,then return to this app.select the non-power saving mode for the positioning method.</string>
    <string name="harmanbar_adddevice_Options">Options</string>
    <string name="harmanbar_adddevice_Please_wait">Please wait</string>
    <string name="harmanbar_adddevice_Wi_Fi_Info">Wi-Fi Info</string>
    <string name="harmanbar_adddevice_Wi_Fi_">Wi-Fi:</string>
    <string name="harmanbar_adddevice_Password_">Password:</string>
    <string name="harmanbar_adddevice_Searching_for_M_ZO_Wireless_Hi_Fi_System">Searching for your device</string>
    <string name="harmanbar_adddevice_Need_help">Need help</string>
    <string name="harmanbar_adddevice_Your_device_____doesn_t_enable_Wi_Fi_connection">Your phone %s doesn\'t enable Wi-Fi connection</string>
    <string name="harmanbar_adddevice_Please_enable_Wi_Fi_connection_then_search_again">Please enable Wi-Fi connection then search again</string>
    <string name="harmanbar_adddevice_3__Return_to_this_App">Return to this App</string>
    <string name="harmanbar_adddevice_Fail">Fail</string>
    <string name="harmanbar_adddevice_Please_choose_from_the_list_of_available_networks">Please choose from the list of available networks</string>
    <string name="harmanbar_adddevice_Content_is_empty">Content is empty</string>
    <string name="harmanbar_adddevice_The_speaker_is_connecting_to_network_________">The speaker is connecting to network\n\" %s \"</string>
    <string name="harmanbar_adddevice_Recently_Played">Recently Played</string>
    <string name="harmanbar_adddevice_Change_the_Wi_Fi_of_your_mobile_device_to__________that_is_the_same_network_as_the_speaker_">Change the Wi-Fi of your mobile device to \" %s \" that is the same network as the speaker.</string>
    <string name="harmanbar_adddevice_Favorites">Favorites</string>
    <string name="harmanbar_adddevice_The_device_is_not_connected_to_the_Wi_Fi_network_yet__Click____Connect____to_connect_it_to_network_">The device is not connected to the network. Click \" Connect \" to setup wireless network</string>
    <string name="harmanbar_adddevice_Press_the_WPS_button_on_your_device_as_shown_below_to_enter_into_setup_mode_">Press the WPS button on your device as shown below to enter into setup mode.</string>
    <string name="harmanbar_adddevice_Continue">Continue</string>
    <string name="harmanbar_adddevice_SET_PHONE_Wi_Fi">CHANGE WI-FI ON PHONE</string>
    <string name="harmanbar_adddevice_setup">setup</string>
    <string name="harmanbar_adddevice_Please_make_sure_your_device_is_powered_up_and_connected_to_the_same_network">Make sure your device is powered on and connected to the same network as your smartphone.\nIf you haven\'t set it up yet, tap the button below to start.</string>
    <string name="harmanbar_adddevice_Help">Help</string>
    <string name="harmanbar_adddevice_The_speaker_has_connected_successfully_">The speaker has connected successfully.</string>
    <string name="harmanbar_adddevice_Failed_to_connect_to_Wi_Fi_network____please_try_again_">Failed to connect to Wi-Fi network %s,please try again.</string>
    <string name="harmanbar_adddevice_Your_device_is_not_connected_to_Wi_Fi">Your phone is not connected to Wi-Fi</string>
    <string name="harmanbar_adddevice_Fail_to_setup_your_MUZO_speaker__Please_retry_if_you_have_given_an_incorrect_password_">Fail to setup your MUZO speaker. Please retry if you have given an incorrect password.</string>
    <string name="harmanbar_adddevice_Lost_Wi_Fi_connection">Lost Wi-Fi connection</string>
    <string name="harmanbar_adddevice_Then__come_back_to_this_App_">Then, come back to this App.</string>
    <string name="harmanbar_adddevice_Could_not_find_Linkplay_XXXX_">Could not find Linkplay_XXXX?</string>
    <string name="harmanbar_adddevice_Settings">Settings</string>
    <string name="harmanbar_adddevice_Don_t_connect_the_device">Don\'t connect the device</string>
    <string name="harmanbar_adddevice_App_will_be_able_to_discover_and_connect_to_devices_on_the_networks_you_use_">You cannot use this app if you select \"Don\'t Allow.\"</string>
    <string name="harmanbar_adddevice_Please_connect_your_new_">Please connect your new&#160;</string>
    <string name="harmanbar_adddevice_Allow_to_access_your_local_network_while_you_are_using_the_app_">Allow this app to access your Local Network.&#160;</string>
    <string name="harmanbar_adddevice_Keep_your_router__phone__and_device_close_to_each_other_">Keep your router, phone, and device close to each other.</string>
    <string name="harmanbar_adddevice_OK">OK</string>
    <string name="harmanbar_adddevice_Connecting___">Connecting...</string>
    <string name="harmanbar_adddevice_Wi_Fi_Settings">Wi-Fi Settings</string>
    <string name="harmanbar_adddevice_App_use_local_network_to_ensure_the_normal_use_of_this_app_and_associated_function__On_the_next_scre">This app needs to use your Local Network in order to locate your device and connect with it.</string>
    <string name="harmanbar_adddevice_Retry">Retry</string>
    <string name="harmanbar_adddevice__connect_success">&#160;connect success</string>
    <string name="harmanbar_adddevice_Select_your_Wi_Fi_Network_">Select your Wi-Fi Network.</string>
    <string name="harmanbar_adddevice_Password">Password</string>
    <string name="harmanbar_adddevice_Please_enter_router_password">Please enter router password</string>
    <string name="harmanbar_adddevice_SELECT_NETWORK">SELECT NETWORK</string>
    <string name="harmanbar_adddevice_Please_reconnect">Please reconnect</string>
    <string name="harmanbar_adddevice_to_continue_configure_process">to continue configure process</string>
    <string name="harmanbar_adddevice_Error">Error</string>
    <string name="harmanbar_adddevice_Password_length_needs_to_be_at_least_5_characters">Password length needs to be at least 5 characters</string>
    <string name="harmanbar_adddevice_Search_finished">Search finished</string>
    <string name="harmanbar_adddevice__connect_fail">&#160;connect fail</string>
    <string name="harmanbar_adddevice_Found____devices">Found %s devices</string>
    <string name="harmanbar_adddevice_Found____device">Found %s device</string>
    <string name="harmanbar_adddevice_1_Please_move_the_device_closer_to_the_router">1.Please move the device closer to the router</string>
    <string name="harmanbar_adddevice_Choose_the_network_you_want_the_device_to_use_____only_supports_2_4G_networks_">Choose the network you want the device to use. %s only supports 2.4G networks.</string>
    <string name="harmanbar_adddevice_Go_to_your_iPhone_s_Wi_Fi_settings_and_select_the_Wi_Fi_starting_with____XXXX_to_connect_">Go to your Phone\'s Wi-Fi settings and select the Wi-Fi starting with %s_XXXX to connect.</string>
    <string name="harmanbar_adddevice_Please_select_your_device">Please select your device</string>
    <string name="harmanbar_adddevice_Any_Problems_">Any Problems?</string>
    <string name="harmanbar_adddevice_Now_connect_this_mobile_device_to_a_temporary_wireless_network_created_by_the_speaker">Now connect this mobile device to a temporary wireless network created by the speaker</string>
    <string name="harmanbar_adddevice_Give_Us_Feedback">Feedback</string>
    <string name="harmanbar_adddevice_1__Open_your_phone_settings_and_tap_on_Wi_Fi">Open your phone settings and tap on Wi-Fi</string>
    <string name="harmanbar_adddevice_2__Tap_on_______XXX____in_the_list_of_networks">Tap on \" %s_XXXX \" in the list of networks</string>
    <string name="harmanbar_adddevice_____requires_a_2_4GHz_network_">(%s requires a 2.4GHz network)</string>
    <string name="harmanbar_adddevice_Add">Add</string>
    <string name="harmanbar_adddevice_Please_enter_a_password_for_this_network">Please enter a password for this network</string>
    <string name="harmanbar_adddevice_Choose_your_network">Choose your network</string>
    <string name="harmanbar_adddevice_Use_Another_Network">Use Another Network</string>
    <string name="harmanbar_adddevice_No_device_found_">No device found.</string>
    <string name="harmanbar_adddevice_Speaker_network">Speaker network</string>
    <string name="harmanbar_adddevice_Getting_network_list_requires_GPS_permission_for_devices_with_Android_6_0_and_above__Please_check_if">Getting network list requires GPS permission for devices with Android 6.0 and above. Please check if GPS is enabled.</string>
    <string name="harmanbar_adddevice_Please_wait__it_might_take_a_minute_">Please wait, it might take a minute.</string>
    <string name="harmanbar_adddevice_Checking_Wi_Fi_status">Checking Wi-Fi status</string>
    <string name="harmanbar_adddevice_Connected_to_Network">Connected to Network</string>
    <string name="harmanbar_adddevice_We_have_found_the_following____devices__n_Select_your_device_and_continue_">We have found the following %s devices.\n Select your device and continue.</string>
    <string name="harmanbar_adddevice_Please_check_if_the_Wi_Fi_password_you_entered_is_correct">Please check if the Wi-Fi password you entered is correct</string>
    <string name="harmanbar_adddevice_Keep_your_router__phone__and_device_close_to_each_other">Keep your router, phone, and device close to each other</string>
    <string name="harmanbar_adddevice_Press_and_hold_the_WPS_button_on_your_device_as_show_below_to_enter_into_setup_mode_">Press and hold the WPS button on your device as show below to enter into setup mode.</string>
    <string name="harmanbar_adddevice_This_is_used_to_search_for_your_device_and_connect_with_it_">This is used to search for your device and connect with it.</string>
    <string name="harmanbar_adddevice____connect_lost__Please_go_to_Wi_Fi_setting_to_reconnect_">%s connect lost, Please go to Wi-Fi setting to reconnect.</string>
    <string name="harmanbar_adddevice_1__If_you_could_not_find_Linkplay_XXXX__please_press_the_WPS_button_on_your_device_as_show_below_to_">1. If you could not find Linkplay_XXXX， please press the WPS button on your device as show below to enter into setup mode.  And wait for 45 seconds until you hear a failed voice prompt , then click Continue.&#160;</string>
    <string name="harmanbar_adddevice_2__If_you_forget_the_Linkplay_XXXX_password_please_restore_factory_settings_and_try_again_">2. If you forget the Linkplay_XXXX password，please restore factory settings and try again.</string>
    <string name="harmanbar_adddevice_Allow_to_access_your_location_while_you_are_using_the_app_">Allow to access your location while you are using the app?</string>
    <string name="harmanbar_adddevice_APP_needs_to_find_your_Wi_Fi_network_as_location_information__This_app_doesn_t_collect_your_location">APP needs to find your Wi-Fi network as location information. This app doesn\'t collect your location using GPS or Wi-Fi based positioning technologies</string>
    <string name="harmanbar_adddevice_Don_t_Allow">Don\'t Allow</string>
    <string name="harmanbar_adddevice_Allow">Allow</string>
    <string name="harmanbar_adddevice_App_collecting_your_location_information_is_to_ensure_the_normal_use_of_this_app_and_associated_func">App collecting your location information is to ensure the normal use of this app and associated function. your information will be kept confidential. if you are still impeach of it, feel free to contact us please. On the next screen, you can press \"allow\" to access to your device.</string>
    <string name="harmanbar_adddevice_I_Know">I Know</string>
    <string name="harmanbar_adddevice_The_device_is_not_connected_to_the_Wi_Fi_network_yet__Click__Add_Device__to_connect_it_to_network_">The device is not connected to the Wi-Fi network yet. Click \"Add Device\" to connect it to network.</string>
    <string name="harmanbar_adddevice_1__Your_device_is_connected_to_wireless_network_XXXX_and_your_speaker_is_connected_to_YYYY__Please_g">1. Your Phone is connected to wireless network XXXX and your speaker is connected to YYYY. Please go to your Phone’s %s and connect to YYYY.</string>
    <string name="harmanbar_adddevice_2__Failed_to_setup_your_speaker__Please_retry_if_you_entered_the_password_incorrectly__Check_help_fo">2. Failed to setup your speaker. Please retry if you entered the password incorrectly. Check help for alternate setup method for Wi-Fi setup.</string>
    <string name="harmanbar_adddevice_Before_setting_up_the_speaker__please_be_sure_the_router_is_running_on_a_2_4_GHz_Wi_Fi_band_network_">Before setting up the speaker, please be sure the router is running on a 2.4 GHz Wi-Fi band network. Then setup the speaker to Wi-Fi network.\n\nThe following steps could help you setup the Wi-Fi for the speaker without using the companion App.\n\n1. Connect the phone or laptop to the speaker\'s Wi-Fi hotspot directly. Go to the phone\'s wireless connection and select the speaker\'s Wi-Fi SSID, which should look like “Linkplay_XXXX”\n\n2. Launch an internet browser in your device and enter http://10.10.10.254. Click the Wi-Fi button in the top-left corner of the page.\n\n3. Select the Wi-Fi network that you want to connect to.\n\n4. Enter the password to connect.\n\n5. You will hear the voice prompt of \"Wi-Fi connected\" after the speaker is connected to your router.\n\n</string>
    <string name="harmanbar_adddevice_Connect_phone_back_to_router">Connect phone back to router</string>
    <string name="harmanbar_adddevice_The_speaker_is_connecting_to_network_YYYY__Please_go_to_your_Phone_s_Wi_Fi_Settings_and_connect_to_Y">The speaker is connecting to network YYYY. Please go to your Phone’s %s and connect to YYYY.</string>
    <string name="harmanbar_adddevice_CONNECTING">CONNECTING</string>
    <string name="harmanbar_adddevice_Would_you_like_to_change_your_device_language_to_____">Would you like to change your device language to %s ?</string>
    <string name="harmanbar_adddevice_Japanese">Japanese</string>
    <string name="harmanbar_adddevice_English__Ireland_">English (Ireland)</string>
    <string name="harmanbar_adddevice_English_Australia_">English (Australia)</string>
    <string name="harmanbar_adddevice_Continue_to_MUZO_App">Continue to MUZO App</string>
    <string name="harmanbar_adddevice_Cancel_setup">Cancel setup</string>
    <string name="harmanbar_adddevice_Setup_failed">Setup failed</string>
    <string name="harmanbar_adddevice_connection_failed">connection failed</string>
    <string name="harmanbar_adddevice_Alternate_Way_to_Setup">Alternate Way to Setup</string>

    <!--alarm-->
    <string name="harmanbar_alarm_AM">AM</string>
    <string name="harmanbar_alarm_PM">PM</string>
    <string name="harmanbar_alarm_Cancel">Cancel</string>
    <string name="harmanbar_alarm_Done">Done</string>
    <string name="harmanbar_alarm_Rate">Repeat</string>
    <string name="harmanbar_alarm_Music">Music</string>
    <string name="harmanbar_alarm_Volume">Volume</string>
    <string name="harmanbar_alarm_Loading____">Loading....</string>
    <string name="harmanbar_alarm_Fail">Fail</string>
    <string name="harmanbar_alarm_Success">Success</string>
    <string name="harmanbar_alarm_Once">Once</string>
    <string name="harmanbar_alarm_USB_Disk">USB Disk</string>
    <string name="harmanbar_alarm_Everyday">Everyday</string>
    <string name="harmanbar_alarm_Monday">Monday</string>
    <string name="harmanbar_alarm_Tuesday">Tuesday</string>
    <string name="harmanbar_alarm_Wednesday">Wednesday</string>
    <string name="harmanbar_alarm_Thursday">Thursday</string>
    <string name="harmanbar_alarm_Friday">Friday</string>
    <string name="harmanbar_alarm_Saturday">Saturday</string>
    <string name="harmanbar_alarm_Sunday">Sunday</string>
    <string name="harmanbar_alarm_Please_select_the_alarm_clock_song">Please select the alarm clock song</string>
    <string name="harmanbar_alarm_Please_select_the_clock_frequency">Please select the clock frequency</string>
    <string name="harmanbar_alarm_Alarm_clock_s_volume_shouldn_t_be_0">Alarm clock\'s volume shouldn\'t be 0</string>
    <string name="harmanbar_alarm_Confirm">Confirm</string>
    <string name="harmanbar_alarm_Setting____">Setting....</string>
    <string name="harmanbar_alarm_Successfully_Set">Successfully Set</string>
    <string name="harmanbar_alarm_Set_fail">Set fail</string>
    <string name="harmanbar_alarm_Content_is_empty">Content is empty</string>
    <string name="harmanbar_alarm_Recently_Played">Recently Played</string>
    <string name="harmanbar_alarm_Favorites">Favorites</string>
    <string name="harmanbar_alarm_You_haven_t_set_up_any_alarms_yet">You haven\'t set up any alarms yet</string>
    <string name="harmanbar_alarm_Alarm_Clock">Alarm Clock</string>
    <string name="harmanbar_alarm_New_Clock">New Clock</string>
    <string name="harmanbar_alarm_Edit_clock">Edit clock</string>
    <string name="harmanbar_alarm_Deleting____">Deleting....</string>
    <string name="harmanbar_alarm_Delete_success">Delete success</string>
    <string name="harmanbar_alarm_Delete_fail">Delete failed</string>
    <string name="harmanbar_alarm_Please_tap_the___button_to_add_one">Please tap the %s button to add one</string>
    <string name="harmanbar_alarm_Preset_Content">Preset Content</string>
    <string name="harmanbar_alarm_Home_Music_Share">Home Music Share</string>
    <string name="harmanbar_alarm_Content_is_empty__please_choose_a_valid_music">Content is empty, please choose a valid music</string>
    <string name="harmanbar_alarm_Duration">Duration</string>
    <string name="harmanbar_alarm_Repeat">Repeat</string>
    <string name="harmanbar_alarm_No_limit">No limit</string>
    <string name="harmanbar_alarm_Add_Alarm">Add Alarm</string>
    <string name="harmanbar_alarm_Edit_Alarm">Edit Alarm</string>
    <string name="harmanbar_alarm_Automatically_play_music_at_a_specified_time">Automatically play music at a specific time.</string>
    <string name="harmanbar_alarm_Delete">Delete</string>

    <!--playview-->
    <string name="harmanbar_spotify_Want_more_skips__Upgrade_to_Spotify_Premium_">Spotify Premium lets you play any track, ad-free and with better audio quality.</string>
    <string name="harmanbar_playview_Are_you_sure_you_want_to_delete_this_preset_">Are you sure you want to delete this preset?</string>
    <string name="harmanbar_playview_Unsupported_music_format">Unsupported music format</string>
    <string name="harmanbar_playview_This_song_cannot_be_added_to_Favorites_">This song cannot be added to Favorites.</string>
    <string name="harmanbar_playview_Add_fail">Add fail</string>
    <string name="harmanbar_playview_Play_successful">Play successful</string>
    <string name="harmanbar_playview_Play_failed">Play failed</string>
    <string name="harmanbar_playview_tunein">tunein</string>
    <string name="harmanbar_playview_Cancel">Cancel</string>
    <string name="harmanbar_playview_Options">Options</string>
    <string name="harmanbar_playview_No_playlist_info_available">No playlist info available</string>
    <string name="harmanbar_playview_Current">Current</string>
    <string name="harmanbar_playview_Please_wait">Please wait</string>
    <string name="harmanbar_playview_Done">Done</string>
    <string name="harmanbar_playview_Glad_you_like_it__nWe_ll_let_our_DJs_know_">Glad you like it!\nWe\'ll let our DJs know.</string>
    <string name="harmanbar_playview_Great_we_ll_play_you_more_songs_like_this_">Great,we\'ll play you more songs like this.</string>
    <string name="harmanbar_playview_Great_we_ll_play_you_more_episodes_like_this_">Great,we\'ll play you more episodes like this.</string>
    <string name="harmanbar_playview_Station_added_to_your_Favorite_Stations">Station added to your Favorite Stations</string>
    <string name="harmanbar_playview_No">No</string>
    <string name="harmanbar_playview_Yes">Yes</string>
    <string name="harmanbar_playview_Station_deleted_from_your_Favorite_Stations">Station deleted from your Favorite Stations</string>
    <string name="harmanbar_playview_Delete_fail">Delete fail</string>
    <string name="harmanbar_playview_An_error_occured_while_trying_to_play">An error occured while trying to play</string>
    <string name="harmanbar_playview_Load_success">Load success</string>
    <string name="harmanbar_playview_Loading____">Loading....</string>
    <string name="harmanbar_playview__connect_fail">&#160;connect fail</string>
    <string name="harmanbar_playview_Load_fail">Load fail</string>
    <string name="harmanbar_playview_Subscribe">Subscribe</string>
    <string name="harmanbar_playview_collect_success">collect success</string>
    <string name="harmanbar_playview_Confirm_to_cancel_collection_of_stations">Confirm to cancel collection of stations</string>
    <string name="harmanbar_playview_Confirm">Confirm</string>
    <string name="harmanbar_playview_The_selected_radio_station_is_added_to_My_Stations_in_TuneIn_">The selected radio station is added to My Stations in TuneIn.</string>
    <string name="harmanbar_playview_Remove_from_the_list_">Remove from the list?</string>
    <string name="harmanbar_playview_Radio_Skip_Limit_Reached">Radio Skip Limit Reached</string>
    <string name="harmanbar_playview_You_have_reached_the_maximum_number_of_skips_allowed_for_this_radio_station_">You have reached the maximum number of skips allowed for this radio station.</string>

    <!--content-->
    <string name="harmanbar_content_iCloud_music_doesn_t_support_this_operation__please_download_the_track_to_local_first">This music only available in the iCloud Music Library or part of the Apple Music subscription service and not allows this operation</string>
    <string name="harmanbar_content_This_music_doesn_t_support_this_operation">This music doesn\'t support this operation</string>
    <string name="harmanbar_content_WiFi">Wi-Fi</string>
    <string name="harmanbar_content_Phone">Phone</string>
    <string name="harmanbar_content_Chinese">Chinese</string>
    <string name="harmanbar_content_English">English</string>
    <string name="harmanbar_content_German">German</string>
    <string name="harmanbar_content_Spanish">Spanish</string>
    <string name="harmanbar_content_French">French</string>
    <string name="harmanbar_content_Portuguese">Portuguese</string>
    <string name="harmanbar_content_Italian">Italian</string>
    <string name="harmanbar_content_Svenska">Svenska</string>
    <string name="harmanbar_content_Favorites">Favorites</string>
    <string name="harmanbar_content_My_Music">My Music</string>
    <string name="harmanbar_content_My_Download">My Download</string>
    <string name="harmanbar_content_Add_More_Services">Add More Services</string>
    <string name="harmanbar_content_XIMALAYA">XIMALAYA</string>
    <string name="harmanbar_content_QINGTINGFM">QINGTINGFM</string>
    <string name="harmanbar_content_QQFM">QQFM</string>
    <string name="harmanbar_content_Unknown_song">Unknown song</string>
    <string name="harmanbar_content_Unknown_artist">Unknown artist</string>
    <string name="harmanbar_content_Unknown_album">Unknown album</string>
    <string name="harmanbar_content_Bluetooth">Bluetooth</string>
    <string name="harmanbar_content_USB_Devices">USB Devices</string>
    <string name="harmanbar_content_Line_In">Line In</string>
    <string name="harmanbar_content_Optical">Optical</string>
    <string name="harmanbar_content_Settings">Settings</string>
    <string name="harmanbar_content_Current_version_is_out_of_date__Please_upgrade_your_firmware_">Current version is out of date; Please upgrade your firmware.</string>
    <string name="harmanbar_content_Change_play_mode____">Change play mode....</string>
    <string name="harmanbar_content_Change_success">Change success</string>
    <string name="harmanbar_content_Change_fail">Change fail</string>
    <string name="harmanbar_content_The_device_isn_t_connected_to_the_internet">The device isn\'t connected to the internet</string>
    <string name="harmanbar_content_No_Playlist">No Playlist</string>
    <string name="harmanbar_content_New_List">New List</string>
    <string name="harmanbar_content_Confirm">Confirm</string>
    <string name="harmanbar_content_My_Playlists">My Playlists</string>
    <string name="harmanbar_content__Song">&#160;Song</string>
    <string name="harmanbar_content__Songs">&#160;Songs</string>
    <string name="harmanbar_content_This_song_already_exists">This song already exists</string>
    <string name="harmanbar_content_Added_successfully">Added successfully</string>
    <string name="harmanbar_content_The_name_of_new_list_is_empty_">The name of new list is empty.</string>
    <string name="harmanbar_content_List_name_already_exists">List name already exists</string>
    <string name="harmanbar_content_Please_press___on_top_to_create_your_personal_playlist">Please press %s on top to create your personal playlist</string>
    <string name="harmanbar_content_Remove">Remove</string>
    <string name="harmanbar_content_This_song_belongs_to_the_cloud_and_can_t_be_played">This music only available in the iCloud Music Library or part of the Apple Music subscription service and not allows to play</string>
    <string name="harmanbar_content_This_song_is_protected_by_Digital_Rights_Management_and_can_t_be_played">This song is protected by Digital Rights Management and can\'t be played</string>
    <string name="harmanbar_content_Find_no_Spotify__would_like_to_download_">Find no Spotify, would like to download?</string>
    <string name="harmanbar_content_Find_no_QQMusic__would_like_to_download_">Find no QQMusic, would like to download?</string>
    <string name="harmanbar_content_Napster">Napster</string>
    <string name="harmanbar_content_Deezer">Deezer</string>
    <string name="harmanbar_content_Search">Search</string>
    <string name="harmanbar_content_QQMusic">QQMusic</string>
    <string name="harmanbar_content_vTuner">vTuner</string>
    <string name="harmanbar_content_Qobuz">Qobuz</string>
    <string name="harmanbar_content_Exit_application__please_press_Back_Key_again">Exit application, please press Back Key again</string>
    <string name="harmanbar_content_Hint">Hint</string>
    <string name="harmanbar_content_Add_to_Favorites">Add to Favorites</string>
    <string name="harmanbar_content_Add_to_Playlist">Add to Playlist</string>
    <string name="harmanbar_content_Next_To_Play">Next To Play</string>
    <string name="harmanbar_content_Artist">Artist</string>
    <string name="harmanbar_content_Album">Album</string>
    <string name="harmanbar_content_Unable_to_complete_this_operation">Unable to complete this operation</string>
    <string name="harmanbar_content_The_music_is_playing">The music is playing</string>
    <string name="harmanbar_content_Station">Station</string>
    <string name="harmanbar_content_Added_failed">Added failed</string>
    <string name="harmanbar_content_Collection">Collection</string>
    <string name="harmanbar_content_Please_wait">Please wait</string>
    <string name="harmanbar_content_Podcast">Podcast</string>
    <string name="harmanbar_content_Searching">Searching</string>
    <string name="harmanbar_content_Show">Show</string>
    <string name="harmanbar_content_Can_t_find_this_singer_s_info">Can\'t find this singer\'s info</string>
    <string name="harmanbar_content_Episode">Episode</string>
    <string name="harmanbar_content_Can_t_find_this_album_s_info">Can\'t find this album\'s info</string>
    <string name="harmanbar_content_Searching_for">Searching for</string>
    <string name="harmanbar_content_Publish">Publish</string>
    <string name="harmanbar_content_Song">Song</string>
    <string name="harmanbar_content_Playlist">Playlist</string>
    <string name="harmanbar_content_Manage">Manage</string>
    <string name="harmanbar_content_Preset">Preset</string>
    <string name="harmanbar_content_Loading____">Loading....</string>
    <string name="harmanbar_content_Fail">Fail</string>
    <string name="harmanbar_content_Speaker_is_busy__please_try_again_">Speaker is busy, please try again.</string>
    <string name="harmanbar_content_Success">Success</string>
    <string name="harmanbar_content_Connection_Lost">Connection Lost</string>
    <string name="harmanbar_content_Volume">Volume</string>
    <string name="harmanbar_content_Napster_account_is_in_use_in_another_location_">Napster account is in use in another location.</string>
    <string name="harmanbar_content_Remove_from_Favorites">Remove from Favorites</string>
    <string name="harmanbar_content_Cancel">Cancel</string>
    <string name="harmanbar_content_TuneIn">TuneIn</string>

    <!--mymusic-->
    <string name="harmanbar_mymusic_Please_tap_the___button_to_add_a_song_to_the_playlist_when_you_browse_the_music">Please tap the %s button to add a song to the playlist when you browse the music</string>
    <string name="harmanbar_mymusic_My_Music">My Music</string>
    <string name="harmanbar_mymusic_Home_Music_Share">Home Music Server</string>
    <string name="harmanbar_mymusic__Number">&#160;Number</string>
    <string name="harmanbar_mymusic_My_Playlists">My Playlists</string>
    <string name="harmanbar_mymusic__Numbers">&#160;Numbers</string>
    <string name="harmanbar_mymusic_100_Songs">100 Songs</string>
    <string name="harmanbar_mymusic_You_didn_t_create_playlist_yet">You didn\'t create playlist yet</string>
    <string name="harmanbar_mymusic_Edit">Edit</string>
    <string name="harmanbar_mymusic_Done">Done</string>
    <string name="harmanbar_mymusic_Change_list_name">Change list name</string>
    <string name="harmanbar_mymusic_The_length_of_name_is_too_long">The length of name is too long</string>
    <string name="harmanbar_mymusic_Please_press___Edit___then___on_top_to_create_your_personal_playlist">Please press \"Edit\" then %s on top to create your personal playlist</string>
    <string name="harmanbar_mymusic_Please_wait">Please wait</string>
    <string name="harmanbar_mymusic_Never_show_this_hint_again_">Never show this hint again.</string>
    <string name="harmanbar_mymusic_USB_Disk">USB Disk</string>
    <string name="harmanbar_mymusic_TF_Card">TF Card</string>
    <string name="harmanbar_mymusic_Add_to_playlist">Add to playlist</string>
    <string name="harmanbar_mymusic_New_List">New List</string>
    <string name="harmanbar_mymusic_Cancel">Cancel</string>
    <string name="harmanbar_mymusic_Confirm">Confirm</string>
    <string name="harmanbar_mymusic_Added_successfully">Added successfully</string>
    <string name="harmanbar_mymusic_Songs_already_exists">Songs already exists</string>
    <string name="harmanbar_mymusic_Favorites">Favorites</string>
    <string name="harmanbar_mymusic__Song">&#160;Song</string>
    <string name="harmanbar_mymusic__Songs">&#160;Songs</string>
    <string name="harmanbar_mymusic_The_name_of_new_list_is_empty_">The name of new list is empty.</string>
    <string name="harmanbar_mymusic_List_name_already_exists">List name already exists</string>
    <string name="harmanbar_mymusic_Absolutely_empty">Absolutely empty</string>
    <string name="harmanbar_mymusic_batch_operation">batch operation</string>
    <string name="harmanbar_mymusic_Remove">Remove</string>
    <string name="harmanbar_mymusic_Finish">Finish</string>
    <string name="harmanbar_mymusic_All">All</string>
    <string name="harmanbar_mymusic_Not_all">Not all</string>
    <string name="harmanbar_mymusic_Select_">Select&#160;</string>
    <string name="harmanbar_mymusic_No_song">No song</string>
    <string name="harmanbar_mymusic_Recently_Played">Recently Played</string>
    <string name="harmanbar_mymusic_Manage">Manage</string>
    <string name="harmanbar_mymusic_Preset">Preset</string>
    <string name="harmanbar_mymusic_Loading____">Loading....</string>
    <string name="harmanbar_mymusic_You_haven_t_added_any_songs_yet">You haven\'t added any songs yet</string>
    <string name="harmanbar_mymusic_NO_Songs">NO Songs</string>

    <!--favorite-->
    <string name="harmanbar_favorite_Favorites_is_empty">Favorites is empty</string>
    <string name="harmanbar_favorite_Favorites">Favorites</string>
    <string name="harmanbar_favorite_Favorites_is_empty_">Favorites is empty.</string>
    <string name="harmanbar_favorite_1_When_browse_the_songs_you_could_tap___to_add_the_song_to_My_Favorite">1.When browse the songs,you could tap %s to add the song to My Favorite</string>
    <string name="harmanbar_favorite_2_In_Now_Play_screen_you_could_tap___to_add_the_song_to_My_Favorite">2.In Now Play screen,you could tap %s to add the song to My Favorite</string>

    <!--preset-->
    <string name="harmanbar_preset_Access_your_favorite_music_from_the_relevant_buttons_on_the_remote_control_or_the_speaker">Access your favorite music from the relevant buttons on the remote control or the speaker</string>
    <string name="harmanbar_preset_Preset_OK">Preset OK</string>
    <string name="harmanbar_preset_Recently_Played">Recently Played</string>
    <string name="harmanbar_preset_Favorites">Favorites</string>
    <string name="harmanbar_preset_Are_you_sure_want_to_replace_">Are you sure want to replace&#160;</string>
    <string name="harmanbar_preset_Cancel">Cancel</string>
    <string name="harmanbar_preset_Confirm">Confirm</string>
    <string name="harmanbar_preset_Deleting____">Deleting....</string>
    <string name="harmanbar_preset_Delete_success">Delete success</string>
    <string name="harmanbar_preset_Delete_fail">Delete failed</string>
    <string name="harmanbar_preset_This_playlist_already_exists">This playlist already exists</string>
    <string name="harmanbar_preset_Operation_failed">Operation failed</string>
    <string name="harmanbar_preset_Preset">Preset</string>
    <string name="harmanbar_preset_Content_is_empty">Content is empty</string>
    <string name="harmanbar_preset_Loading____">Loading....</string>
    <string name="harmanbar_preset_Fail">Fail</string>
    <string name="harmanbar_preset_Success">Success</string>
    <string name="harmanbar_preset_Delete">Delete</string>
    <string name="harmanbar_preset_Preset____playlist_for_easy_access">Preset %s playlist for easy access</string>

    <!--search-->
    <string name="harmanbar_search_iCloud_music_doesn_t_support_this_operation__please_download_the_track_to_local_first">This music only available in the iCloud Music Library or part of the Apple Music subscription service and not allows this operation</string>
    <string name="harmanbar_search_This_music_doesn_t_support_this_operation">This music doesn\'t support this operation</string>
    <string name="harmanbar_search_This_song_belongs_to_the_cloud_and_can_t_be_played">This music only available in the iCloud Music Library or part of the Apple Music subscription service and not allows to play</string>
    <string name="harmanbar_search_This_song_is_protected_by_Digital_Rights_Management_and_can_t_be_played">This song is protected by Digital Rights Management and can\'t be played</string>
    <string name="harmanbar_search_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_search_Manage">Manage</string>
    <string name="harmanbar_search_Preset">Preset</string>
    <string name="harmanbar_search_Folder">Folder</string>
    <string name="harmanbar_search__Song">Song</string>
    <string name="harmanbar_search_Song">Song</string>
    <string name="harmanbar_search_Album">Album</string>
    <string name="harmanbar_search_Playlist">Playlist</string>
    <string name="harmanbar_search_No_search_result">No search result</string>
    <string name="harmanbar_search_More">More</string>
    <string name="harmanbar_search_Find_">Find&#160;</string>
    <string name="harmanbar_search_your_favorite_music_n">your favorite music</string>
    <string name="harmanbar_search_Search_for_">Search for&#160;</string>
    <string name="harmanbar_search_songs__artists_and_albums">songs, artists and albums</string>
    <string name="harmanbar_search_Search">Search</string>
    <string name="harmanbar_search__Songs">&#160;Songs</string>
    <string name="harmanbar_search_Remove_all_history">Remove all history</string>
    <string name="harmanbar_search_Searching">Searching</string>
    <string name="harmanbar_search_Search_failed">Search failed</string>
    <string name="harmanbar_search_NO_Result">NO Result</string>
    <string name="harmanbar_search_History_is_empty">History is empty</string>
    <string name="harmanbar_search_Artist">Artist</string>
    <string name="harmanbar_search_Find_your_favorite_music">Find your favorite music</string>
    <string name="harmanbar_search_Please_enter_a_key">Please enter a key</string>
    <string name="harmanbar_search_See_All">See All</string>
    <string name="harmanbar_search_No_search_results">No search results</string>
    <string name="harmanbar_search_Search_for_song__playlist__artist__album__station_or_podcast_">Search for song, playlist, artist, album, station or podcast.</string>
    <string name="harmanbar_search_Recent_searches">Recent searches</string>
    <string name="harmanbar_search_Clear_recent_searches">Clear recent searches</string>
    <string name="harmanbar_search_Couldn_t_find">Couldn\'t find</string>
    <string name="harmanbar_search_Shows">Shows</string>
    <string name="harmanbar_search_Episodes">Episodes</string>
    <string name="harmanbar_search_Artists">Artists</string>
    <string name="harmanbar_search_Songs">Songs</string>
    <string name="harmanbar_search_Albums">Albums</string>
    <string name="harmanbar_search_Playlists">Playlists</string>
    <string name="harmanbar_search_Stations">Stations</string>

    <!--setting-->
    <string name="harmanbar_setting_EQ">EQ</string>
    <string name="harmanbar_setting_Tremble">Tremble</string>
    <string name="harmanbar_setting_Bass">Bass</string>
    <string name="harmanbar_setting_Sleep_time">Sleep time</string>
    <string name="harmanbar_setting_Set_your_bedtime_and_the_light_will_transition_from_bright_to_dim_until_it_is_off">Set your bedtime and the light will transition from bright to dim until it is off</string>
    <string name="harmanbar_setting_Wake_time">Wake time</string>
    <string name="harmanbar_setting_Set_a_time_to_get_up_and_the_light_will_transition_from_dim_to_bright">Set a time to get up and the light will transition from dim to bright</string>
    <string name="harmanbar_setting_Light_mode">Light mode</string>
    <string name="harmanbar_setting_Please_enter_the_subject_of_the_problem">Please enter the subject of the problem</string>
    <string name="harmanbar_setting_General_Lighting">General Lighting</string>
    <string name="harmanbar_setting_Please_describe_the_problem_you_have_">Please describe the problem you have&#160;</string>
    <string name="harmanbar_setting_Situational_Mode">Situational Mode</string>
    <string name="harmanbar_setting_Do_you_want_to_send_debug_log_">Do you want to send debug log?</string>
    <string name="harmanbar_setting_Intelligent_Lighting">Intelligent Lighting</string>
    <string name="harmanbar_setting_Your_email">Your email</string>
    <string name="harmanbar_setting_Brightness">Brightness</string>
    <string name="harmanbar_setting_Please_describe_your_problem_here">Please describe your problem here</string>
    <string name="harmanbar_setting_Allow_MUZO_to_access_your_location_while_you_are_using_the_app_">Allow MUZO to access your location while you are using the app?</string>
    <string name="harmanbar_setting_When_you_are_1_km_away_from_home__the_lights_will_turn_on_automatically_">When you are 1 km away from home, the lights will turn on automatically.</string>
    <string name="harmanbar_setting_Add_More_Services">Add More Services</string>
    <string name="harmanbar_setting_Select_to_display_on_main_">Select to display on main interface:</string>
    <string name="harmanbar_setting_Submit_a_request">Submit a request</string>
    <string name="harmanbar_setting_Send_us_feedback">Feedback</string>
    <string name="harmanbar_setting_Feedback">Feedback</string>
    <string name="harmanbar_setting_Hint">Hint</string>
    <string name="harmanbar_setting_Confirm">Confirm</string>
    <string name="harmanbar_setting_Please_wait">Please wait</string>
    <string name="harmanbar_setting_Submit_unsucceeded">Submit unsucceeded</string>
    <string name="harmanbar_setting_Your_confirmation_ID_is___">Your confirmation ID is %s</string>
    <string name="harmanbar_setting_Thanks_for_your_feedback_">Thanks for your feedback!</string>
    <string name="harmanbar_setting_Ver">App Version</string>
    <string name="harmanbar_setting_About">About</string>
    <string name="harmanbar_setting_Finish">Finish</string>
    <string name="harmanbar_setting_Audio_quality">Audio quality</string>
    <string name="harmanbar_setting_Super_quality">Super quality</string>
    <string name="harmanbar_setting_320kbps_SQ_">320kbps(SQ)</string>
    <string name="harmanbar_setting_High_quality">High quality</string>
    <string name="harmanbar_setting_128kbps_HQ_Recommend">128kbps(HQ)Recommend</string>
    <string name="harmanbar_setting_Low_quality">Low quality</string>
    <string name="harmanbar_setting_64kbps">64kbps</string>
    <string name="harmanbar_setting_FAQ">FAQ</string>
    <string name="harmanbar_setting_Settings">Settings</string>
    <string name="harmanbar_setting_Please_make_sure_you_are_connected_to_the_internet">Please make sure you are connected to the internet</string>
    <string name="harmanbar_setting_Current_OS_doesn_t_support_email_function">Current OS doesn\'t support email function</string>
    <string name="harmanbar_setting_Can_t_find_an_email_account">Can\'t find an email account</string>
    <string name="harmanbar_setting_Submit">Submit</string>
    <string name="harmanbar_setting_Please_enter">Please enter</string>
    <string name="harmanbar_setting_Please_check_your_email_address_format">Please check your email address format</string>
    <string name="harmanbar_setting_Confirm_to_submit_this_feedback_">Confirm to submit this feedback?</string>
    <string name="harmanbar_setting_Cancel">Cancel</string>
    <string name="harmanbar_setting_We_always_listen_to_you__our_users__nIf_you_have_any_feedback_to_help_us_improve_our_app__pl">We always listen to you, our users.\nIf you have any feedback to help us improve our app, please let us know.\nWe constantly try to improve your music experience.</string>
    <string name="harmanbar_setting_Done">Done</string>
    <string name="harmanbar_setting_SEND_US_FEEDBACK">SEND US FEEDBACK</string>
    <string name="harmanbar_setting_Invalid_Email_Address">Invalid Email Address</string>
    <string name="harmanbar_setting_Retry">Retry</string>
    <string name="harmanbar_setting_Edit_Music_Services">Edit Music Services</string>
    <string name="harmanbar_setting_Subject">Subject</string>
    <string name="harmanbar_setting_Your_name">Your name</string>
    <string name="harmanbar_setting_Your_email_address">Your email address</string>
    <string name="harmanbar_setting_Send">Send</string>
    <string name="harmanbar_setting_Your_name_">Your name*</string>
    <string name="harmanbar_setting_Retailer_">Retailer*</string>
    <string name="harmanbar_setting_Music_Service_">Music Service*</string>
    <string name="harmanbar_setting_Description_">Description*</string>
    <string name="harmanbar_setting_Please_enter_a_valid_email_address_">Please enter a valid email address.</string>
    <string name="harmanbar_setting_Issue_Type_">Issue Type</string>
    <string name="harmanbar_setting_Please_enter_the_details_of_your_request__A_member_of_our_support_staff_will_respond_as_soon_as_poss">Please enter the details of your request. A member of our support staff will respond as soon as possible*</string>
    <string name="harmanbar_setting_Related_articles">Related articles</string>

    <!--BLE-->
    <string name="harmanbar_BLE_Searching_for_Bluetooth_device">Searching for Bluetooth device</string>
    <string name="harmanbar_BLE_Device_service_problems">Device service problems</string>
    <string name="harmanbar_BLE_Please_turn_on_Bluetooth">Please turn on Bluetooth</string>
    <string name="harmanbar_BLE_Please_turn_on_bluetooth">Please turn on Bluetooth</string>
    <string name="harmanbar_BLE_The_network_connection_timed_out__Please_reconnect_to_the_Wifi_network_">The network connection timed out. Please reconnect to the Wifi network.</string>
    <string name="harmanbar_BLE_Please_choose_the_speaker_which_you_want_to_setup_">Please choose the speaker which you want to setup.</string>
    <string name="harmanbar_BLE_DEVICE_LIST">DEVICE LIST</string>
    <string name="harmanbar_BLE_Please_wait">Please wait</string>
    <string name="harmanbar_BLE_Finished">Finished</string>
    <string name="harmanbar_BLE_Bluetooth_is_not_turned_on__please_refer_to_the_following_tips_to_turn_on_">Bluetooth is not turned on, please refer to the following tips to turn on.</string>
    <string name="harmanbar_BLE_Turn_on_bluetooth">Turn on bluetooth</string>
    <string name="harmanbar_BLE_Have_not_scanned__the_specified_SSID">Have not scanned the specified SSID</string>
    <string name="harmanbar_BLE_WIFI_connection_timeout">Wi-Fi connection timeout</string>
    <string name="harmanbar_BLE_DHCP_timeout">DHCP timeout</string>
    <string name="harmanbar_BLE_The_password_you_entered_is_incorrect">The password you entered is incorrect</string>
    <string name="harmanbar_BLE_Unsupported_router_encryption_protocol">Unsupported router encryption protocol</string>
    <string name="harmanbar_BLE_Parameter_error">Parameter error</string>
    <string name="harmanbar_BLE_Other_errors">Other errors</string>
    <string name="harmanbar_BLE_Finding_Speakers_for_set_up_">Finding Speakers for set up…</string>
    <string name="harmanbar_BLE_Set_up_speaker">Set up speaker</string>

    <!--alexa-->
    <string name="harmanbar_alexa_Request_Sounds">Request Sounds</string>
    <string name="harmanbar_alexa_Play_a_sound_when_you_say_something_to_Alexa">Play a sound when you say something to Alexa</string>
    <string name="harmanbar_alexa_Please_choose_your_preferred_language_for_Alexa_">Please choose your preferred language for Alexa.</string>
    <string name="harmanbar_alexa_You_can_change_the_language_later_in___Amazon_Alexa_Settings___">You can change the language later in \"Amazon Alexa Settings\".</string>
    <string name="harmanbar_alexa_English_Australia_New_Zealand_">English(Australia/New Zealand)</string>
    <string name="harmanbar_alexa_Deutsch_Deutschland__sterreich_">Deutsch(Deutschland/Österreich)</string>
    <string name="harmanbar_alexa_English_United_Kingdom_Ireland_">English(United Kingdom/Ireland)</string>
    <string name="harmanbar_alexa_English_UK_IE_">English(UK/IE)</string>
    <string name="harmanbar_alexa_English_AU_NZ_">English(AU/NZ)</string>
    <string name="harmanbar_alexa_English_DE_AT_">English(DE/AT)</string>
    <string name="harmanbar_alexa_French">French</string>
    <string name="harmanbar_alexa_English_US_">English(US)</string>
    <string name="harmanbar_alexa_English_UK_">English(UK)</string>
    <string name="harmanbar_alexa_Deutsch">Deutsch</string>
    <string name="harmanbar_alexa_English__United_Kingdom_">English (United Kingdom)</string>
    <string name="harmanbar_alexa_English__United_States_">English (United States)</string>
    <string name="harmanbar_alexa_Continue_to____App">Continue to %s App</string>
    <string name="harmanbar_alexa_Amazon_Login">Amazon Login</string>
    <string name="harmanbar_alexa_Amazon_login_timeout">Amazon login timeout</string>
    <string name="harmanbar_alexa_Amazon_login_successful">Amazon login successful</string>
    <string name="harmanbar_alexa_Alexa_allows_you_to_use_your_voice_to_play_music_and_get_news__sports_scores__weather_and_more___all">Alexa allows you to use your voice to play music and get news, sports scores, weather and more - all hands-free.\nAll you have to do is ask Alexa.</string>
    <string name="harmanbar_alexa_Time_out__please_retry">Time out, please retry</string>
    <string name="harmanbar_alexa_Next">Next</string>
    <string name="harmanbar_alexa_Choose_Alexa_Language">Amazon Alexa Language</string>
    <string name="harmanbar_alexa_Cancel">Cancel</string>
    <string name="harmanbar_alexa_Successfully_Set">Successfully Set</string>
    <string name="harmanbar_alexa_Set_fail">Set fail</string>
    <string name="harmanbar_alexa_We_value_your_privacy__so_you_can_disable_your_microphone_as_shown_in_the_image_above_">We value your privacy, so you can disable your microphone as shown in the image above.</string>
    <string name="harmanbar_alexa_Alexa_is_ready">Alexa is ready</string>
    <string name="harmanbar_alexa_Please_press_the_Alexa_button_to_start_talking_to_Alexa_">Please press the Alexa button to start talking to Alexa.</string>
    <string name="harmanbar_alexa_To_learn_more_and_access_additional">To learn more and access additional</string>
    <string name="harmanbar_alexa__features__open_the">&#160;features, open the</string>
    <string name="harmanbar_alexa__features__download_the">&#160;features, download the</string>
    <string name="harmanbar_alexa__Amazon_Alexa_App">&#160;Amazon Alexa App</string>
    <string name="harmanbar_alexa_Sign_Out">Sign Out</string>
    <string name="harmanbar_alexa_Fail">Fail</string>
    <string name="harmanbar_alexa_Logging_out___">Logging out...</string>
    <string name="harmanbar_alexa_Log_out_failed">Log out failed</string>
    <string name="harmanbar_alexa_Press_and_hold_the_Alexa_icon_to_say_a_command__Release_the_button_once_you_have_finished_spea">Press and hold the Microphone icon to say a command. Release the button once you have finished speaking and wait for Alexa to respond.</string>
    <string name="harmanbar_alexa_Sign_in_with_Amazon">Sign in with Amazon</string>
    <string name="harmanbar_alexa_Back">Back</string>
    <string name="harmanbar_alexa_Your_device_includes_access_to_Alexa__Connect_your_Amazon_account_to_access_personalized_featu">Your device includes access to Alexa. Connect your Amazon account to access personalized features.</string>
    <string name="harmanbar_alexa_Unable_to_record">Unable to record</string>
    <string name="harmanbar_alexa_OK">OK</string>
    <string name="harmanbar_alexa_Listening_____seconds_remaining">Listening: %s seconds remaining</string>
    <string name="harmanbar_alexa_Send_success">Send success</string>
    <string name="harmanbar_alexa_Would_you_like_to_Sign_Out_">Would you like to Sign Out?</string>
    <string name="harmanbar_alexa_Alexa__what_s_the_weather_">Alexa, what\'s the weather?</string>
    <string name="harmanbar_alexa_Alexa__play_my_Flash_Briefing_">Alexa, play my Flash Briefing.</string>
    <string name="harmanbar_alexa_Alexa__what_are_some_top_rated_Indian_restaurants_">Alexa, what are some top-rated Indian restaurants?</string>
    <string name="harmanbar_alexa_Alexa__set_a_timer_for_20_mins_">Alexa, set a timer for 20 mins.</string>
    <string name="harmanbar_alexa_What_s_the_weather_">What\'s the weather?</string>
    <string name="harmanbar_alexa_Play_my_Flash_Briefing_">Play my Flash Briefing.</string>
    <string name="harmanbar_alexa_What_are_some_top_rated_Indian_restaurants_">What are some top-rated Indian restaurants?</string>
    <string name="harmanbar_alexa_Set_a_timer_for_20_mins_">Set a timer for 20 mins.</string>
    <string name="harmanbar_alexa_You_have_logged_into_Amazon_Alexa__To_speak_to_Alexa_just_say_her_name_followed_by_your_reques">You have logged into Amazon Alexa. Here are some of the many things you can ask Alexa.</string>
    <string name="harmanbar_alexa_You_have_logged_into_Amazon_Alexa__To_speak_to_Alexa__press_the_Alexa_button_">You have logged into Amazon Alexa. To speak to Alexa, press the Alexa button.</string>
    <string name="harmanbar_alexa_You_are_logged_into_Amazon_Alexa__If_you_don_t_need_to_switch_accounts__please_skip_this_step_">You are logged into Amazon Alexa. If you don\'t need to switch accounts, please skip this step.</string>
    <string name="harmanbar_alexa_Alexa_allows_you_to_use_your_voice_to_play_music_and_get_news__sports_scores__weather_and_more">Alexa allows you to use your voice to play music and get news, sports scores, weather and more - all hands-free.</string>
    <string name="harmanbar_alexa__All_you_have_to_do_is_ask_Alexa_">&#160;All you have to do is ask Alexa.</string>
    <string name="harmanbar_alexa_Switch_account">Switch account</string>
    <string name="harmanbar_alexa_Skip">Skip</string>
    <string name="harmanbar_alexa_Retry">Retry</string>
    <string name="harmanbar_alexa_Logging_into_Amazon">Logging into Amazon</string>
    <string name="harmanbar_alexa_Here_are_some_of_the_many_things_you_can_ask_Alexa_">Here are some of the many things you can ask Alexa.</string>
    <string name="harmanbar_alexa_English__India_">English (India)</string>
    <string name="harmanbar_alexa_Canada">Canada</string>

    <!--deezer-->
    <string name="harmanbar_deezer_Content_is_empty">Content is empty</string>
    <string name="harmanbar_deezer_Empty">Empty</string>
    <string name="harmanbar_deezer_This_track_is_not_available_">This track is not available.</string>
    <string name="harmanbar_deezer_Add_to_playlist">Add to playlist</string>
    <string name="harmanbar_deezer_Name_can_t_be_blank">Name can\'t be blank</string>
    <string name="harmanbar_deezer_See_all">See all</string>
    <string name="harmanbar_deezer_See_all_artists">See all artists</string>
    <string name="harmanbar_deezer_See_all_tracks">See all tracks</string>
    <string name="harmanbar_deezer_See_all_albums">See all albums</string>
    <string name="harmanbar_deezer_See_all_playlists">See all playlists</string>
    <string name="harmanbar_deezer_No_playlists_are_currently_available_">No playlists are currently available.</string>
    <string name="harmanbar_deezer_No_albums_are_currently_available_">No albums are currently available.</string>
    <string name="harmanbar_deezer_No_similar_artists_are_available_">No similar artists are available.</string>
    <string name="harmanbar_deezer_No_tracks_are_currently_available_">No tracks are currently available.</string>
    <string name="harmanbar_deezer_Deezer_Login">Deezer Login</string>
    <string name="harmanbar_deezer_Log_in____">Log in....</string>
    <string name="harmanbar_deezer_Deezer_account_has_logout">Deezer account has logged out</string>
    <string name="harmanbar_deezer_No_Favorite_Tracks">No Favorite Tracks</string>
    <string name="harmanbar_deezer_No_Favorite_Playlists">No Favorite Playlists</string>
    <string name="harmanbar_deezer_No_Favorite_Albums">No Favorite Albums</string>
    <string name="harmanbar_deezer_No_Favorite_Artists">No Favorite Artists</string>
    <string name="harmanbar_deezer_Search_the_entire_Deezer_catalogue_for_albums__tracks__artists_and_playlists_">Search the entire Deezer catalogue for albums, tracks, artists and playlists.</string>
    <string name="harmanbar_deezer_Deezer_Search">Deezer Search</string>
    <string name="harmanbar_deezer_Artist_Mix">Artist Mix</string>
    <string name="harmanbar_deezer_FLOW">FLOW</string>
    <string name="harmanbar_deezer_A_mix_based_on_your_favorites">A mix based on your favorites</string>
    <string name="harmanbar_deezer_Cancel">Cancel</string>
    <string name="harmanbar_deezer_Create">Create</string>
    <string name="harmanbar_deezer_Loading____">Loading....</string>
    <string name="harmanbar_deezer_Tracks">Tracks</string>
    <string name="harmanbar_deezer_Logging_out___">Logging out...</string>
    <string name="harmanbar_deezer_Log_out_failed">Log out failed</string>
    <string name="harmanbar_deezer_Logout">Logout</string>
    <string name="harmanbar_deezer_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_deezer_Log_Out">Log Out</string>
    <string name="harmanbar_deezer_No_results">No results</string>
    <string name="harmanbar_deezer_Next_To_Play">Next To Play</string>
    <string name="harmanbar_deezer_Artist">Artist</string>
    <string name="harmanbar_deezer_Album">Album</string>
    <string name="harmanbar_deezer_Unable_to_complete_this_operation">Unable to complete this operation</string>
    <string name="harmanbar_deezer_The_music_is_playing">The music is playing</string>
    <string name="harmanbar_deezer_Added_failed">Added failed</string>
    <string name="harmanbar_deezer_Please_wait">Please wait</string>
    <string name="harmanbar_deezer_The_current_user_is___">The current user is %s</string>
    <string name="harmanbar_deezer_Track">Track</string>
    <string name="harmanbar_deezer_I_got_it">I got it</string>
    <string name="harmanbar_deezer_Albums">Albums</string>
    <string name="harmanbar_deezer_Playlists">Playlists</string>
    <string name="harmanbar_deezer_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_deezer_Artists">Artists</string>
    <string name="harmanbar_deezer_Remove_all_history">Remove all history</string>
    <string name="harmanbar_deezer_Searching">Searching</string>
    <string name="harmanbar_deezer_Search_failed">Search failed</string>
    <string name="harmanbar_deezer_No_matching_results_for_______">No matching results for \"%s\"</string>
    <string name="harmanbar_deezer_History_is_empty">History is empty</string>
    <string name="harmanbar_deezer_Hint">Hint</string>
    <string name="harmanbar_deezer_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_deezer_Confirm">Confirm</string>
    <string name="harmanbar_deezer_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_deezer_Login_failed">Login failed</string>
    <string name="harmanbar_deezer_Login_successful">Login successful</string>
    <string name="harmanbar_deezer_Don_t_have_an_account_">Don\'t have an account?</string>
    <string name="harmanbar_deezer_Sign_up_now">Sign up now</string>
    <string name="harmanbar_deezer_Login">Login</string>
    <string name="harmanbar_deezer_Email_Username">Email/Username</string>
    <string name="harmanbar_deezer_Password">Password</string>
    <string name="harmanbar_deezer_Login_with_Tidal_Account">Login with Tidal Account</string>
    <string name="harmanbar_deezer_Are_you_sure_you_want_to_delete_this_playlist_">Are you sure you want to delete this playlist?</string>
    <string name="harmanbar_deezer_Delete_failed">Delete failed</string>
    <string name="harmanbar_deezer_fans">fans</string>
    <string name="harmanbar_deezer_Search">Search</string>
    <string name="harmanbar_deezer_OK">OK</string>

    <!--radionet-->
    <string name="harmanbar_radionet_Register_fail">Register fail</string>
    <string name="harmanbar_radionet_The_two_passwords_don_t_match__Please_enter_the_same_password_in_both_fiels_and_try_again_">The two passwords don\'t match. Please enter the same password in both fiels and try again.</string>
    <string name="harmanbar_radionet_Sign_Up">Sign Up</string>
    <string name="harmanbar_radionet_Confirm_your_password">Confirm your password</string>
    <string name="harmanbar_radionet_I_would_like_to_get_the_newsletter_">I would like to get the newsletter.</string>
    <string name="harmanbar_radionet_By_creating_an_account_you_agree_to_the_following_Terms_and_conditions_">By creating an account you agree to the following Terms and conditions.</string>
    <string name="harmanbar_radionet_I_agree_to_the_processing_of_my_personal_data_for_the_purposes_set_out_in_the_Privacy_Polic">I agree to the processing of my personal data for the purposes set out in the Privacy Policy</string>
    <string name="harmanbar_radionet_Tracks">Tracks</string>
    <string name="harmanbar_radionet_Other_Albums">Other Albums</string>
    <string name="harmanbar_radionet_Registering____">Registering....</string>
    <string name="harmanbar_radionet_By_country">By country</string>
    <string name="harmanbar_radionet_A_Z">A-Z</string>
    <string name="harmanbar_radionet_Most_heard">Most heard</string>
    <string name="harmanbar_radionet_Continue_without_registration">Continue without registration</string>
    <string name="harmanbar_radionet_Create_New_Playlist">Create New Playlist</string>
    <string name="harmanbar_radionet_Create_playlists_to_play">Create playlists to play</string>
    <string name="harmanbar_radionet_Playlists">Playlists</string>
    <string name="harmanbar_radionet_Email">Email</string>
    <string name="harmanbar_radionet_No_stations_available_">No stations available.</string>
    <string name="harmanbar_radionet_Editor_s_Picks">Editor\'s Picks</string>
    <string name="harmanbar_radionet_Top_100">Top 100</string>
    <string name="harmanbar_radionet_Topics">Topics</string>
    <string name="harmanbar_radionet_Local_Stations">Local Stations</string>
    <string name="harmanbar_radionet_Countries">Countries</string>
    <string name="harmanbar_radionet_Languages">Languages</string>
    <string name="harmanbar_radionet_Cities">Cities</string>
    <string name="harmanbar_radionet_Podcasts">Podcasts</string>
    <string name="harmanbar_radionet_See_all__">See all &gt;</string>
    <string name="harmanbar_radionet_There_are_no_local_stations_available_">There are no local stations available.</string>
    <string name="harmanbar_radionet_For_you">For you</string>
    <string name="harmanbar_radionet_Recently_Played">Recently Played</string>
    <string name="harmanbar_radionet_Recommended">Recommended</string>
    <string name="harmanbar_radionet_You_haven_t_listened_to_any_stations_yet_">You haven\'t listened to any stations yet.</string>
    <string name="harmanbar_radionet_Please_listen_to_some_stations_to_get_recommendations_">Please listen to some stations to get recommendations.</string>
    <string name="harmanbar_radionet_Loading____">Loading....</string>
    <string name="harmanbar_radionet_Discover">Discover</string>
    <string name="harmanbar_radionet_My_Radio">My Radio</string>
    <string name="harmanbar_radionet_Stations">Stations</string>
    <string name="harmanbar_radionet_Logging_out___">Logging out...</string>
    <string name="harmanbar_radionet_Log_out_failed">Log out failed</string>
    <string name="harmanbar_radionet_Hello_">Hello!</string>
    <string name="harmanbar_radionet_Logout">Logout</string>
    <string name="harmanbar_radionet_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_radionet_Cancel">Cancel</string>
    <string name="harmanbar_radionet_Log_Out">Log Out</string>
    <string name="harmanbar_radionet_You_haven_t_added_any_stations_yet_">You haven\'t added any stations yet.</string>
    <string name="harmanbar_radionet_Episodes">Episodes</string>
    <string name="harmanbar_radionet_Similar_podcasts">Similar podcasts</string>
    <string name="harmanbar_radionet_Podcasts_in_Family">Podcasts in Family</string>
    <string name="harmanbar_radionet_No_other_episodes_available">No other episodes available</string>
    <string name="harmanbar_radionet_No_stations_available">No stations available</string>
    <string name="harmanbar_radionet_No_podcasts_available_in_the_family_">No podcasts available in the family.</string>
    <string name="harmanbar_radionet_Deleting____">Deleting....</string>
    <string name="harmanbar_radionet_Add____">Adding....</string>
    <string name="harmanbar_radionet_Delete_success">Delete success</string>
    <string name="harmanbar_radionet_Delete_fail">Delete failed</string>
    <string name="harmanbar_radionet_Added_successfully">Added successfully</string>
    <string name="harmanbar_radionet_Added_failed">Added failed</string>
    <string name="harmanbar_radionet_Link_to_Station">Link to Station</string>
    <string name="harmanbar_radionet_You_haven_t_added_any_podcasts_yet_">You haven\'t added any podcasts yet.</string>
    <string name="harmanbar_radionet_Similar_stations">Similar stations</string>
    <string name="harmanbar_radionet_Stations_in_Family">Stations in Family</string>
    <string name="harmanbar_radionet_No_stations_available_for___">No stations available for %s</string>
    <string name="harmanbar_radionet_All">All</string>
    <string name="harmanbar_radionet_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_radionet_Find_">Find&#160;</string>
    <string name="harmanbar_radionet__your_favorite_station_n">&#160;your favorite station</string>
    <string name="harmanbar_radionet_Search">Search</string>
    <string name="harmanbar_radionet_Searching">Searching</string>
    <string name="harmanbar_radionet_Remove_all_history">Remove all history</string>
    <string name="harmanbar_radionet_NO_Result">NO Result</string>
    <string name="harmanbar_radionet_Search_failed">Search failed</string>
    <string name="harmanbar_radionet_Anonymous_Log_in____">Anonymous Log in....</string>
    <string name="harmanbar_radionet_Login_successful">Login successful</string>
    <string name="harmanbar_radionet_Login_failed">Login failed</string>
    <string name="harmanbar_radionet_Hint">Hint</string>
    <string name="harmanbar_radionet_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_radionet_Confirm">Confirm</string>
    <string name="harmanbar_radionet_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_radionet_Don_t_have_an_account_">Don\'t have an account?</string>
    <string name="harmanbar_radionet_Sign_up_now">Sign up now</string>
    <string name="harmanbar_radionet_Login">Login</string>
    <string name="harmanbar_radionet_Username">Username</string>
    <string name="harmanbar_radionet_Password">Password</string>
    <string name="harmanbar_radionet_Login_with_Tidal_Account">Login with Tidal Account</string>

    <!--pandora-->
    <string name="harmanbar_pandora_If_you_aren_t_in_the_US_or_New_Zealand__please_use_proxy_link">If you aren\'t in the US or New Zealand, please use proxy link</string>
    <string name="harmanbar_pandora_Proxy">Proxy</string>
    <string name="harmanbar_pandora_Your_email">Your email</string>
    <string name="harmanbar_pandora_Password">Password</string>
    <string name="harmanbar_pandora_Port">Port</string>
    <string name="harmanbar_pandora_Login">Login</string>
    <string name="harmanbar_pandora_Create_an_account_for_free__Register">Create an account for free. Register</string>
    <string name="harmanbar_pandora_Please_login_with_your_Pandora_credentials">Please login with your Pandora credentials</string>
    <string name="harmanbar_pandora_Hint">Hint</string>
    <string name="harmanbar_pandora_Please_enter_your_Pandora_email_address">Please enter your Pandora email address</string>
    <string name="harmanbar_pandora_Confirm">Confirm</string>
    <string name="harmanbar_pandora_Please_enter_your_Pandora_password">Please enter your Pandora password</string>
    <string name="harmanbar_pandora_Waiting">Waiting</string>
    <string name="harmanbar_pandora_Login_failed">Login failed</string>
    <string name="harmanbar_pandora_Login_successful">Login successful</string>
    <string name="harmanbar_pandora_Wrong_email_address_or_password">Wrong email address or password</string>
    <string name="harmanbar_pandora_Internal_error">Internal error</string>
    <string name="harmanbar_pandora_Pandora_is_not_available_in_your_country__Set_up_a_control_proxy_">Pandora is not available in your country (Set up a control proxy)</string>
    <string name="harmanbar_pandora_Invalid_Pandora_login">Invalid Pandora login</string>
    <string name="harmanbar_pandora_Loading____">Loading....</string>
    <string name="harmanbar_pandora_Please_login_to_access_Pandora">Please login to access Pandora</string>
    <string name="harmanbar_pandora_Success">Success</string>
    <string name="harmanbar_pandora_Fail">Fail</string>
    <string name="harmanbar_pandora_Log_Out">Log Out</string>
    <string name="harmanbar_pandora_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_pandora_Cancel">Cancel</string>
    <string name="harmanbar_pandora_OK">OK</string>
    <string name="harmanbar_pandora_Remember_me">Remember me</string>

    <!--vtuner-->
    <string name="harmanbar_vtuner_Preset">Preset</string>
    <string name="harmanbar_vtuner_Loading____">Loading....</string>
    <string name="harmanbar_vtuner_Success">Success</string>
    <string name="harmanbar_vtuner_Fail">Fail</string>
    <string name="harmanbar_vtuner_vTuner">vTuner</string>
    <string name="harmanbar_vtuner_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_vtuner_Find_">Find&#160;</string>
    <string name="harmanbar_vtuner__your_favorite_station_n">&#160;your favorite station</string>
    <string name="harmanbar_vtuner__search">&#160;search</string>
    <string name="harmanbar_vtuner_NO_Result">NO Result</string>
    <string name="harmanbar_vtuner_Searching">Searching</string>
    <string name="harmanbar_vtuner_Search_failed">Search failed</string>
    <string name="harmanbar_vtuner_Remove_all_history">Remove all history</string>

    <!--qqfm-->
    <string name="harmanbar_qqfm_Loading____">Loading....</string>
    <string name="harmanbar_qqfm_Success">Success</string>
    <string name="harmanbar_qqfm_Fail">Fail</string>
    <string name="harmanbar_qqfm_QQFM">QQFM</string>
    <string name="harmanbar_qqfm_QQ_music_provides_massive_Music___your_favorite_singer__the_latest_album__and_recommends_music_based">QQ音乐提供海量音乐—您最爱的歌手，最新的专辑，还根据您的个人喜欢推荐音乐。</string>
    <string name="harmanbar_qqfm_Play_any_song_in_QQ_music_">在QQ音乐中播放任何一首歌。</string>
    <string name="harmanbar_qqfm_Click_on_the_player_interface">在播放界面点击</string>
    <string name="harmanbar_qqfm_Select_your_device_from_the_push_list">从推送列表里选择你的设备。</string>
    <string name="harmanbar_qqfm_Select_your_speakers_from_the_list_of_devices_">从设备列表中选择你的音箱。</string>
    <string name="harmanbar_qqfm_do_not_show_again">不再显示</string>
    <string name="harmanbar_qqfm_Go_To_QQ_Music">前往QQ音乐</string>

    <!--napster-->
    <string name="harmanbar_napster_Login_with_Tidal_Account">Login with Tidal Account</string>
    <string name="harmanbar_napster_Aldi_Life_account_has_logout">Aldi Life account has logged out</string>
    <string name="harmanbar_napster_Aldi_Life_account_has_been_changed">Aldi Life account has been changed</string>
    <string name="harmanbar_napster_Aldi_Life_account_is_in_use_in_another_location_">Aldi Life account is in use in another location.</string>
    <string name="harmanbar_napster_Napster_Login">Napster Login</string>
    <string name="harmanbar_napster_Create_playlists_to_play_">Create playlists to play.</string>
    <string name="harmanbar_napster_Playlists">Playlists</string>
    <string name="harmanbar_napster_added_to">added to</string>
    <string name="harmanbar_napster_Added_failed">Added failed</string>
    <string name="harmanbar_napster_Loading____">Loading....</string>
    <string name="harmanbar_napster_Create_New_Playlist">Create New Playlist</string>
    <string name="harmanbar_napster_Enter_a_name_for_the_new_playlist">Enter a name for the new playlist</string>
    <string name="harmanbar_napster_Cancel">Cancel</string>
    <string name="harmanbar_napster_Log_in____">Log in....</string>
    <string name="harmanbar_napster_Create">Create</string>
    <string name="harmanbar_napster_LISTENER_NETWORK">LISTENER NETWORK</string>
    <string name="harmanbar_napster_Fail">Fail</string>
    <string name="harmanbar_napster_VIEW_MORE_FEATURED_MUSIC">VIEW MORE FEATURED MUSIC</string>
    <string name="harmanbar_napster_January">January</string>
    <string name="harmanbar_napster_NEW_FOR_YOU">NEW FOR YOU</string>
    <string name="harmanbar_napster_February">February</string>
    <string name="harmanbar_napster_BROWSE_GENRES___MORE">BROWSE GENRES &amp; MORE</string>
    <string name="harmanbar_napster_March">March</string>
    <string name="harmanbar_napster_Adding____">Adding....</string>
    <string name="harmanbar_napster_April">April</string>
    <string name="harmanbar_napster_Are_you_sure_you_want_to_delete_this_playlist_">Are you sure you want to delete this playlist?</string>
    <string name="harmanbar_napster_May">May</string>
    <string name="harmanbar_napster_Search_for_artists__albums__tracks__and_even_playlists">Search for artists, albums, tracks, and even playlists</string>
    <string name="harmanbar_napster_June">June</string>
    <string name="harmanbar_napster_Find_your_music">Find your music</string>
    <string name="harmanbar_napster_July">July</string>
    <string name="harmanbar_napster_Search">Search</string>
    <string name="harmanbar_napster_August">August</string>
    <string name="harmanbar_napster_Remove_from_My_Music">Remove from My Music</string>
    <string name="harmanbar_napster_September">September</string>
    <string name="harmanbar_napster_Add_All_to_My_Music">Add All to My Music</string>
    <string name="harmanbar_napster_October">October</string>
    <string name="harmanbar_napster_Remove_All_from_My_Music">Remove All from My Music</string>
    <string name="harmanbar_napster_November">November</string>
    <string name="harmanbar_napster_December">December</string>
    <string name="harmanbar_napster_Unlimited_access_to_millions_of_songs">Unlimited access to millions of songs</string>
    <string name="harmanbar_napster_Download_any_song__album_or_playlist">Download any song, album or playlist</string>
    <string name="harmanbar_napster_Play_on_mobile__web_and_home_audio_devices">Play on mobile, web and home audio devices</string>
    <string name="harmanbar_napster_Upgrade_today_for_unlimited_access_to_endless_music_">Upgrade today for unlimited access to endless music.</string>
    <string name="harmanbar_napster_Not_right_now">Not right now</string>
    <string name="harmanbar_napster_Upgrade">Upgrade</string>
    <string name="harmanbar_napster_FEATURED">FEATURED</string>
    <string name="harmanbar_napster_POPULAR">POPULAR</string>
    <string name="harmanbar_napster_My_Music">My Music</string>
    <string name="harmanbar_napster_Featured">Featured</string>
    <string name="harmanbar_napster_logout">logout</string>
    <string name="harmanbar_napster_Logout">Logout</string>
    <string name="harmanbar_napster_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_napster_Log_Out">Log Out</string>
    <string name="harmanbar_napster_Logging_out___">Logging out...</string>
    <string name="harmanbar_napster_Log_out_failed">Log out failed</string>
    <string name="harmanbar_napster_New_Releases">New Releases</string>
    <string name="harmanbar_napster_Delete_Playlist">Delete Playlist</string>
    <string name="harmanbar_napster_Save_Playlist">Save Playlist</string>
    <string name="harmanbar_napster_Add_to_Playlist">Add to Playlist</string>
    <string name="harmanbar_napster_Remove_from_Playlist">Remove from Playlist</string>
    <string name="harmanbar_napster_Add_to_My_Music">Add to My Music</string>
    <string name="harmanbar_napster_Add_to_Favorites">Add to Favorites</string>
    <string name="harmanbar_napster_Remove_from_Favorites">Remove from Favorites</string>
    <string name="harmanbar_napster_View_Album">View Album</string>
    <string name="harmanbar_napster_Play_Track_Radio">Play Track Radio</string>
    <string name="harmanbar_napster_Add_to_My_Stations">Add to My Stations</string>
    <string name="harmanbar_napster_Remove_from_My_Stations">Remove from My Stations</string>
    <string name="harmanbar_napster_Unable_to_complete_this_operation">Unable to complete this operation</string>
    <string name="harmanbar_napster_The_music_is_playing">The music is playing</string>
    <string name="harmanbar_napster_Next_To_Play">Next To Play</string>
    <string name="harmanbar_napster_Please_wait">Please wait</string>
    <string name="harmanbar_napster_Delete">Delete</string>
    <string name="harmanbar_napster_added_to_library">added to library</string>
    <string name="harmanbar_napster_added_to_My_Stations">added to My Stations</string>
    <string name="harmanbar_napster_remove_from">remove from</string>
    <string name="harmanbar_napster_Playlist_saved_to_My_Music">Playlist saved to My Music</string>
    <string name="harmanbar_napster_Release_Date">Release Date</string>
    <string name="harmanbar_napster_Label">Label</string>
    <string name="harmanbar_napster_Tracks">Tracks</string>
    <string name="harmanbar_napster_Albums">Albums</string>
    <string name="harmanbar_napster_Similar_Artists">Similar Artists</string>
    <string name="harmanbar_napster_Top_Tracks">Top Tracks</string>
    <string name="harmanbar_napster_View_All_Albums">View All Albums</string>
    <string name="harmanbar_napster_View_Similar_Artists">View Similar Artists</string>
    <string name="harmanbar_napster_More">More</string>
    <string name="harmanbar_napster_The_current_user_is___">The current user is %s</string>
    <string name="harmanbar_napster_I_got_it">I got it</string>
    <string name="harmanbar_napster_Napster_account_has_logout">Napster account has logout</string>
    <string name="harmanbar_napster_All_Posts">All Posts</string>
    <string name="harmanbar_napster_Staff_Picks">Staff Picks</string>
    <string name="harmanbar_napster_No_posts_are_available_for_this_genre_">No posts are available for this genre.</string>
    <string name="harmanbar_napster_All_Genres">All Genres</string>
    <string name="harmanbar_napster_No_stations_are_available_for_this_genre_">No stations are available for this genre.</string>
    <string name="harmanbar_napster_Stations">Stations</string>
    <string name="harmanbar_napster_NEW">NEW</string>
    <string name="harmanbar_napster_STATIONS">STATIONS</string>
    <string name="harmanbar_napster_New_Released_for">New Released for</string>
    <string name="harmanbar_napster_Add_to_Favorite_Tracks_for_quick_access_to_your_most_often_played_songs_">Add to Favorite Tracks for quick access to your most often played songs.</string>
    <string name="harmanbar_napster_Favorite_Tracks">Favorite Tracks</string>
    <string name="harmanbar_napster_Favorites">Favorites</string>
    <string name="harmanbar_napster_Artists">Artists</string>
    <string name="harmanbar_napster_Listening_History">Listening History</string>
    <string name="harmanbar_napster_Tracks_and_added_to_history_as_you_listen_to_them_on_any_of_the_Napster_services_">Tracks and added to history as you listen to them on any of the Napster services.</string>
    <string name="harmanbar_napster_Success">Success</string>
    <string name="harmanbar_napster_No_tracks_in_this_playlist">No tracks in this playlist</string>
    <string name="harmanbar_napster_Save_radio_stations_for_quick_access_">Save radio stations for quick access.</string>
    <string name="harmanbar_napster_View">View</string>
    <string name="harmanbar_napster_Playlist">Playlist</string>
    <string name="harmanbar_napster_Related_Albums">Related Albums</string>
    <string name="harmanbar_napster_Remove">Remove</string>
    <string name="harmanbar_napster_Related_Posts">Related Posts</string>
    <string name="harmanbar_napster_More_albums">More albums</string>
    <string name="harmanbar_napster_More_artists">More artists</string>
    <string name="harmanbar_napster_More_playlists">More playlists</string>
    <string name="harmanbar_napster_More_tracks">More tracks</string>
    <string name="harmanbar_napster_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_napster_Find_">Find&#160;</string>
    <string name="harmanbar_napster_your_favorite_music_n">your favorite music</string>
    <string name="harmanbar_napster_Search_for_">Search for&#160;</string>
    <string name="harmanbar_napster_Searching">Searching</string>
    <string name="harmanbar_napster_Search_failed">Search failed</string>
    <string name="harmanbar_napster_NO_Result">NO Result</string>
    <string name="harmanbar_napster_Hint">Hint</string>
    <string name="harmanbar_napster_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_napster_Confirm">Confirm</string>
    <string name="harmanbar_napster_Including">Including</string>
    <string name="harmanbar_napster_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_napster_Login_failed">Login failed</string>
    <string name="harmanbar_napster_Login_successful">Login successful</string>
    <string name="harmanbar_napster_Don_t_have_an_account_">Don\'t have an account?</string>
    <string name="harmanbar_napster_Sign_up_now">Sign up now</string>
    <string name="harmanbar_napster_Login">Login</string>
    <string name="harmanbar_napster_Email_Username">Email/Username</string>
    <string name="harmanbar_napster_Password">Password</string>

    <!--spotify-->
    <string name="harmanbar_spotify_Ready_to_play_some_music_">Ready to play some music?</string>
    <string name="harmanbar_spotify_Use_your_voice_to_control_Spotify_">Use your voice to control Spotify.</string>
    <string name="harmanbar_spotify_Ask_Alexa_to_play_your_favourite_playlists__artists_and_genres__For_example__just_say____Alexa__play">Ask Alexa to play your favourite playlists, artists and genres. For example, just say, \"Alexa, play Today\'s Top Hits.\"</string>
    <string name="harmanbar_spotify_Remote_control_">Remote control.</string>
    <string name="harmanbar_spotify_Link_Spotify">LINK TO ALEXA</string>
    <string name="harmanbar_spotify_Link_Music_Service">Link Music Service</string>
    <string name="harmanbar_spotify_We_will_launch_the_Alexa_mobile_app__which_will_allow_you_to_link_your_favorite_music_services_to_th">We will launch the Alexa mobile app, which will allow you to link your favorite music services to the Amazon Alexa account associated with your speaker.\n\nTo link music service accounts in the Alexa app, choose \'Settings\', then \'Music &amp; Media\'.</string>
    <string name="harmanbar_spotify_GET_SPOTIFY_FREE">GET SPOTIFY FREE</string>
    <string name="harmanbar_spotify_How_to_preset_Spotify_">How to preset Spotify?</string>
    <string name="harmanbar_spotify_RUN_NOW">RUN NOW</string>
    <string name="harmanbar_spotify_Tap_the_Spotify_Connect_icon___myicon___">Tap the Spotify Connect icon \" %s \".</string>
    <string name="harmanbar_spotify_Spotify_lets_you_listen_to_millions_of_songs___the_artists_you_love__the_latest_hits__and_di">Spotify lets you listen to millions of songs - the artists you love, the latest hits, and discoveries just for you.</string>
    <string name="harmanbar_spotify_For_information_on_how_to_setup_and_use_Spotify_Connect_please_visit">For information on how to setup and use Spotify Connect please visit</string>
    <string name="harmanbar_spotify_Open_the_Spotify_App_">Open the Spotify App.</string>
    <string name="harmanbar_spotify_Play_any_song_of_your_choosing_">Play any song of your choosing.</string>
    <string name="harmanbar_spotify_Select_your_speaker__or_speaker_group_from_the_list_">Select your speaker, or speaker group from the list.</string>
    <string name="harmanbar_spotify_Tap_the_song_that_is_currently_playing_at_the_bottom_of_the_app_">Tap the song that is currently playing at the bottom of the app.</string>
    <string name="harmanbar_spotify_Don_t_show_again_">Don\'t show again.</string>
    <string name="harmanbar_spotify_https___support_spotify_com_us_learn_more_guides____article_spotify_connect">https://support.spotify.com/us/learn-more/guides/#!/article/spotify-connect</string>
    <string name="harmanbar_spotify_Find_no_Spotify__would_like_to_download_">Find no Spotify, would like to download?</string>
    <string name="harmanbar_spotify_Cancel">Cancel</string>
    <string name="harmanbar_spotify_Confirm">Confirm</string>
    <string name="harmanbar_spotify_READY_TO_PLAY_SOME_MUSIC_">READY TO PLAY SOME MUSIC?</string>
    <string name="harmanbar_spotify_Listen_to_Spotify_on____using_the_Spotify_app_as_a_remote_">Listen to Spotify on %s using the Spotify app as a remote.</string>
    <string name="harmanbar_spotify_Open_up_the_Spotify_app_on_your_phone__tablet_or_laptop_">Open up the Spotify app on your phone, tablet or laptop.</string>
    <string name="harmanbar_spotify_Play_s_song_and_select____">Play a song and select %s.</string>
    <string name="harmanbar_spotify_Devices_Available">Devices Available</string>
    <string name="harmanbar_spotify_Select_your_device_and_start_listening_">Select your device and start listening.</string>
    <string name="harmanbar_spotify_Learn_More">Learn More</string>
    <string name="harmanbar_spotify_OPEN_SPOTIFY">OPEN SPOTIFY</string>
    <string name="harmanbar_spotify_PRESET_SPOTIFY">PRESET SPOTIFY</string>
    <string name="harmanbar_spotify_1__Open_up_the_Spotify_app_on_your_phone__tablet_or_laptop_using_the_same_WiFi_network__n_n2__Play_a">1. Open up the Spotify app on your phone, tablet or laptop using the same WiFi network.\n\n2. Play a song and select your device.\n\n3. Open this app, the Spotify content that is playing shows in the Now Playing view.\n\n4. From the Now Playing view, press the preset button to store the Spotify content to desired preset number.</string>

    <!--tidal-->
    <string name="harmanbar_tidal_A_pre_paid_account_is_needed">A pre-paid account is needed</string>
    <string name="harmanbar_tidal_Search">Search</string>
    <string name="harmanbar_tidal_Tidal_Search">Tidal Search</string>
    <string name="harmanbar_tidal_OTHER_ALBUMS">OTHER ALBUMS</string>
    <string name="harmanbar_tidal_TIDAL_LOGIN">TIDAL LOGIN</string>
    <string name="harmanbar_tidal_My_Playlist">My Playlist</string>
    <string name="harmanbar_tidal_Remove">Remove</string>
    <string name="harmanbar_tidal_Log_in____">Log in....</string>
    <string name="harmanbar_tidal_Created_by_me">Created by me</string>
    <string name="harmanbar_tidal_Created_by_TIDAL">Created by TIDAL</string>
    <string name="harmanbar_tidal_Similar_Albums">Similar Albums</string>
    <string name="harmanbar_tidal_Loading____">Loading....</string>
    <string name="harmanbar_tidal_Success">Success</string>
    <string name="harmanbar_tidal_Fail">Fail</string>
    <string name="harmanbar_tidal_Deleting____">Deleting....</string>
    <string name="harmanbar_tidal_Add____">Add....</string>
    <string name="harmanbar_tidal_Delete_success">Delete success</string>
    <string name="harmanbar_tidal_Added_successfully">Added successfully</string>
    <string name="harmanbar_tidal_Delete_fail">Delete fail</string>
    <string name="harmanbar_tidal_Added_failed">Added failed</string>
    <string name="harmanbar_tidal_What_s_New_Playlists">What\'s New:Playlists</string>
    <string name="harmanbar_tidal_What_s_New_Albums">What\'s New:Albums</string>
    <string name="harmanbar_tidal_What_s_New_Tracks">What\'s New:Tracks</string>
    <string name="harmanbar_tidal_TIDAL_Discovery">TIDAL Discovery</string>
    <string name="harmanbar_tidal_ARTISTS">ARTISTS</string>
    <string name="harmanbar_tidal_Genres">Genres</string>
    <string name="harmanbar_tidal_TRACKS">TRACKS</string>
    <string name="harmanbar_tidal_Login">Login</string>
    <string name="harmanbar_tidal_ALBUMS">ALBUMS</string>
    <string name="harmanbar_tidal_Tidal_account_has_been_changed">Tidal account has been changed</string>
    <string name="harmanbar_tidal_PLAYLISTS">PLAYLISTS</string>
    <string name="harmanbar_tidal_The_current_user_is___">The current user is %s</string>
    <string name="harmanbar_tidal_EXTEND">EXTEND</string>
    <string name="harmanbar_tidal_I_got_it">I got it</string>
    <string name="harmanbar_tidal_Adding____">Adding....</string>
    <string name="harmanbar_tidal_Tidal_account_has_logout">Tidal account has logged out</string>
    <string name="harmanbar_tidal_tracks">tracks</string>
    <string name="harmanbar_tidal_What_s_New">What\'s New</string>
    <string name="harmanbar_tidal_TIDAL_Rising">TIDAL Rising</string>
    <string name="harmanbar_tidal_Playlists">Playlists</string>
    <string name="harmanbar_tidal_My_Music">My Collection</string>
    <string name="harmanbar_tidal_Please_login_tidal_">Please login tidal.</string>
    <string name="harmanbar_tidal_Logout">Logout</string>
    <string name="harmanbar_tidal_Logging_out___">Logging out...</string>
    <string name="harmanbar_tidal_Log_out_failed">Log out failed</string>
    <string name="harmanbar_tidal_Cancel">Cancel</string>
    <string name="harmanbar_tidal_Favorite">Favorite</string>
    <string name="harmanbar_tidal_Add_to_Playlist">Add to Playlist</string>
    <string name="harmanbar_tidal_Play_next">Play next</string>
    <string name="harmanbar_tidal_Go_to_Artist">Go to Artist</string>
    <string name="harmanbar_tidal_Go_to_Album">Go to Album</string>
    <string name="harmanbar_tidal_Artist">Artist</string>
    <string name="harmanbar_tidal_Album">Album</string>
    <string name="harmanbar_tidal_Unable_to_complete_this_operation">Unable to complete this operation</string>
    <string name="harmanbar_tidal_The_music_is_playing">The music is playing</string>
    <string name="harmanbar_tidal_Next_To_Play">Next To Play</string>
    <string name="harmanbar_tidal_Please_wait">Please wait</string>
    <string name="harmanbar_tidal_Tracks">Tracks</string>
    <string name="harmanbar_tidal_Albums">Albums</string>
    <string name="harmanbar_tidal_EPS___SINGLES">EPS &amp; SINGLES</string>
    <string name="harmanbar_tidal_More">More</string>
    <string name="harmanbar_tidal_Artists">Artists</string>
    <string name="harmanbar_tidal_Create_New_Playlist">Create New Playlist</string>
    <string name="harmanbar_tidal_Enter_a_name_for_the_new_playlist">Enter a name for the new playlist</string>
    <string name="harmanbar_tidal_Create">Create</string>
    <string name="harmanbar_tidal_The_name_of_new_list_is_empty_">The name of new list is empty.</string>
    <string name="harmanbar_tidal_Email_Username">Email/Username</string>
    <string name="harmanbar_tidal_Password">Password</string>
    <string name="harmanbar_tidal_Login_with_Tidal_Account">Login with Tidal Account</string>
    <string name="harmanbar_tidal_Hint">Hint</string>
    <string name="harmanbar_tidal_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_tidal_Confirm">Confirm</string>
    <string name="harmanbar_tidal_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_tidal_Login_successful">Login successful</string>
    <string name="harmanbar_tidal_Login_failed">Login failed</string>
    <string name="harmanbar_tidal_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_tidal_Find_your_favorite_music">Find your favorite music</string>
    <string name="harmanbar_tidal_Search_for_artists_tracks_albums_playlists">Search for artists,tracks,albums,playlists</string>
    <string name="harmanbar_tidal_Searching">Searching</string>
    <string name="harmanbar_tidal_Remove_all_history">Remove all history</string>
    <string name="harmanbar_tidal_Search_failed">Search failed</string>
    <string name="harmanbar_tidal_NO_Result">NO Result</string>

    <!--tunein-->
    <string name="harmanbar_tunein_NO_Result">NO Result</string>
    <string name="harmanbar_tunein_Searching">Searching</string>
    <string name="harmanbar_tunein_Search_failed">Search failed</string>
    <string name="harmanbar_tunein_Remove_all_history">Remove all history</string>
    <string name="harmanbar_tunein_TuneIn">TuneIn</string>
    <string name="harmanbar_tunein_No_stations">No stations</string>
    <string name="harmanbar_tunein_My_Station">My Station</string>
    <string name="harmanbar_tunein_Loading____">Loading....</string>
    <string name="harmanbar_tunein_Nothing">Nothing</string>
    <string name="harmanbar_tunein_Connecting___">Connecting...</string>
    <string name="harmanbar_tunein_Fail">Fail</string>
    <string name="harmanbar_tunein_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_tunein_Find_">Find&#160;</string>
    <string name="harmanbar_tunein__your_favorite_station_n">&#160;your favorite station</string>
    <string name="harmanbar_tunein_Search_for_">Search for&#160;</string>
    <string name="harmanbar_tunein_albums_people_voice">albums,people,voice</string>
    <string name="harmanbar_tunein_tunein">tunein</string>
    <string name="harmanbar_tunein__search">&#160;search</string>

    <!--qingtingfm-->
    <string name="harmanbar_qingtingfm_Loading____">Loading....</string>
    <string name="harmanbar_qingtingfm_Success">Success</string>
    <string name="harmanbar_qingtingfm_Fail">Fail</string>
    <string name="harmanbar_qingtingfm_QINGTINGFM">QINGTINGFM</string>
    <string name="harmanbar_qingtingfm_Station">Station</string>
    <string name="harmanbar_qingtingfm_Favorite">Favorite</string>
    <string name="harmanbar_qingtingfm_RANK">RANK</string>
    <string name="harmanbar_qingtingfm_Connecting___">Connecting...</string>
    <string name="harmanbar_qingtingfm_Album">Album</string>
    <string name="harmanbar_qingtingfm_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_qingtingfm_Find_">Find&#160;</string>
    <string name="harmanbar_qingtingfm__your_favorite_station_n">&#160;your favorite station</string>
    <string name="harmanbar_qingtingfm_Search_for_">Search for&#160;</string>
    <string name="harmanbar_qingtingfm_Station__Album_and_Program">Station, Album and Program</string>
    <string name="harmanbar_qingtingfm_Searching">Searching</string>
    <string name="harmanbar_qingtingfm_Remove_all_history">Remove all history</string>
    <string name="harmanbar_qingtingfm_NO_Result">NO Result</string>
    <string name="harmanbar_qingtingfm_Search_failed">Search failed</string>
    <string name="harmanbar_qingtingfm_Ranking_List">Ranking List</string>
    <string name="harmanbar_qingtingfm_States_Station">States Station</string>
    <string name="harmanbar_qingtingfm_Net_Station">Net Station</string>
    <string name="harmanbar_qingtingfm_QingTing_FM_Search">QingTing FM Search</string>
    <string name="harmanbar_qingtingfm_All">All</string>
    <string name="harmanbar_qingtingfm_Radio_Station">Radio Station</string>
    <string name="harmanbar_qingtingfm_Programme">Programme</string>

    <!--ximalaya-->
    <string name="harmanbar_ximalaya_Loading____">Loading....</string>
    <string name="harmanbar_ximalaya_Success">Success</string>
    <string name="harmanbar_ximalaya_Fail">Fail</string>
    <string name="harmanbar_ximalaya_Creat_time__">Creat time&#160;&#160;</string>
    <string name="harmanbar_ximalaya_Play_count__">Play count&#160;&#160;</string>
    <string name="harmanbar_ximalaya_XIMALAYA">XIMALAYA</string>
    <string name="harmanbar_ximalaya_GENRE">GENRE</string>
    <string name="harmanbar_ximalaya_Last_update">Last update</string>
    <string name="harmanbar_ximalaya_Hot">Hot</string>
    <string name="harmanbar_ximalaya_Recent_update">Recent update</string>
    <string name="harmanbar_ximalaya_Classic">Classic</string>
    <string name="harmanbar_ximalaya_Search_for_your_favorite_music">Search for your favorite music</string>
    <string name="harmanbar_ximalaya_Search_for_songs__artists_and_albums">Search for songs, artists and albums</string>
    <string name="harmanbar_ximalaya_LIVE">LIVE</string>
    <string name="harmanbar_ximalaya_HOST">HOST</string>
    <string name="harmanbar_ximalaya_Ranking_List">Ranking List</string>
    <string name="harmanbar_ximalaya_your_favorite_voice_n">your favorite voice\n</string>
    <string name="harmanbar_ximalaya_Search">Search</string>
    <string name="harmanbar_ximalaya_Search_for">Search for</string>
    <string name="harmanbar_ximalaya_albums_people_voice">albums,people,voice</string>
    <string name="harmanbar_ximalaya_XIMALAYA_SEARCH">XIMALAYA SEARCH</string>

    <!--iheartradio-->
    <string name="harmanbar_iheartradio_Email_Address">Email Address</string>
    <string name="harmanbar_iheartradio_Birth_Year">Birth Year</string>
    <string name="harmanbar_iheartradio_Sign_up">Sign up</string>
    <string name="harmanbar_iheartradio__and_">&#160;and&#160;</string>
    <string name="harmanbar_iheartradio_Citys">Citys</string>
    <string name="harmanbar_iheartradio_All_Stations">All Stations</string>
    <string name="harmanbar_iheartradio_International">International</string>
    <string name="harmanbar_iheartradio_Loading____">Loading....</string>
    <string name="harmanbar_iheartradio_Fail">Fail</string>
    <string name="harmanbar_iheartradio_Local_Radios">Local Radios</string>
    <string name="harmanbar_iheartradio_Featured">Featured</string>
    <string name="harmanbar_iheartradio_No_results">No results</string>
    <string name="harmanbar_iheartradio_Custom_Radio">Custom Radio</string>
    <string name="harmanbar_iheartradio_Success">Success</string>
    <string name="harmanbar_iheartradio_iHeartRadio">iHeartRadio</string>
    <string name="harmanbar_iheartradio_Live_Radio">Live Radio</string>
    <string name="harmanbar_iheartradio_Podcasts">Podcasts</string>
    <string name="harmanbar_iheartradio_Login">Login</string>
    <string name="harmanbar_iheartradio_Local_Stations">Local Stations</string>
    <string name="harmanbar_iheartradio_Cancel">Cancel</string>
    <string name="harmanbar_iheartradio_Options">Options</string>
    <string name="harmanbar_iheartradio_Log_In">Log In</string>
    <string name="harmanbar_iheartradio_Please_enter_your_email_address">Please enter your email address</string>
    <string name="harmanbar_iheartradio_Confirm">Confirm</string>
    <string name="harmanbar_iheartradio_Must_enter_your_password">Must enter your password</string>
    <string name="harmanbar_iheartradio_Invalid_Email_Address">Invalid Email Address</string>
    <string name="harmanbar_iheartradio_Retry">Retry</string>
    <string name="harmanbar_iheartradio_Invalid_Password">Invalid Password</string>
    <string name="harmanbar_iheartradio_Password_must_be_4_32_characters_long__consisting_of_letters_or_numbers__with_no_spaces_">Password must be 4-32 characters long, consisting of letters or numbers, with no spaces.</string>
    <string name="harmanbar_iheartradio_Login_failed">Login failed</string>
    <string name="harmanbar_iheartradio_OK">OK</string>
    <string name="harmanbar_iheartradio_Authentication_Failed">Authentication Failed</string>
    <string name="harmanbar_iheartradio_Loading">Loading</string>
    <string name="harmanbar_iheartradio_Service_unavailable">Service unavailable</string>
    <string name="harmanbar_iheartradio_The_email_password_does_not_match_our_records__Please_try_again_">The email/password does not match our records. Please try again.</string>
    <string name="harmanbar_iheartradio_Sign_Up">Sign Up</string>
    <string name="harmanbar_iheartradio_Have_an_account__Log_in">Have an account? Log in</string>
    <string name="harmanbar_iheartradio_Sorry__you_are_not_eligible_to_register_for_iHeartRadio_">Sorry, you are not eligible to register for iHeartRadio.</string>
    <string name="harmanbar_iheartradio_Account_error">Account error</string>
    <string name="harmanbar_iheartradio_Password_error">Password error</string>
    <string name="harmanbar_iheartradio_Login_failure">Login failure</string>
    <string name="harmanbar_iheartradio_Please_enter_zip_code">Please enter zip code</string>
    <string name="harmanbar_iheartradio_Please_enter_birth_year">Please enter birth year</string>
    <string name="harmanbar_iheartradio_You_must_agree_to_the_Terms_of_Use_and_Privacy_PolicyYou_must_agree_to_the_Terms_of_Use_">You must agree to the Terms of Use and Privacy Policy</string>
    <string name="harmanbar_iheartradio_Invalid_Email_">Invalid Email.</string>
    <string name="harmanbar_iheartradio_Password_must_be_4_32_characters_">Password must be 4-32 characters.</string>
    <string name="harmanbar_iheartradio_Please_enter_a_valid_birth_year_">Please enter a valid birth year.</string>
    <string name="harmanbar_iheartradio_ZIP_Code">ZIP Code</string>
    <string name="harmanbar_iheartradio_Gender__optional_">Gender (optional)</string>
    <string name="harmanbar_iheartradio_Male">Male</string>
    <string name="harmanbar_iheartradio_Female">Female</string>
    <string name="harmanbar_iheartradio_I_agree_to_the_">I agree to the&#160;</string>
    <string name="harmanbar_iheartradio_Terms_of_Service">Terms of Service</string>
    <string name="harmanbar_iheartradio_Privacy_Policy">Privacy Policy</string>
    <string name="harmanbar_iheartradio_I_agree_to_the_Terms_of_Service_and_">I agree to the Terms of Service and&#160;</string>
    <string name="harmanbar_iheartradio_Custom_Radio_may_contain_explicit_content__which_may_be_inappropriate_for_users_under_th">Custom Radio may contain explicit content, which may be inappropriate for users under the age of 13. By turning explicit content off, access to Custom Radio will be disabled.</string>
    <string name="harmanbar_iheartradio_Explicit">Explicit</string>
    <string name="harmanbar_iheartradio_Explicit_Content">Explicit Content</string>
    <string name="harmanbar_iheartradio_Hint">Hint</string>
    <string name="harmanbar_iheartradio_User_has_logout_Please_login_again">User has logout,Please login again</string>
    <string name="harmanbar_iheartradio_For_You">For You</string>
    <string name="harmanbar_iheartradio_My_Stations">My Stations</string>
    <string name="harmanbar_iheartradio_You_have_no_favorites">You have no favorites</string>
    <string name="harmanbar_iheartradio_Logged_In_As">Logged In As</string>
    <string name="harmanbar_iheartradio_Terms_of_Use">Terms of Use</string>
    <string name="harmanbar_iheartradio_Settings">Settings</string>
    <string name="harmanbar_iheartradio_On">On</string>
    <string name="harmanbar_iheartradio_Off">Off</string>
    <string name="harmanbar_iheartradio_Logging_out___">Logging out...</string>
    <string name="harmanbar_iheartradio_Log_out_failed">Log out failed</string>
    <string name="harmanbar_iheartradio_Logout">Logout</string>
    <string name="harmanbar_iheartradio_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_iheartradio_Log_Out">Log Out</string>
    <string name="harmanbar_iheartradio_iHeartRadio_account_has_been_changed">iHeartRadio account has been changed</string>
    <string name="harmanbar_iheartradio_The_current_user_is___">The current user is %s</string>
    <string name="harmanbar_iheartradio_I_got_it">I got it</string>
    <string name="harmanbar_iheartradio_iHeartRadio_account_has_logout">iHeartRadio account has logged out</string>
    <string name="harmanbar_iheartradio_Unfavorite">Unfavorite</string>
    <string name="harmanbar_iheartradio_Favorite">Favorite</string>
    <string name="harmanbar_iheartradio_Play_next">Play next</string>
    <string name="harmanbar_iheartradio_Go_to_Artist">Go to Artist</string>
    <string name="harmanbar_iheartradio_Go_to_Album">Go to Album</string>
    <string name="harmanbar_iheartradio_Adding____">Adding....</string>
    <string name="harmanbar_iheartradio_Station_added_to_your_Favorite_Stations">Station added to your Favorite Stations</string>
    <string name="harmanbar_iheartradio_Deleting____">Deleting....</string>
    <string name="harmanbar_iheartradio_Station_deleted_from_your_Favorite_Stations">Station deleted from your Favorite Stations</string>
    <string name="harmanbar_iheartradio_Delete_fail">Delete fail</string>
    <string name="harmanbar_iheartradio_No_stations">No stations</string>
    <string name="harmanbar_iheartradio_Create_Artist_Radio_or_Search_for_Station">Create Artist Radio or Search for Station</string>
    <string name="harmanbar_iheartradio_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_iheartradio_Search_for_stations__artists_">Search for stations, artists,</string>
    <string name="harmanbar_iheartradio_songs_or_podcasts">songs or podcasts</string>
    <string name="harmanbar_iheartradio_IHEARTRADIO_SEARCH">IHEARTRADIO SEARCH</string>
    <string name="harmanbar_iheartradio_Remove_all_history">Remove all history</string>
    <string name="harmanbar_iheartradio_Stations">Stations</string>
    <string name="harmanbar_iheartradio_Artists">Artists</string>
    <string name="harmanbar_iheartradio_Songs">Songs</string>
    <string name="harmanbar_iheartradio_Searching">Searching</string>
    <string name="harmanbar_iheartradio_Preset">Preset</string>
    <string name="harmanbar_iheartradio_Please_enter_a_valid_address">Please enter a valid address</string>
    <string name="harmanbar_iheartradio_Password">Password</string>
    <string name="harmanbar_iheartradio_Explicit_Content_Restricted">Explicit Content Restricted</string>
    <string name="harmanbar_iheartradio_Custom_radio_may_contain_explicit_lyrics__You_can_turn_on_explicit_content_in_Options_">Custom radio may contain explicit lyrics. You can turn on explicit content in Options.</string>
    <string name="harmanbar_iheartradio_Account">Account</string>
    <string name="harmanbar_iheartradio_Legal">Legal</string>
    <string name="harmanbar_iheartradio_Favorite_Stations">Favorite Stations</string>
    <string name="harmanbar_iheartradio_Recent_Stations">Recent Stations</string>
    <string name="harmanbar_iheartradio_Added_fail">Added fail</string>
    <string name="harmanbar_iheartradio_Sorry__you_ve_reached_your_daily_skip_limit__Want_to_know_more__Visit_help_iheartradio_com_">Sorry, you\'ve reached your daily skip limit. Want to know more? Visit help.iheartradio.com.</string>
    <string name="harmanbar_iheartradio_Sorry__you_ve_reached_your_skip_limit_for_this_station__Want_to_know_more__Visit_help_iheartradio_co">Sorry, you\'ve reached your skip limit for this station. Want to know more? Visit help.iheartradio.com.</string>
    <string name="harmanbar_iheartradio_Originals">Originals</string>
    <string name="harmanbar_iheartradio_No_results_for______nCheck_your_spelling_or_try_another_Search">No results for \'%s\'\nCheck your spelling or try another Search</string>

    <!--qobuz-->
    <string name="harmanbar_qobuz_Playing_____n_The_rights_holders_have_not_made_the_streaming_of_this_track_possible_n_The_righ">Playing %s \n The rights-holders have not made the streaming of this track possible\n The rights holders have not made listening to this track\'s samples possible</string>
    <string name="harmanbar_qobuz_Hint">Hint</string>
    <string name="harmanbar_qobuz_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_qobuz_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_qobuz_Login_failed">Login failed</string>
    <string name="harmanbar_qobuz_Login_successful">Login successful</string>
    <string name="harmanbar_qobuz_Don_t_have_an_account_">Don\'t have an account?</string>
    <string name="harmanbar_qobuz_Sign_up_now">Sign up now</string>
    <string name="harmanbar_qobuz_Login">Login</string>
    <string name="harmanbar_qobuz_Email_Username">Email/Username</string>
    <string name="harmanbar_qobuz_Password">Password</string>
    <string name="harmanbar_qobuz_Login_with_Tidal_Account">Login with Tidal Account</string>
    <string name="harmanbar_qobuz_Search_the_entire_Qobuz_catalogue_for_albums__tracks__artists_and_playlists_">Search the entire Qobuz catalogue for albums, tracks, artists and playlists.</string>
    <string name="harmanbar_qobuz_Delete_from_Favorites">Delete from Favorites</string>
    <string name="harmanbar_qobuz_has_been_subscription">has been subscription</string>
    <string name="harmanbar_qobuz_Released">Released</string>
    <string name="harmanbar_qobuz_Stereo">Stereo</string>
    <string name="harmanbar_qobuz_See_digital_booklet">See digital booklet</string>
    <string name="harmanbar_qobuz_January">January</string>
    <string name="harmanbar_qobuz_added_to_favorites">added to favorites</string>
    <string name="harmanbar_qobuz_February">February</string>
    <string name="harmanbar_qobuz_March">March</string>
    <string name="harmanbar_qobuz_See_all_artists">See all artists</string>
    <string name="harmanbar_qobuz_April">April</string>
    <string name="harmanbar_qobuz_See_all_tracks">See all tracks</string>
    <string name="harmanbar_qobuz_May">May</string>
    <string name="harmanbar_qobuz_See_all_albums">See all albums</string>
    <string name="harmanbar_qobuz_June">June</string>
    <string name="harmanbar_qobuz_See_all_playlists">See all playlists</string>
    <string name="harmanbar_qobuz_July">July</string>
    <string name="harmanbar_qobuz_Qobuz_Playlist">Qobuz Playlist</string>
    <string name="harmanbar_qobuz_August">August</string>
    <string name="harmanbar_qobuz_Streaming_charts">Streaming charts</string>
    <string name="harmanbar_qobuz_September">September</string>
    <string name="harmanbar_qobuz_Download_charts">Download charts</string>
    <string name="harmanbar_qobuz_October">October</string>
    <string name="harmanbar_qobuz_Selected_by_Qobuz">Selected by Qobuz</string>
    <string name="harmanbar_qobuz_November">November</string>
    <string name="harmanbar_qobuz_Selected_by_the_Media">Selected by the Media</string>
    <string name="harmanbar_qobuz_December">December</string>
    <string name="harmanbar_qobuz_Loading____">Loading....</string>
    <string name="harmanbar_qobuz_One_more_thing__Access_from_mobile_networks_to_large_Hi_Res_files_may_quickly_consume_your_data_plan">One more thing! Access from mobile networks to large Hi-Res files may quickly consume your data plan, or result in increased cost from the operator. For this reason, we recommend that you do not activate the Hi-Res mobile network. Do you really want to continue?</string>
    <string name="harmanbar_qobuz_Disc">Disc</string>
    <string name="harmanbar_qobuz_Tracks">Tracks</string>
    <string name="harmanbar_qobuz_Similar_Albums">Similar Albums</string>
    <string name="harmanbar_qobuz_More">More</string>
    <string name="harmanbar_qobuz_Search">Search</string>
    <string name="harmanbar_qobuz_Playlists">Playlists</string>
    <string name="harmanbar_qobuz_Favorites">Favorites</string>
    <string name="harmanbar_qobuz_Purchases">Purchases</string>
    <string name="harmanbar_qobuz_No_Results">No Results</string>
    <string name="harmanbar_qobuz_Streaming">Streaming</string>
    <string name="harmanbar_qobuz_Logout">Logout</string>
    <string name="harmanbar_qobuz_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_qobuz_Cancel">Cancel</string>
    <string name="harmanbar_qobuz_Log_Out">Log Out</string>
    <string name="harmanbar_qobuz_logout">logout</string>
    <string name="harmanbar_qobuz_Logging_out___">Logging out...</string>
    <string name="harmanbar_qobuz_Log_out_failed">Log out failed</string>
    <string name="harmanbar_qobuz_Confirm">Confirm</string>
    <string name="harmanbar_qobuz_All_Genres">All Genres</string>
    <string name="harmanbar_qobuz_Qobuz">Qobuz</string>
    <string name="harmanbar_qobuz_New_Releases">New Releases</string>
    <string name="harmanbar_qobuz_Add_to_Favorites">Add to Favorites</string>
    <string name="harmanbar_qobuz_Add_to_Playlists">Add to Playlists</string>
    <string name="harmanbar_qobuz_See_Artist">See Artist</string>
    <string name="harmanbar_qobuz_Description">Description</string>
    <string name="harmanbar_qobuz_Biography">Biography</string>
    <string name="harmanbar_qobuz_Subscribe">Subscribe</string>
    <string name="harmanbar_qobuz_Edit_the_name">Edit the name</string>
    <string name="harmanbar_qobuz_Are_you_sure_you_want_to_delete_this_playlist_">Are you sure you want to delete this playlist?</string>
    <string name="harmanbar_qobuz_Edit_confidentiality">Edit confidentiality</string>
    <string name="harmanbar_qobuz_Qobuz_Search">Qobuz Search</string>
    <string name="harmanbar_qobuz_Delete">Delete</string>
    <string name="harmanbar_qobuz_Log_in____">Log in....</string>
    <string name="harmanbar_qobuz_Play_next">Play next</string>
    <string name="harmanbar_qobuz_See_Album">See Album</string>
    <string name="harmanbar_qobuz_By">By</string>
    <string name="harmanbar_qobuz_Unsubscribe">Unsubscribe</string>
    <string name="harmanbar_qobuz_Unable_to_complete_this_operation">Unable to complete this operation</string>
    <string name="harmanbar_qobuz_The_music_is_playing">The music is playing</string>
    <string name="harmanbar_qobuz_Next_To_Play">Next To Play</string>
    <string name="harmanbar_qobuz_Added_failed">Added failed</string>
    <string name="harmanbar_qobuz_Please_wait">Please wait</string>
    <string name="harmanbar_qobuz_Request_failed__not_found__404_">Request failed: not found (404)</string>
    <string name="harmanbar_qobuz_Remove_from_Favorites">Remove from Favorites</string>
    <string name="harmanbar_qobuz_Edit_name_of_playlist">Edit name of playlist</string>
    <string name="harmanbar_qobuz_Enter_a_new_name_for_your_playlist">Enter a new name for your playlist</string>
    <string name="harmanbar_qobuz_Save">Save</string>
    <string name="harmanbar_qobuz_Private">Private</string>
    <string name="harmanbar_qobuz_Public">Public</string>
    <string name="harmanbar_qobuz_Collaborative">Collaborative</string>
    <string name="harmanbar_qobuz_Please_note">Please note</string>
    <string name="harmanbar_qobuz_Are_you_sure_want_to_delete_this_playlist_">Are you sure want to delete this playlist?</string>
    <string name="harmanbar_qobuz_album">album</string>
    <string name="harmanbar_qobuz_Albums">Albums</string>
    <string name="harmanbar_qobuz_Discography">Discography</string>
    <string name="harmanbar_qobuz_Similar_artists">Similar artists</string>
    <string name="harmanbar_qobuz_No_albums_available">No albums available</string>
    <string name="harmanbar_qobuz_Qobuz_account_has_been_changed">Qobuz account has been changed</string>
    <string name="harmanbar_qobuz_The_current_user_is___">The current user is %s</string>
    <string name="harmanbar_qobuz_I_got_it">I got it</string>
    <string name="harmanbar_qobuz_Qobuz_account_has_logout">Qobuz account has logged out</string>
    <string name="harmanbar_qobuz_No_albums_added_to_favorites">No albums added to favorites</string>
    <string name="harmanbar_qobuz_No_artists_added_to_favorites">No artists added to favorites</string>
    <string name="harmanbar_qobuz_Artists">Artists</string>
    <string name="harmanbar_qobuz_No_tracks_added_to_favorites">No tracks added to favorites</string>
    <string name="harmanbar_qobuz_Add_to_a_playlist">Add to a playlist</string>
    <string name="harmanbar_qobuz_The_name_of_new_list_is_empty_">The name of new list is empty.</string>
    <string name="harmanbar_qobuz_added_to">added to</string>
    <string name="harmanbar_qobuz_New_playlist">New playlist</string>
    <string name="harmanbar_qobuz_Enter_a_name_for_this_playlist">Enter a name for this playlist</string>
    <string name="harmanbar_qobuz_Create">Create</string>
    <string name="harmanbar_qobuz_Finish">Finish</string>
    <string name="harmanbar_qobuz_No_available_albums_in_your_purchases">No available albums in your purchases</string>
    <string name="harmanbar_qobuz_No_available_tracks_in_your_purchases">No available tracks in your purchases</string>
    <string name="harmanbar_qobuz_Search_isn_t_available_the_phone_isn_t_connect_to_the_Internet">Search isn\'t available,the phone isn\'t connect to the Internet</string>
    <string name="harmanbar_qobuz_Remove_all_history">Remove all history</string>
    <string name="harmanbar_qobuz_Searching">Searching</string>
    <string name="harmanbar_qobuz_Search_failed">Search failed</string>
    <string name="harmanbar_qobuz_No_matching_results_for_______">No matching results for \"%s\"</string>
    <string name="harmanbar_qobuz_History_is_empty">History is empty</string>

    <!--iot-->
    <string name="harmanbar_iot_Sign_In">Sign In</string>
    <string name="harmanbar_iot_Show">Show</string>
    <string name="harmanbar_iot_Hide">Hide</string>
    <string name="harmanbar_iot_Forgot_your_password_">Forget your password?</string>
    <string name="harmanbar_iot_Email">Email</string>
    <string name="harmanbar_iot_User_name_or_E_mail">User name or E-mail</string>
    <string name="harmanbar_iot_Reset_password_success_">Reset password success.</string>
    <string name="harmanbar_iot_Sign_up">Sign up</string>
    <string name="harmanbar_iot_Don_t_have_an_account__Sign_up">Don\'t have an account? Sign up</string>
    <string name="harmanbar_iot_LOGIN">LOGIN</string>
    <string name="harmanbar_iot_Please_enter_the_correct_mailbox_">Please enter the correct mailbox.</string>
    <string name="harmanbar_iot_Email_address_length_must_not_be_more_than_64_characters_">Email address length must not be more than 64 characters.</string>
    <string name="harmanbar_iot_User_name_length_must_not_be_more_than_64_characters_">User name length must not be more than 64 characters.</string>
    <string name="harmanbar_iot_Password_length_must_not_be_more_than_64_characters_">Password length must not be more than 64 characters.</string>
    <string name="harmanbar_iot_Sending_request">Sending request</string>
    <string name="harmanbar_iot_Your_user_name_or_password_is_incorrect_">Your user name or password is incorrect.</string>
    <string name="harmanbar_iot_Submit">Submit</string>
    <string name="harmanbar_iot_Tips">Tips</string>
    <string name="harmanbar_iot_The_user_has_already_registered___Please_go_to_login_">The user has already registered. \"Please go to login.\"</string>
    <string name="harmanbar_iot_ok">ok</string>
    <string name="harmanbar_iot_The_mailbox_already_exists_">The mailbox already exists.</string>
    <string name="harmanbar_iot_SIGN_UP_1_3">SIGN UP 1/3</string>
    <string name="harmanbar_iot_User_name_cannot_be_a_mailbox_">User name cannot be a mailbox.</string>
    <string name="harmanbar_iot_Your_email_is_incorrect_">Your email is incorrect.</string>
    <string name="harmanbar_iot_Your_user_name_or_email_is_incorrect_">Your user name or email is incorrect.</string>
    <string name="harmanbar_iot______Your_password_needs_to_meet_the_following_conditions__nAt_least_one_number_or_one_symbol_is_inc">&#160;&#160;&#160;&#160;&#160;Your password needs to meet the following conditions:\nAt leaset one number and one letter are included.\nAt least 8 characters long.</string>
    <string name="harmanbar_iot_The_passwords_entered_did_not_match_">The passwords entered did not match.</string>
    <string name="harmanbar_iot_Password_length_must_not_be_less_than_8_characters_">Password length must not be less than 8 characters.</string>
    <string name="harmanbar_iot_Password_length_must_not_be_more_than_64_characters">Password length must not be more than 64 characters</string>
    <string name="harmanbar_iot_Success">Success</string>
    <string name="harmanbar_iot_Fail">Fail</string>
    <string name="harmanbar_iot_CHANGE_PASSWORD">CHANGE PASSWORD</string>
    <string name="harmanbar_iot_SIGN_UP_2_3">SIGN UP 2/3</string>
    <string name="harmanbar_iot_Switch_Account">Switch Account</string>
    <string name="harmanbar_iot_Your_current_login_account_is_XXXXXXXXXXXXXX__If_you_want_to_switch_accounts__Please_logout_first_">Your current login account is XXXXXXXXXXXXXX. If you want to switch accounts, Please logout first.</string>
    <string name="harmanbar_iot_You_are_currently_logged_into_EDGE_Home_with_XXXXXXXXXXXXXX__If_this_is_not_you__please_switch_accou">You are currently logged into EDGE Home with XXXXXXXXXXXXXX. If this is not you, please switch accounts.</string>
    <string name="harmanbar_iot_Change_your_password_">Change your password?</string>
    <string name="harmanbar_iot_IoT_Login">IoT Login</string>
    <string name="harmanbar_iot_The_verification_code_has_been_sent_to_your_mailbox_XXXXXXXX__Please_check_and_enter_the_verificatio">The verification code has been sent to your mailbox XXXXXXXX. Please check and enter the verification code.</string>
    <string name="harmanbar_iot_Please_check_your_spam_folder_if_you_don_t_see_the_message_in_your_inbox_">Please check your spam folder if you don\'t see the message in your inbox.</string>
    <string name="harmanbar_iot_Next">Next</string>
    <string name="harmanbar_iot_Resend_the_verification_code">Resend the verification code</string>
    <string name="harmanbar_iot_A_verification_code_has_been_sent_to__nXXXXXXXX_">A verification code has been sent to \nXXXXXXXX.</string>
    <string name="harmanbar_iot_SIGN_UP_3_3">SIGN UP 3/3</string>
    <string name="harmanbar_iot_Verification_code_error_">Verification code error.</string>
    <string name="harmanbar_iot_Error">Error</string>
    <string name="harmanbar_iot_Your_device_is_not_binded__nClick_the___Binding_Device___button_to_complete_the_operation__n__n_Afte">Your device is not binded.\nClick the \"Binding Device\" button to complete the operation.\n \n After the device is bound, remote control and voice control can be performed on the device.\n\n</string>
    <string name="harmanbar_iot_Current_device_login_account_">Current device login account:</string>
    <string name="harmanbar_iot_Binding_device">Binding device</string>
    <string name="harmanbar_iot_Use_voice_to_control_the_light__click____Enable___to_start_the_skill__n_n">Use voice to control the light, click \" Enable\" to start the skill.\n\n</string>
    <string name="harmanbar_iot_Before_enabling__please_click_on___How_to_enable_skill___">Before enabling, please click on \"How to enable skill\".</string>
    <string name="harmanbar_iot_Enable">Enable</string>
    <string name="harmanbar_iot_Done">Done</string>
    <string name="harmanbar_iot_Binding_success">Binding success</string>
    <string name="harmanbar_iot_HELP">HELP</string>
    <string name="harmanbar_iot_Enter_the_email_address_you_used_to_create_your_account_to_retrieve_your_password_">Enter the email address you used to create your account to retrieve your password.</string>
    <string name="harmanbar_iot_Email_address">Email address</string>
    <string name="harmanbar_iot_Please_enter_the_correct_email_address">Please enter the correct email address</string>
    <string name="harmanbar_iot_Email_address_length_must_not_be_more_than_64_characters">Email address length must not be more than 64 characters</string>
    <string name="harmanbar_iot_cancle">cancle</string>
    <string name="harmanbar_iot_You_can_bind_the_device_with_your_IoT_account__After_successful_login_you_can_use_alexa_voice_comman">You can bind the device with your IoT account. After successful login you can use alexa voice command to control the light of the speaker.</string>
    <string name="harmanbar_iot_Please_bind_the_device__After_successful_login_you_can_use_the_voice_control_to_switch_light_and_the">Please bind the device, After successful login you can use the voice control to switch light and the light mode.</string>
    <string name="harmanbar_iot_Skip">Skip</string>
    <string name="harmanbar_iot_This_feature_is_needed_in_the_future_and_you_can_do_the_following_Select_the_device_in_the_device_li">This feature is needed in the future and you can do the following:Select the device in the device list, click on \"Gears\" -&gt; Binding device</string>
    <string name="harmanbar_iot_Please_set_a_new_password_for_this_account__nXXXXXXXX">Please set a new password for this account:\nXXXXXXXX</string>
    <string name="harmanbar_iot_______Your_password_needs_to_meet_the_following_conditions__nAt_least_one_number_or_one_symbol_is_in">&#160;&#160;&#160;&#160;&#160;&#160;Your password needs to meet the following conditions:\nAt leaset one number and one letter are included.\nAt least 8 characters long.</string>
    <string name="harmanbar_iot_______Your_password_needs_to_meet_the_following_conditions__nAt_least_one_number_and_one_letter_are_">&#160;&#160;&#160;&#160;&#160;&#160;Your password needs to meet the following conditions:\nAt least one number and one letter are included. The length shouldn’t be shorter than 8 characters.</string>
    <string name="harmanbar_iot_Password_length_must_not_be_less_than_8_characters">Password length must not be less than 8 characters</string>
    <string name="harmanbar_iot_Light_On_Off">Light On/Off</string>
    <string name="harmanbar_iot_Adjustment">Adjustment</string>
    <string name="harmanbar_iot_Scheduling">Scheduling</string>
    <string name="harmanbar_iot_Adjust_the_color_and_brightness_of_your_light">Adjust the color and brightness of your light</string>
    <string name="harmanbar_iot_Set_a_schedule_for_your_light_to_turn_on_off_during_the_day">Set a schedule for your light to turn on/off during the day</string>
    <string name="harmanbar_iot_Light">Light</string>
    <string name="harmanbar_iot_Lighting_Adjustment">Lighting Adjustment</string>
    <string name="harmanbar_iot_Lighting_Mode">Lighting Mode</string>
    <string name="harmanbar_iot_Time_Light_Switch">Time Light Switch</string>
    <string name="harmanbar_iot_Light_Settings">Light Settings</string>
    <string name="harmanbar_iot_Turn_off_at">Turn off at</string>
    <string name="harmanbar_iot_Turn_on_at">Turn on at</string>
    <string name="harmanbar_iot_Set_the_time_that_the_light_will_gradually_dim_and_then_shut_off__Tap_the_time_to_set_the_time_of_da">Set the time that the light will gradually dim and then shut off. Tap the time to set the time of day, then tap the &gt; to set the day(s) it will occur.</string>
    <string name="harmanbar_iot_Set_the_time_that_the_light_will_turn_on_and_gradually_brighten__Tap_the_time_to_set_the_time_of_day">Set the time that the light will turn on and gradually brighten. Tap the time to set the time of day, then tap the &gt; to set the day(s) it will occur.</string>
    <string name="harmanbar_iot_Sleep_Time">Sleep Time</string>
    <string name="harmanbar_iot_Wake_Time">Wake Time</string>
    <string name="harmanbar_iot_Set_your_bedtime__when_the_light_goes_from_light_to_dark_to_off_">Set your bedtime, when the light goes from light to dark to off.</string>
    <string name="harmanbar_iot_Set_the_wake_up_time__when_the_light_goes_from_dark_to_light_until_it_is_at_its_brightest_">Set the wake-up time, when the light goes from dark to light until it is at its brightest.</string>
    <string name="harmanbar_iot_Save">Save</string>
    <string name="harmanbar_iot_Brightness">Brightness</string>
    <string name="harmanbar_iot_Lighting_mode">Lighting mode</string>
    <string name="harmanbar_iot_You_can_also_tell_Alexa_to_switch_the_lighting_scene__Just_say__Turn_on_xxxx_s_study_mode__to_alexa_">You can also tell Alexa to switch the lighting scene. Just say \"Turn on xxxx\'s study mode\" to alexa. xxxx is the name you gave your speaker</string>
    <string name="harmanbar_iot_Everyday">Everyday</string>
    <string name="harmanbar_iot_Sunday">Sunday</string>
    <string name="harmanbar_iot_Monday">Monday</string>
    <string name="harmanbar_iot_Tuesday">Tuesday</string>
    <string name="harmanbar_iot_Wednesday">Wednesday</string>
    <string name="harmanbar_iot_Thursday">Thursday</string>
    <string name="harmanbar_iot_Friday">Friday</string>
    <string name="harmanbar_iot_Saturday">Saturday</string>
    <string name="harmanbar_iot_Change_password">Change password</string>
    <string name="harmanbar_iot_How_to_enable_skill">How to enable skill</string>
    <string name="harmanbar_iot_Film">Film</string>
    <string name="harmanbar_iot_Game">Game</string>
    <string name="harmanbar_iot_Sleep">Sleep</string>
    <string name="harmanbar_iot_Yoga">Yoga</string>
    <string name="harmanbar_iot_Read">Read</string>
    <string name="harmanbar_iot_Study">Study</string>
    <string name="harmanbar_iot_Recommended">Recommended</string>
    <string name="harmanbar_iot_Color_Temperature">Color Temperature</string>
    <string name="harmanbar_iot__Please_set_a_new_password_for_this_account__n____">&#160;Please set a new password for this account:\n %s&#160;</string>
    <string name="harmanbar_iot_Your_current_login_account_is__n_n____If_you_want_to_switch_accounts__n_nplease_log_out_first_">Your current login account is:\n\n%s. If you want to switch accounts,\n\nplease log out first.</string>
    <string name="harmanbar_iot_Need_to_re_Binding_device_after_renaming_">Need to re-Binding device after renaming.</string>

    <!--primemusic-->
    <string name="harmanbar_primemusic_Block_Explicit_Songs">Block Explicit Songs</string>
    <string name="harmanbar_primemusic_Unblock_Explicit_Songs">Unblock Explicit Songs</string>
    <string name="harmanbar_primemusic_Songs_with_explicit_language_will_be_blocked_on_this_device_">Songs with explicit language will be blocked on this device.</string>
    <string name="harmanbar_primemusic_Songs_with_explicit_language_are_blocked_on_this_device__Would_you_like_to_unblock_">Songs with explicit language are blocked on this device. Would you like to unblock?</string>
    <string name="harmanbar_primemusic_Block">Block</string>
    <string name="harmanbar_primemusic_Unblock">Unblock</string>
    <string name="harmanbar_primemusic__nExplicit_songs__n_are_blocked_n">\nExplicit songs \n are blocked\n</string>
    <string name="harmanbar_primemusic__nExplicit_songs__n_are_unblocked_n">\nExplicit songs \n are unblocked\n</string>
    <string name="harmanbar_primemusic_Loading_error__Please_try_again_later_">Loading error. Please try again later.</string>
    <string name="harmanbar_primemusic_We_re_sorry__this_content_is_no_longer_available">We\'re sorry, this content is no longer available</string>
    <string name="harmanbar_primemusic_Available_to_Prime_and_Amazon_Music_Unlimited_Subscribers">Available to Prime and Amazon Music Unlimited Subscribers</string>
    <string name="harmanbar_primemusic_Unexpected_error">Unexpected error</string>
    <string name="harmanbar_primemusic_Sorry__there_must_be_something_wrong__Please_sign_in_again_">Sorry, there must be something wrong. Please sign in again.</string>
    <string name="harmanbar_primemusic_Please_refresh_the_page_to_try_again">Please refresh the page to try again.</string>
    <string name="harmanbar_primemusic_Fail">Fail</string>
    <string name="harmanbar_primemusic_NO_Result">NO Result</string>
    <string name="harmanbar_primemusic_Logging_out___">Logging out...</string>
    <string name="harmanbar_primemusic_See_All">See All</string>
    <string name="harmanbar_primemusic_My_Music">My Music</string>
    <string name="harmanbar_primemusic_Settings">Settings</string>
    <string name="harmanbar_primemusic_Cancel">Cancel</string>
    <string name="harmanbar_primemusic_Dismiss">Dismiss</string>
    <string name="harmanbar_primemusic_Sign_Out">Sign Out</string>
    <string name="harmanbar_primemusic_The_connection_has_timed_out_">The connection has timed out.</string>
    <string name="harmanbar_primemusic_See_More">See More</string>
    <string name="harmanbar_primemusic_Search">Search</string>
    <string name="harmanbar_primemusic_Search_Amazon_Music">Search Amazon Music</string>
    <string name="harmanbar_primemusic_Amazon_Login">Amazon Login</string>
    <string name="harmanbar_primemusic_clear_recent_searches">clear recent searches</string>
    <string name="harmanbar_primemusic_Loading____">Loading....</string>
    <string name="harmanbar_primemusic_Play_All">Play All</string>
    <string name="harmanbar_primemusic_Shuffle_All">Shuffle All</string>
    <string name="harmanbar_primemusic_Start_your_30_day_free_trial">Start your 30-day free trial</string>
    <string name="harmanbar_primemusic_Are_you_sure_you_want_to_quit_">Are you sure you want to quit?</string>
    <string name="harmanbar_primemusic_logout">Logout</string>
    <string name="harmanbar_primemusic_Refresh">Refresh</string>
    <string name="harmanbar_primemusic_We__ll_play_more_songs_like_this">We\'ll play more songs like this</string>
    <string name="harmanbar_primemusic_We_won__t_play_that_song_again_on_this_station">We won\'t play that song again on this station</string>
    <string name="harmanbar_primemusic_Explicit_Songs">Explicit Songs</string>
    <string name="harmanbar_primemusic_Songs_with_explicit_language_are_blocked_on_this_device_">Songs with explicit language are blocked on this device.</string>
    <string name="harmanbar_primemusic_OK">OK</string>
    <string name="harmanbar_primemusic_Play">Play</string>
    <string name="harmanbar_primemusic_More">More</string>
    <string name="harmanbar_primemusic_Amazon_Music">Amazon Music</string>

    <!--newtuneIn-->
    <string name="harmanbar_newtuneIn_logout">Logout</string>
    <string name="harmanbar_newtuneIn_login">Login</string>
    <string name="harmanbar_newtuneIn_Premium">Premium</string>
    <string name="harmanbar_newtuneIn_Search">Search</string>
    <string name="harmanbar_newtuneIn_Search_for_stations__podcasts__or_events">Search for stations, podcasts, or events</string>
    <string name="harmanbar_newtuneIn_No_Results_found_for">No Results found for</string>
    <string name="harmanbar_newtuneIn_Log_out_fail__Please_try_again_">Log out failed, Please try again.</string>
    <string name="harmanbar_newtuneIn_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_newtuneIn_Time_out">Time out</string>
    <string name="harmanbar_newtuneIn_Prompt">Prompt</string>
    <string name="harmanbar_newtuneIn_TuneIn_account_has_been_changed">TuneIn account has been changed</string>
    <string name="harmanbar_newtuneIn_TuneIn_account_has_logout">TuneIn account has logged out</string>
    <string name="harmanbar_newtuneIn_Favorites">Favorites</string>
    <string name="harmanbar_newtuneIn_name">tuneIn</string>
    <string name="harmanbar_newtuneIn_Logout_Fail">Logout Fail</string>
    <string name="harmanbar_newtuneIn_Time">Time</string>
    <string name="harmanbar_newtuneIn_loading___">loading...</string>
    <string name="harmanbar_newtuneIn_Login_failed">Login failed</string>
    <string name="harmanbar_newtuneIn_Login_successful">Login successful</string>
    <string name="harmanbar_newtuneIn_Cancel">Cancel</string>
    <string name="harmanbar_newtuneIn_Location">Location</string>
    <string name="harmanbar_newtuneIn_more">more</string>
    <string name="harmanbar_newtuneIn_This_content_cannot_be_played_">This content cannot be played.</string>
    <string name="harmanbar_newtuneIn_This_show_will_be_available_later__Please_come_back_then_">This show will be available later. Please come back then.</string>
    <string name="harmanbar_newtuneIn_Favorite">Favorite</string>
    <string name="harmanbar_newtuneIn_Less">Less</string>
    <string name="harmanbar_newtuneIn_No_results">No results</string>
    <string name="harmanbar_newtuneIn_Unfavorite">Unfavorite</string>
    <string name="harmanbar_newtuneIn_Favorites_Success">Favorites Success</string>
    <string name="harmanbar_newtuneIn_Favorites_Fail">Favorites Fail</string>
    <string name="harmanbar_newtuneIn_Removed_Favorite_Fail">Removed Favorite Fail</string>
    <string name="harmanbar_newtuneIn_Removed_Favorite_Successfully">Removed Favorite Successfully</string>
    <string name="harmanbar_newtuneIn_Favorites_is_empty">Favorites is empty</string>
    <string name="harmanbar_newtuneIn_This_content_can__t_play">This content can\'t play</string>
    <string name="harmanbar_newtuneIn_Premium_opt_in_failed">Premium opt-in failed</string>
    <string name="harmanbar_newtuneIn_Browse">Browse</string>
    <string name="harmanbar_newtuneIn_Home">Home</string>
    <string name="harmanbar_newtuneIn_Setting">Setting</string>
    <string name="harmanbar_newtuneIn_Now_can__t_play_please_choose_others">Now can\'t play，please choose others</string>
    <string name="harmanbar_newtuneIn_Fail">Fail</string>
    <string name="harmanbar_newtuneIn_OK">OK</string>

    <!--newadddevice-->
    <string name="harmanbar_newadddevice_Make_sure_your_smartphone_is_connected_to_2_4GHz_Wi_Fi_">Make sure your smartphone is connected to 2.4GHz Wi-Fi.</string>
    <string name="harmanbar_newadddevice_Make_sure_your____is_connected_to_2_4GHz_Wi_Fi__This____does_not_support_5GHz_network__">Make sure your %1$s is connected to 2.4GHz Wi-Fi. This %2$s does not support 5GHz network.&#160;</string>
    <string name="harmanbar_newadddevice_Please_turn_on_your____">Please turn on your %s&#160;</string>
    <string name="harmanbar_newadddevice_Searching_for______">Searching for %s...</string>
    <string name="harmanbar_newadddevice_Check_the_indicator_on_your____">Check the indicator on your %s.</string>
    <string name="harmanbar_newadddevice_Cound_not_find_your_device__">Cound not find your device &gt;</string>
    <string name="harmanbar_newadddevice_Searching_for_your______">Searching for your %s...</string>
    <string name="harmanbar_newadddevice_Wi_Fi_name_">Wi-Fi name:</string>
    <string name="harmanbar_newadddevice_Your_device_has_already_connected_to_Wi_Fi_network__but_app_couldn_t_find_your_device_">Your device has already connected to Wi-Fi network, but app couldn’t find your device.</string>
    <string name="harmanbar_newadddevice_Enter_password_">Enter password:</string>
    <string name="harmanbar_newadddevice_No____found_">No %s found.</string>
    <string name="harmanbar_newadddevice_If_Wi_Fi_indicator_is_not_blinking__cancel_setup_and_start_setup_from_the_beginning_">If Wi-Fi indicator is not blinking, cancel setup and start setup from the beginning.</string>
    <string name="harmanbar_newadddevice_Hold_the_Wi_Fi_button_on_your____until_the_indicator_starts_blinking_">Hold the Wi-Fi button on your %s until the indicator starts blinking.</string>
    <string name="harmanbar_newadddevice_We_found_new___">We found new %s</string>
    <string name="harmanbar_newadddevice_Set_up_Wi_Fi_for____">Set up Wi-Fi for %s.</string>
    <string name="harmanbar_newadddevice_Change_Wi_Fi">Change Wi-Fi</string>
    <string name="harmanbar_newadddevice_Checking_the_latest_firmware_">Checking the latest firmware…</string>
    <string name="harmanbar_newadddevice_Allow_this_app_to_access">Allow this app to access</string>
    <string name="harmanbar_newadddevice_Location__This_is_for_setup_only__We_won_t_collect_your_actual_location_using_GPS_">Location: This is for setup only. We won\'t collect your actual location using GPS.</string>
    <string name="harmanbar_newadddevice_Bluebooth__This_is_to_search_for_your____for_setup_only_">Bluebooth: This is to search for your %s for setup only.</string>
    <string name="harmanbar_newadddevice_Storage__This_is_to_playback_music_files_stored_in_your_smartphone__">Storage: This is to playback music files stored in your smartphone.&#160;</string>
    <string name="harmanbar_newadddevice_Allow_location_access">Allow location access</string>
    <string name="harmanbar_newadddevice_Location_access_is_needed_to_setup_your_____We_won_t_collect_your_actual_location_using_GPS_">Location access is needed to setup your %s. We won\'t collect your actual location using GPS.</string>
    <string name="harmanbar_newadddevice_Allow_Bluetooth_access">Allow Bluetooth access</string>
    <string name="harmanbar_newadddevice_Bluetooth_access_is_needed_to_search_for_your____for_setup_">Bluetooth access is needed to search for your %s for setup.</string>
    <string name="harmanbar_newadddevice_Your____is_now_up_to_date_">Your %s is now up to date.</string>
    <string name="harmanbar_newadddevice_Estimated_time_of_completion__">Estimated time of completion:&#160;</string>
    <string name="harmanbar_newadddevice_Don_t_unplug_the_power_supply_during_the_update_">Don\'t unplug the power supply during the update.</string>
    <string name="harmanbar_newadddevice_Unknown_error">Unknown error</string>
    <string name="harmanbar_newadddevice_Update_failed">Update failed</string>
    <string name="harmanbar_newadddevice_Try_later">Try later</string>
    <string name="harmanbar_newadddevice____is_restarting_">%s is restarting…</string>
    <string name="harmanbar_newadddevice_indicator_is_blinking_">indicator is blinking&#160;</string>
    <string name="harmanbar_newadddevice_indicator_is_not_on">indicator is not on</string>
    <string name="harmanbar_newadddevice_Turn_on_Bluetooth_to_allow_your____to_establish_connection_">Turn on Bluetooth to allow your %s to establish connection.</string>
    <string name="harmanbar_newadddevice_Setup_this_device">Setup this device</string>
    <string name="harmanbar_newadddevice_It_may_take_____">It may take: %s&#160;</string>
    <string name="harmanbar_newadddevice_20_s">20 s</string>
    <string name="harmanbar_newadddevice_You_can_retry_updating_after_restarting_the____">You can retry updating after restarting the %s.</string>
    <string name="harmanbar_newadddevice_An_unknown_error_has_occurred__Restart_the____and_try_again_">An unknown error has occurred. Restart the %s and try again.</string>
    <string name="harmanbar_newadddevice____successfully_restarted__Retry">%s successfully restarted: Retry</string>
    <string name="harmanbar_newadddevice_Device_setup">Device setup</string>
    <string name="harmanbar_newadddevice_Choose_language_for_Alexa">Choose language for Alexa</string>
    <string name="harmanbar_newadddevice_You_can_change_the_language_later_in___Alexa_settings__">You can change the language later in \"Alexa settings\"</string>
    <string name="harmanbar_newadddevice_Please_wait_while_the_connection_is_completed__This_might_take_a_minute_">Please wait while the connection is completed. This might take a minute.</string>
    <string name="harmanbar_newadddevice_Change_Wi_Fi_on_phone">Change Wi-Fi on phone</string>
    <string name="harmanbar_newadddevice_Connection_failed">Connection failed</string>
    <string name="harmanbar_newadddevice_Alternative_setup">Alternative setup</string>
    <string name="harmanbar_newadddevice_Other_Network">Other Network</string>
    <string name="harmanbar_newadddevice_None">None</string>
    <string name="harmanbar_newadddevice_WEP">WEP</string>
    <string name="harmanbar_newadddevice_WPA">WPA</string>
    <string name="harmanbar_newadddevice_WPA_2">WPA 2</string>
    <string name="harmanbar_newadddevice_Network_name_">Network name:</string>
    <string name="harmanbar_newadddevice_Security_">Security:</string>
    <string name="harmanbar_newadddevice_Username_">Username:</string>
    <string name="harmanbar_newadddevice_Congratulations_">Congratulations!</string>
    <string name="harmanbar_newadddevice_Update_complete">Update complete</string>
    <string name="harmanbar_newadddevice_Your_device_is_updated_to_firmware_version_____Ready_to_use_">Your device is updated to firmware version %s. Ready to use.</string>
    <string name="harmanbar_newadddevice_Stage">Stage</string>
    <string name="harmanbar_newadddevice_Search_timeout">Search timeout</string>
    <string name="harmanbar_newadddevice_Do_you_want_connect_to_______">Do you want connect to \"%s\"</string>
    <string name="harmanbar_newadddevice_You_ve_found_the_device">You\'ve found the device</string>
    <string name="harmanbar_newadddevice_My_name">My name</string>
    <string name="harmanbar_newadddevice_Recommended_name_">Recommended name:</string>
    <string name="harmanbar_newadddevice_App_can_t_connect_to_speaker">App can\'t connect to speaker</string>
    <string name="harmanbar_newadddevice_Let_s_try_the_follows_to_get_it_back_">Let\'s try the follows to get it back:</string>
    <string name="harmanbar_newadddevice_We_find_your_router_is_BT_Smart_Hub_2__please_try_to_turn_off_your_router_s_5GHz_band_and_see_if_App">We find your router is BT Smart Hub 2, please try to turn off your router\'s 5GHz band and see if App can find your speaker.</string>
    <string name="harmanbar_newadddevice_Learn_more_">Learn more&gt;</string>
    <string name="harmanbar_newadddevice_1__Turn_your_WiFi_off_and_on__Check_if_the_App_finds_the_speaker__If_not__go_to_step_2_">1. Turn your WiFi off and on. Check if the App finds the speaker. If not, go to step 2.</string>
    <string name="harmanbar_newadddevice_2__Turn_off_your_speaker__unplug_the_power__and_turn_it_on_again__wait_for_30_seconds_">2. Turn off your speaker (unplug the power) and turn it on again, wait for 30 seconds.</string>
    <string name="harmanbar_newadddevice_3__Launch_the_App_and_see_if_it_finds_the_speaker_">3. Launch the App and see if it finds the speaker.</string>
    <string name="harmanbar_newadddevice_The_issue_is_caused_by_the_latest_BT_smart_hub2_firmware_that_they_aren_t_able_to_bridge_the_2_4_GHz_and_5_GHz_communication__The____speaker_is_connected_to_the_2_4_GHz_network_while_your_phone_is_connected_to_5_GHz_network__In_the_properly_configured_router_AP__it_should_be_able_to_communicate_with_each_other__To_work_around_this__could_you_please_disable_the_5G_band_in_your_router_">The issue is caused by the latest BT smart hub2 firmware that they aren\'t able to bridge the 2.4 GHz and 5 GHz communication. The %s speaker is connected to the 2.4 GHz network while your phone is connected to 5 GHz network. In the properly configured router/AP, it should be able to communicate with each other. To work around this, could you please disable the 5G band in your router?</string>
    <string name="harmanbar_newadddevice_To_disable_5GHz_on_BT_Smart_Hub_2__it_looks_like_that_s_what_you_ve_got__please_do_the_following_">To disable 5GHz on BT Smart Hub 2 (it looks like that\'s what you\'ve got) please do the following:</string>
    <string name="harmanbar_newadddevice_1_Open_http___192_168_1_254__this_is_the_configuration_page_for_your_router__You_can_open_this_on_an">1.Open http://192.168.1.254  this is the configuration page for your router. You can open this on any browser, but I recommend using a screen bigger than a phone\'s.</string>
    <string name="harmanbar_newadddevice_2_When_you_see_the_purple_boxes__click_on_Wireless_">2.When you see the purple boxes, click on Wireless.</string>
    <string name="harmanbar_newadddevice_3_At_some_point__perhaps_not_right_now__you_ll_be_asked_for_a_password_to_log_in__This_can_be_found_">3.At some point (perhaps not right now) you\'ll be asked for a password to log in. This can be found on a sticker on the router, and is called the Admin or Settings password.</string>
    <string name="harmanbar_newadddevice_4_You_ll_see_a_page_with_details_for_2_4_GHz_and_for_5_GHz__Turn_off_5GHz_only___there_will_be_an_on">4.You\'ll see a page with details for 2.4 GHz and for 5 GHz. Turn off 5GHz only - there will be an on/off switch for it.</string>
    <string name="harmanbar_newadddevice_5_Save_your_changes_">5.Save your changes.</string>
    <string name="harmanbar_newadddevice_Test_for_a_day_or_two_until_you_re_happy_it_s_better_or_have_determined_that_it_hasn_t_helped__If_it">Test for a day or two until you\'re happy it\'s better or have determined that it hasn\'t helped. If it hasn\'t helped, turn 5GHz back on again.</string>
    <string name="harmanbar_newadddevice_If_possible__you_could_also_get_the_third_party_dual_band_router_instead_of_this_one_without_waiting">If possible, you could also get the third party dual band router instead of this one without waiting for BT figure this out.</string>
    <string name="harmanbar_newadddevice_Connecting____to______">Connecting %1$s to %2$s...</string>
    <string name="harmanbar_newadddevice_Set_up_Wi_Fi_for_Device">Set up Wi-Fi for Device</string>
    <string name="harmanbar_newadddevice_We_found_new_Device">We found new Device</string>
    <string name="harmanbar_newadddevice_Please_turn_on_your_device">Please turn on your device</string>
    <string name="harmanbar_newadddevice_Check_the_indicator_on_your_device">Check the indicator on your device</string>
    <string name="harmanbar_newadddevice_Hold_the_Wi_Fi_button_on_your_device_until_the_indicator_starts_blinking_">Hold the Wi-Fi button on your device until the indicator starts blinking.</string>
    <string name="harmanbar_newadddevice_All_your_content_in_one_App">All your content in one App</string>
    <string name="harmanbar_newadddevice_Multi_room_audio_or_Stereo_pairing">Multi-room audio or Stereo pairing</string>
    <string name="harmanbar_newadddevice_Work_with_voice">Work with voice</string>
    <string name="harmanbar_newadddevice_Automate_your_listening_experience">Automate your listening experience</string>
    <string name="harmanbar_newadddevice_Let_s_start__">Let\'s start &gt;</string>
    <string name="harmanbar_newadddevice_Local_Network__This_is_used_to_search_for_your_device_and_connect_with_it_">Local Network: This is used to search for your device and connect with it.</string>
    <string name="harmanbar_newadddevice_This_will_allow_app_to_find_and_connect__to_bluetooth_device_">This will allow app to find and connect  to bluetooth device.</string>
    <string name="harmanbar_newadddevice_App_needs_to_find_your_Wi_Fi_network_as_location_information__This_app_doesn_t_collect_your_actual_l">App needs to find your Wi-Fi network as location information. This app doesn\'t collect your actual location using GPS or Wi-Fi based positioning technologies.</string>
    <string name="harmanbar_newadddevice_This_app_will_be_able_to_discover_and_connect_to_devices_on_the_networks_you_use_">This app will be able to discover and connect to devices on the networks you use.</string>
    <string name="harmanbar_newadddevice_We_need_to_access_your_local_music_to_play_them_on_speaker">We need to access your local music to play them on device</string>
    <string name="harmanbar_newadddevice____has_been_connected_to____">%1$s has been connected to %2$s.</string>
    <string name="harmanbar_newadddevice_The_Wi_Fi_of____should_be_the_same_as_your_mobile_device_to_work__Please_change_the_Wi_Fi_of_your_mobile_device_to____">The Wi-Fi of %1$s should be the same as your mobile device to work. Please change the Wi-Fi of your mobile device to %2$s.</string>
    <string name="harmanbar_newadddevice_Found_a_new_device_nearby__please_set_up_it_">Found a new device nearby</string>
    <string name="harmanbar_newadddevice_Preparing">Preparing</string>
    <string name="harmanbar_newadddevice_Please_make_sure_your_phone_is_connect_to___">Please make sure your mobile phone is connect to network %s</string>
    <string name="harmanbar_newadddevice_Your_device_is_now_connected_to_your_5_GHz_wireless_network__This_will_facilitate_the_device_discove">Your device is now connected to your 5 GHz wireless network. This will facilitate the device discovery and audio playback.</string>
    <string name="harmanbar_newadddevice_Couldn_t_find_device">Couldn\'t find device</string>

    <!--NewPlayView-->
    <string name="harmanbar_NewPlayView_Alarm">Alarm</string>
    <string name="harmanbar_NewPlayView_Sleep_Timer">Sleep Timer</string>
    <string name="harmanbar_NewPlayView_mins">mins</string>
    <string name="harmanbar_NewPlayView_min">min</string>
    <string name="harmanbar_NewPlayView_Custom">Custom</string>
    <string name="harmanbar_NewPlayView_hour">hour</string>
    <string name="harmanbar_NewPlayView_hours">hours</string>
    <string name="harmanbar_NewPlayView_Stop_audio_in">Stop Audio in</string>
    <string name="harmanbar_NewPlayView_Remove_from_favorites">Remove from favorites</string>
    <string name="harmanbar_NewPlayView_Add_to_favorites">Add to favorites</string>
    <string name="harmanbar_NewPlayView_Add_to_playlist">Add to playlist</string>
    <string name="harmanbar_NewPlayView_Add_to____favorites">Add to %s favorites</string>
    <string name="harmanbar_NewPlayView_Remove_from____favorites">Remove from %s favorites</string>
    <string name="harmanbar_NewPlayView_Remove_from_my_music">Remove from my music</string>
    <string name="harmanbar_NewPlayView_Add_to_my_music">Add to my music</string>
    <string name="harmanbar_NewPlayView_View_album">View album</string>
    <string name="harmanbar_NewPlayView_Play_Track_Radio">Play Track Radio</string>
    <string name="harmanbar_NewPlayView_View_artist">View artist</string>
    <string name="harmanbar_NewPlayView_The_Song_added_to____favorites_successfully">The Song added to %s favorites successfully</string>
    <string name="harmanbar_NewPlayView_The_Song_added_to____favorites_failed">The Song added to %s favorites failed</string>
    <string name="harmanbar_NewPlayView_The_Song_removed_from____favorites_successfully">The Song removed from %s favorites successfully</string>
    <string name="harmanbar_NewPlayView_The_Song_removed_from____favorites_failed">The Song removed from %s favorites failed</string>
    <string name="harmanbar_NewPlayView_Added_successfully_on___">Added successfully on %s</string>
    <string name="harmanbar_NewPlayView_Added_failed_on___">Added failed on %s</string>
    <string name="harmanbar_NewPlayView_Removed_successfully_on___">Removed successfully on %s</string>
    <string name="harmanbar_NewPlayView_Removed_failed_on___">Removed failed on %s</string>
    <string name="harmanbar_NewPlayView_Swap_audio_to_a_selected_room">Swap audio to a selected room</string>
    <string name="harmanbar_NewPlayView_Remove_from_playlist">Remove from playlist</string>
    <string name="harmanbar_NewPlayView_Add_to_queue">Add to queue</string>
    <string name="harmanbar_NewPlayView_No_music_selected">No music selected</string>
    <string name="harmanbar_NewPlayView_Queue">Queue</string>
    <string name="harmanbar_NewPlayView_At_least_two_speakers_are_required_to_use_swapping_audio__you_can_swap_the_currently_playing_content">At least two speakers are required to use swapping audio, you can swap the currently playing content to your desired device.</string>

    <!--NewContent-->
    <string name="harmanbar_NewContent_Stereo_Analog_Audio">Stereo out</string>
    <string name="harmanbar_NewContent_My_library">My library</string>
    <string name="harmanbar_NewContent_Music_service">Music services</string>
    <string name="harmanbar_NewContent_Select_source_on_device">Select source on device</string>
    <string name="harmanbar_NewContent_WiFi">Wi-Fi</string>
    <string name="harmanbar_NewContent_Manage_Music_services">Manage music services</string>
    <string name="harmanbar_NewContent_Preset_Content">Preset Content</string>
    <string name="harmanbar_NewContent_Recently_Played">Recently Played</string>

    <!--NewDeviceList-->
    <string name="harmanbar_NewDeviceList_No_Music_Select">No Music Selected</string>
    <string name="harmanbar_NewDeviceList_Group_volume">Group volume</string>
    <string name="harmanbar_NewDeviceList_To_play_synchronized">To play synchronized</string>
    <string name="harmanbar_NewDeviceList_Browse">Browse</string>
    <string name="harmanbar_NewDeviceList_Device">Device</string>
    <string name="harmanbar_NewDeviceList_Settings">Settings</string>
    <string name="harmanbar_NewDeviceList_Hindi__India_">Hindi (India)</string>
    <string name="harmanbar_NewDeviceList_Internet_Radio">Internet Radio</string>
    <string name="harmanbar_NewDeviceList_Group">Group</string>
    <string name="harmanbar_NewDeviceList_Ungroup">Ungroup</string>
    <string name="harmanbar_NewDeviceList_Wi_Fi_status">Wi-Fi status</string>
    <string name="harmanbar_NewDeviceList_Status_light">Status light</string>
    <string name="harmanbar_NewDeviceList_Preferred_DNS_server">Preferred DNS server</string>
    <string name="harmanbar_NewDeviceList_5_GHz_only">5 GHz only</string>
    <string name="harmanbar_NewDeviceList_Band">Band</string>
    <string name="harmanbar_NewDeviceList_Wi_Fi_roaming">Wi-Fi roaming</string>
    <string name="harmanbar_NewDeviceList_TX_rate">TX rate</string>
    <string name="harmanbar_NewDeviceList_SNR">SNR</string>
    <string name="harmanbar_NewDeviceList_Please_make_sure_your_Wi_Fi_router_supports_5_GHz_band__n5_GHz_Only_disables_2_4_GHz_band_on_your_de">Please make sure your Wi-Fi router supports 5 GHz band.\n5 GHz Only disables 2.4 GHz band on your device and it connects to your router with 5 GHz band. It helps device discovery and more smooth audio playback.</string>
    <string name="harmanbar_NewDeviceList_When_5_GHz_Only__your_device_will_disconnect_from_your_Wi_Fi_router_and_reconnect_it__it_will_take_a">When 5 GHz Only, your device will disconnect from your Wi-Fi router and reconnect it, it will take about 2 minutes.</string>

    <!--NewPreset-->
    <string name="harmanbar_NewPreset_Preset__XXXX__playlist_for_easy_access">Preset [XXXX] playlist for easy access</string>
    <string name="harmanbar_NewPreset_Access_your_favorite_music_from_the_relevant_buttons_on_the_remote_control_or_the_speaker">Access your favorite music from the relevant buttons on the remote control or the speaker</string>
    <string name="harmanbar_NewPreset_Favorite">Favorite</string>
    <string name="harmanbar_NewPreset_Empty">Empty</string>

    <!--wac-->
    <string name="harmanbar_wac_Can_t_find_your_device">Can\'t find your device</string>
    <string name="harmanbar_wac_1__Make_sure_your_device_has_already_been_restored_factory_settings_">1. Make sure your device has already been restored factory settings.</string>
    <string name="harmanbar_wac_restored_factory_settings">restored factory settings</string>
    <string name="harmanbar_wac_2__Press___Retry___button_to_try_again_">2. Press \"Retry\" button to try again.</string>
    <string name="harmanbar_wac_3__If_your_device_setup_still_fails__please_choose_alternate_way_to_setup_">3. If your device setup still fails, please choose alternate way to setup.</string>

    <!--jbl-->
    <string name="harmanbar_jbl_PLEASE_TRY">PLEASE TRY</string>
    <string name="harmanbar_jbl_AirPlay_Is_Ready">AirPlay Is Ready</string>
    <string name="harmanbar_jbl_GOT_IT">GOT IT</string>
    <string name="harmanbar_jbl_ENABLE_MORE_SERVICE">ENABLE MORE SERVICES</string>
    <string name="harmanbar_jbl_CALIBRATE">CALIBRATE</string>
    <string name="harmanbar_jbl_LATER">LATER</string>
    <string name="harmanbar_jbl_ALLOW">ALLOW</string>
    <string name="harmanbar_jbl_ENABLE_ALEXA">ENABLE ALEXA</string>
    <string name="harmanbar_jbl_Available_for_iOS_devices">Available for iOS devices</string>
    <string name="harmanbar_jbl_Enabled_devices">Enabled devices</string>
    <string name="harmanbar_jbl_Moment">Moment</string>
    <string name="harmanbar_jbl_Equalizer">Equalizer</string>
    <string name="harmanbar_jbl_Wi_Fi">Wi-Fi</string>
    <string name="harmanbar_jbl_Dolby_Audio">Dolby Audio</string>
    <string name="harmanbar_jbl_Calibration">Calibration</string>
    <string name="harmanbar_jbl_No_content">No content</string>
    <string name="harmanbar_jbl_Play_______">Play \"%s\"</string>
    <string name="harmanbar_jbl_Product_Model">Product Model</string>
    <string name="harmanbar_jbl_Build_date">Build Date</string>
    <string name="harmanbar_jbl_Software">Software</string>
    <string name="harmanbar_jbl_New_updat">New version available!</string>
    <string name="harmanbar_jbl_General">General</string>
    <string name="harmanbar_jbl_Quick_Start_Guide">Quick Start Guide</string>
    <string name="harmanbar_jbl_Dolby_Atmos">Dolby Atmos</string>
    <string name="harmanbar_jbl_Check_Wi_Fi">Check Wi-Fi</string>
    <string name="harmanbar_jbl_START">START</string>
    <string name="harmanbar_jbl_Tuning_finished">Tuning finished</string>
    <string name="harmanbar_jbl_Cancel_Calibration__">Cancel Calibration ?</string>
    <string name="harmanbar_jbl_STAY">STAY</string>
    <string name="harmanbar_jbl_Immersive_Audio_Demo">Immersive Audio Demo</string>
    <string name="harmanbar_jbl_Inactive">Inactive</string>
    <string name="harmanbar_jbl_With">With</string>
    <string name="harmanbar_jbl_Volume">Volume</string>
    <string name="harmanbar_jbl_Accept">Accept</string>
    <string name="harmanbar_jbl_ADD_PRODUCT_LATER">ADD PRODUCT LATER</string>
    <string name="harmanbar_jbl_GET_HELP">GET HELP</string>
    <string name="harmanbar_jbl_Turn_On_Bluetooth">Turn On Bluetooth</string>
    <string name="harmanbar_jbl_CONTINUE">CONTINUE</string>
    <string name="harmanbar_jbl_OTHER_NETWORK">OTHER NETWORK</string>
    <string name="harmanbar_jbl_Security">Security</string>
    <string name="harmanbar_jbl_TURN_ON_BLUETOOTH">TURN ON BLUETOOTH</string>
    <string name="harmanbar_jbl_None">None</string>
    <string name="harmanbar_jbl_WEP">WEP</string>
    <string name="harmanbar_jbl_WPA">WPA</string>
    <string name="harmanbar_jbl_WPA_2">WPA 2</string>
    <string name="harmanbar_jbl_Up_to_date">Up To Date</string>
    <string name="harmanbar_jbl_You_are_all_set__nCurrent_version___">You are all set.\nCurrent version %s</string>
    <string name="harmanbar_jbl_UPDATE_LATER">UPDATE LATER</string>
    <string name="harmanbar_jbl___JBL_Soundbar_xxx__">\"JBL Soundbar_xxx\"</string>
    <string name="harmanbar_jbl_TROUBLE_SHOOT">TROUBLESHOOT</string>
    <string name="harmanbar_jbl_CHANGE_NETWORK">CHANGE NETWORK</string>
    <string name="harmanbar_jbl_Language">Language</string>
    <string name="harmanbar_jbl_Feedback">Feedback</string>
    <string name="harmanbar_jbl_ACCEPT">ACCEPT</string>
    <string name="harmanbar_jbl_NOT_ACCEPT">NOT ACCEPT</string>
    <string name="harmanbar_jbl_Version">Version</string>
    <string name="harmanbar_jbl_Soundbar">Soundbar</string>
    <string name="harmanbar_jbl_REMOVE">REMOVE</string>
    <string name="harmanbar_jbl_CANCEL">CANCEL</string>
    <string name="harmanbar_jbl_OK">OK</string>
    <string name="harmanbar_jbl_Minutes">Minutes</string>
    <string name="harmanbar_jbl_SELECT">SELECT</string>
    <string name="harmanbar_jbl_Never">Never</string>
    <string name="harmanbar_jbl_Exit_Setup__">Exit Setup ?</string>
    <string name="harmanbar_jbl_EXIT">EXIT</string>
    <string name="harmanbar_jbl_NOT_ALLOW">NOT ALLOW</string>
    <string name="harmanbar_jbl_ENABLE_SERVICE">ENABLE SERVICE</string>
    <string name="harmanbar_jbl_Chromecast_Built_In_Is_Ready">Google Cast Is Ready</string>
    <string name="harmanbar_jbl_Google_Home">Google Home</string>
    <string name="harmanbar_jbl_TURN_ON">TURN ON</string>
    <string name="harmanbar_jbl_RESTORE">RESTORE</string>
    <string name="harmanbar_jbl_REMOVE_THIS_PRODUCT__">REMOVE THIS PRODUCT</string>
    <string name="harmanbar_jbl_Favourite">Favourite</string>
    <string name="harmanbar_jbl_Enabled_music_service">Enabled music service</string>
    <string name="harmanbar_jbl_Available_music_service">Available music service</string>
    <string name="harmanbar_jbl_Bluetooth">Bluetooth</string>
    <string name="harmanbar_jbl_Location">Location</string>
    <string name="harmanbar_jbl_Calibration_Unavailable">Calibration Unavailable</string>
    <string name="harmanbar_jbl_KEEP">KEEP</string>
    <string name="harmanbar_jbl_Fail">Fail</string>
    <string name="harmanbar_jbl_Amazon_Alexa">Amazon Alexa</string>
    <string name="harmanbar_jbl_Retry">Retry</string>
    <string name="harmanbar_jbl_Thanks_for_your_feedback_">Thanks for your feedback!</string>
    <string name="harmanbar_jbl_SEND">SEND</string>
    <string name="harmanbar_jbl_Subject">Subject</string>
    <string name="harmanbar_jbl_Email">Email</string>
    <string name="harmanbar_jbl_CONFIRM">CONFIRM</string>
    <string name="harmanbar_jbl_AirPlay">AirPlay</string>
    <string name="harmanbar_jbl_Allow">Allow</string>
    <string name="harmanbar_jbl_Delete">Delete</string>
    <string name="harmanbar_jbl_RESET">RESET</string>
    <string name="harmanbar_jbl_Please_reconnect____to_continue_configure_process">Please reconnect %s to continue configuring process.</string>
    <string name="harmanbar_jbl_Sorry__product_cannot_be_connected_at_the_moment__please_reconnect_">Sorry, product cannot be connected at the moment, please try again.</string>
    <string name="harmanbar_jbl_Password_length_needs_to_be_at_least_5_characters">Password length needs to be at least 5 characters.</string>
    <string name="harmanbar_jbl_RECONNECT_PRODUCT">RECONNECT PRODUCT</string>
    <string name="harmanbar_jbl_Edit_this_name_in_your_iPhone_Home_app">Edit this name using the Home App.</string>
    <string name="harmanbar_jbl_Rebooting____">Rebooting....</string>
    <string name="harmanbar_jbl_Reboot_success">Reboot success</string>
    <string name="harmanbar_jbl_Now_you_can_stream_favorite_music_to_your_JBL_products_through_AirPlay_">Now you can stream music to your JBL products via AirPlay.</string>
    <string name="harmanbar_jbl_Do_you_want_connect_to_______">Do you want connect to \"%s\"</string>
    <string name="harmanbar_jbl_Discover_More">Discover More</string>
    <string name="harmanbar_jbl_If_you_want_to_enable_Google_Chromecast__Amazon_Alexa_or_other_streaming_service__visit_3rd_party_se">If you want to enable Google Chromecast/\nAmazon Alexa or other streaming services, visit the 3rd party service page to learn more.</string>
    <string name="harmanbar_jbl_Let_s_calibrate_your_speakers_to_get_the_best_sound_">Let\'s calibrate your product to get the best sound!</string>
    <string name="harmanbar_jbl_LOGIN_ALEXA">LOG IN ALEXA</string>
    <string name="harmanbar_jbl_Here_Are_Some_Things_You_Can_Say">Here Are Some Things You Can Say</string>
    <string name="harmanbar_jbl_Setup_A_Preferred_Speaker">Setup A Preferred Speaker</string>
    <string name="harmanbar_jbl_Alexa_Preferred_Speaker_are_super_convenient_since_they_don_t_require_any_additional_commands_for_th">Alexa Preferred Speaker gives you the convenience of designating a preferred speaker to become the default music playback device.&#160;</string>
    <string name="harmanbar_jbl_Setup_Alexa_Multi_Room_Music_">Set up Alexa Multi-Room Music</string>
    <string name="harmanbar_jbl_Alexa_Multi_Room_Music_feature_lets_you_play_the_same_music_on_JBL_One_products_and_Alexa_enabled_sp">Alexa Multi-Room Music lets you play the same music on %s products and Alexa enabled speakers.&#160;</string>
    <string name="harmanbar_jbl_To_learn_more_and_access_additional_features__go_to_Amazon_Alexa_App_">To learn more and access additional features, go to Amazon Alexa App.</string>
    <string name="harmanbar_jbl_Open_Amazon_Alexa_App">Open Amazon Alexa App</string>
    <string name="harmanbar_jbl_Manage_streaming_services">Manage streaming services</string>
    <string name="harmanbar_jbl_Available_from_supported_music_App">Available from supported music App</string>
    <string name="harmanbar_jbl_Available_from_Amazon_music_App">Available from Amazon music App</string>
    <string name="harmanbar_jbl_Availale_from_Spotify_native_App">Available from Spotify native App</string>
    <string name="harmanbar_jbl_3rd_party_Service">3rd Party Service</string>
    <string name="harmanbar_jbl_Calibration_Takes_About_3_Minute">Calibration Takes About 3 Minutes</string>
    <string name="harmanbar_jbl_Detach_rear_speakers_">Detach rear speakers.</string>
    <string name="harmanbar_jbl_Place_rear_speakers_in_audience_sitting_position_">Place rear speakers in audience sitting position.</string>
    <string name="harmanbar_jbl_Make_sure_rear_speakers_are_facing_the_soundbar_">Make sure rear speakers are facing the soundbar.</string>
    <string name="harmanbar_jbl_Available_devices">Available devices</string>
    <string name="harmanbar_jbl_If_you_come_across_issue_to_stream_via_AirPlay__please_press___REBOOT___button_below_to_reset__This_">If you’re having problems with streaming via AirPlay, please press \”REBOOT\” button below to reset.  This will take about 1 minute before you can stream music again.&#160;</string>
    <string name="harmanbar_jbl_Step_1_calibration_finished_">Step 1 calibration finished.</string>
    <string name="harmanbar_jbl_REBOOT">REBOOT</string>
    <string name="harmanbar_jbl_Place_rear_speakers_adjacent_to_the_regular_listening_area__">Place rear speakers adjacent to the regular listening area.</string>
    <string name="harmanbar_jbl_Rotate_45_degrees_for_the_best_listening_experience_">Rotate 45 degrees for the best listening experience.</string>
    <string name="harmanbar_jbl_Step_2_calibration_finished_">Step 2 calibration finished.</string>
    <string name="harmanbar_jbl_BASS">BASS</string>
    <string name="harmanbar_jbl_Product_cannot_be_connected_">Product cannot be connected.</string>
    <string name="harmanbar_jbl_No_connection">No connection</string>
    <string name="harmanbar_jbl_LOW">LOW</string>
    <string name="harmanbar_jbl_HIGH">HIGH</string>
    <string name="harmanbar_jbl_Rear_Speakers">Detachable Rear Speakers</string>
    <string name="harmanbar_jbl_Audio_Sync">Audio Sync</string>
    <string name="harmanbar_jbl_Rear_Channel">Rear Channel</string>
    <string name="harmanbar_jbl_Guide__Network_status__Info__Software_version">Guide, Network status, Info, Software version</string>
    <string name="harmanbar_jbl_Attached">Attached</string>
    <string name="harmanbar_jbl_Software_is_up_to_date">Software is up to date</string>
    <string name="harmanbar_jbl_Customer_service">Customer service</string>
    <string name="harmanbar_jbl_Remove_This_Product">Remove This Product ?</string>
    <string name="harmanbar_jbl_Rename_My_Product">Rename My Product</string>
    <string name="harmanbar_jbl_Network_Status">Network Status</string>
    <string name="harmanbar_jbl_Product_Info">Product Info</string>
    <string name="harmanbar_jbl_More_Audio_Settings">More Audio Settings</string>
    <string name="harmanbar_jbl_Customer_Service">Customer Service</string>
    <string name="harmanbar_jbl_Upload_Product_Log">UPLOAD PRODUCT LOG</string>
    <string name="harmanbar_jbl_Enable_Remote_Debug">ENABLE REMOTE DEBUG</string>
    <string name="harmanbar_jbl_How_To_Get_My_Product_Online">How To Get My Product Online</string>
    <string name="harmanbar_jbl_Reset_Product_Network">Reset Product Network</string>
    <string name="harmanbar_jbl_Power_On">Power On</string>
    <string name="harmanbar_jbl_Make_sure_your_mobile_phone_is_connected_to_the_same_Wi_Fi_as_the_product_and_the_Wi_Fi_router_is_wo">Make sure your mobile phone is connected to the same Wi-Fi network as the product and the Wi-Fi router is working as normal.</string>
    <string name="harmanbar_jbl_If_the_product_is_still_not_showing_up__please_reset_the_network_for_your_product_">If the product still cannot be connected, please reset the Wi-Fi network for your product</string>
    <string name="harmanbar_jbl_Make_sure_product_is_in_operation_mode_">Make sure product is in operation mode.</string>
    <string name="harmanbar_jbl_Reset_Network">Reset Network</string>
    <string name="harmanbar_jbl_SAVE">SAVE</string>
    <string name="harmanbar_jbl_Keeping_Quiet">Keeping Quiet</string>
    <string name="harmanbar_jbl_Re_Calibrate_Product_When_Position_Changes">Re-Calibrate Product When Position Changes</string>
    <string name="harmanbar_jbl_Sound_Warning">Sound Warning</string>
    <string name="harmanbar_jbl_Do_not_make_loud_noises_or_walk_in_front_of_your_speaker_during_the_calibration_process_">Do not make loud noises or walk in front of your speaker during the calibration process.</string>
    <string name="harmanbar_jbl_If_you_change_the_environment_or_the_position_of_the_products_in_the_future__please_re_Calibrate_">If you change the environment or the position of the products in the future, please re-Calibrate.</string>
    <string name="harmanbar_jbl_HAVE_A_TRY">PLEASE TRY</string>
    <string name="harmanbar_jbl_Tuning____">TUNING ...</string>
    <string name="harmanbar_jbl_To_Calibrate_later__find_the__Calibration__option_from_product_control_page_">To Calibrate later, find the [Calibration] option from product control page.</string>
    <string name="harmanbar_jbl_TRY_AGAIN">TRY AGAIN</string>
    <string name="harmanbar_jbl_SKIP">SKIP</string>
    <string name="harmanbar_jbl_If_you_change_the_environment_or_the_position_of_the_products_in_future__please_re_calibrate_">If you change the environment or the position of the products in future, please re-calibrate.</string>
    <string name="harmanbar_jbl_Let_s_start_the_amazing_music_journey_">Let\'s start the amazing music journey!</string>
    <string name="harmanbar_jbl_I_HAVE_PLACE_THEM">I HAVE PLACE THEM</string>
    <string name="harmanbar_jbl_Step_2__Move_Rear_speakers">Step 2: Move Rear speakers</string>
    <string name="harmanbar_jbl_Now_please_put_the_rear_speakers_where_you_want_them_to_be_placed_around_the_listening_area_">Now please put the rear speakers where you want them to be placed around the listening area.</string>
    <string name="harmanbar_jbl_Step_1__Place_Rear_speakers">Step 1: Place Rear speakers</string>
    <string name="harmanbar_jbl_Please_take_the_2_rear_speakers_off_from_soundbar_and_put_them_on_the_daily__listening_spot_next_to_">Please undock the detachable rear speakers and place them in your preferred listening position.\n\nDistance between soundbar and speakers should be 3 ~ 5 meters.</string>
    <string name="harmanbar_jbl_My_Products">My Products</string>
    <string name="harmanbar_jbl_Avoid_Obstacles">Avoid Obstacles</string>
    <string name="harmanbar_jbl_Check_for_Update">Check For Update</string>
    <string name="harmanbar_jbl_Do_not_block_soundbar__rear_speakers_or_put_them_too_close_to_wall_">Do not block soundbar / detachable rear speakers or put them too close to the wall.&#160;</string>
    <string name="harmanbar_jbl_Updating">Updating</string>
    <string name="harmanbar_jbl_Please_undock_both_rear_speakers_and_make_sure_you_have_placed_them_according_to_guide_">Please undock both rear speakers and make sure you have placed them according to the guide.</string>
    <string name="harmanbar_jbl_End_User_License_Agreement">End User License Agreement</string>
    <string name="harmanbar_jbl_Harman_Privacy_Statement">Harman Privacy Statement</string>
    <string name="harmanbar_jbl_Add_Music">Add Music</string>
    <string name="harmanbar_jbl_You_cannot_use_the_JBL_One_app_for_product_control_and_other_service_if_do_not_accept_the_Terms_and_">You cannot use the %s app for product control and other services if you do not accept the Terms and Statement. Please accept to continue.</string>
    <string name="harmanbar_jbl_To_continue_using_this_app__please_agree_to_the_following_terms_">To continue using this app, please agree to the following terms.</string>
    <string name="harmanbar_jbl_GRANT">GRANT</string>
    <string name="harmanbar_jbl_Put_your_speaker_in_setup_mode_">Put your product in setup mode.</string>
    <string name="harmanbar_jbl_Sorry__we_are_unable_to_find_your_product__please_reset_and_try_again_">Sorry, we are unable to find your product, please reset and try again.</string>
    <string name="harmanbar_jbl_Please_grant_Permissions_to_put_JBL_products_in_discovery_mode_and_connection_">Please grant permission to put product in discovery mode.</string>
    <string name="harmanbar_jbl_Press_and_hold_VOL____VOL____SOURCE_button_at_the_same_time_until_you_see_status_LED__start_breathin">Press and hold VOL- &amp; VOL+ &amp; SOURCE buttons at the same time until LED flashes (hold for about 5 seconds).</string>
    <string name="harmanbar_jbl_No_personal_information_will_be_collected_">No personal information will be collected.</string>
    <string name="harmanbar_jbl_Local_Network">Local Network</string>
    <string name="harmanbar_jbl_Trigger_Your_product">Trigger Your Product</string>
    <string name="harmanbar_jbl_Bluetooth_Permission">Bluetooth Permission</string>
    <string name="harmanbar_jbl_Location_Permission">Location Permission</string>
    <string name="harmanbar_jbl_Grant_Permission_In_Settings">Set up</string>
    <string name="harmanbar_jbl_Connected">Connected</string>
    <string name="harmanbar_jbl_To_find_and_connect_to_devices_on_your_local_network__please_grant___Local_network___permission_in_A">Please go to Settings to grant \"Local network\" permission to put product in discovery mode.</string>
    <string name="harmanbar_jbl_Please_connect_your_phone_to_Wi_Fi_to_continue_setting_up_your_speaker_">Please connect your phone to Wi-Fi to continue setting up your speaker.</string>
    <string name="harmanbar_jbl_To_easily_connect_your_product__please_grant___Bluetooth___Permission_in_App_settings_">Please go to Settings to grant \"Bluetooth\" permission to put product in discovery mode.</string>
    <string name="harmanbar_jbl_GO_TO_SETTINGS">GO TO SETTINGS</string>
    <string name="harmanbar_jbl_To_easily_connect_your_product__please_grant___Location___Permission_in_App_settings_">Please go to Settings to grant \"Location\" permission to put product in discovery mode.</string>
    <string name="harmanbar_jbl_Not_Connected">Not Connected</string>
    <string name="harmanbar_jbl_Location_Is_Required_To_Setup_New_Product">Location Is Required To Set Up New Product</string>
    <string name="harmanbar_jbl_Set_up_Wi_Fi">Set Up Wi-Fi</string>
    <string name="harmanbar_jbl_Change_Network">Change Network</string>
    <string name="harmanbar_jbl_Go_to_control_center_and_turn_on_Bluetooth_to_setup_JBL_One_product_">Go to control center and turn on Bluetooth to set up product.</string>
    <string name="harmanbar_jbl_Choose_your_Wi_Fi_Network_">Choose your Wi-Fi Network.</string>
    <string name="harmanbar_jbl_Discovering_product___">Discovering product...</string>
    <string name="harmanbar_jbl_Cannot_find_product_">Cannot find product?</string>
    <string name="harmanbar_jbl_Other_Network">Other Network</string>
    <string name="harmanbar_jbl_CONNECT">CONNECT</string>
    <string name="harmanbar_jbl_CHECKING">CHECKING</string>
    <string name="harmanbar_jbl_Your_product_is_up_to_date_">Your product is up to date.</string>
    <string name="harmanbar_jbl_Please_wait_while_the_connection_is_completed__This_might_take_a_minute_">Please wait until the connection is completed. This will take up to 1 minute.</string>
    <string name="harmanbar_jbl_Connecting">Connecting</string>
    <string name="harmanbar_jbl____MINUTE">%s MINUTE</string>
    <string name="harmanbar_jbl_Wi_Fi_Connected">Wi-Fi Connected</string>
    <string name="harmanbar_jbl_Connect_Failed">Connection Failed</string>
    <string name="harmanbar_jbl_Select___JBL_Soundbar_xxx___in_the_list_of_networks_">Select \"JBL Soundbar_xxx\" in the list of networks.</string>
    <string name="harmanbar_jbl_Unsuccessful">Unsuccessful</string>
    <string name="harmanbar_jbl_Open_your_phone_settings_and_tap_on_Wi_Fi_">Open your mobile phone settings and tap on Wi-Fi.</string>
    <string name="harmanbar_jbl_Return_to_this_App_">Return to this App.</string>
    <string name="harmanbar_jbl_Start_OTA_Failed">Start OTA Failed</string>
    <string name="harmanbar_jbl_Manage_Services">Supported Services</string>
    <string name="harmanbar_jbl_Add_Music_Service">Add A Music Service</string>
    <string name="harmanbar_jbl_Open_Source_Licenses">Open Source Licenses</string>
    <string name="harmanbar_jbl_Connections">Connections</string>
    <string name="harmanbar_jbl_GET_STARTED">GET STARTED</string>
    <string name="harmanbar_jbl_App_preference">App Preference</string>
    <string name="harmanbar_jbl_Welcome">Welcome</string>
    <string name="harmanbar_jbl_Enjoy_the_immersive_audio_experience_provided_by_JBL_One_Platform_">Enjoy amazing sound and more control.</string>
    <string name="harmanbar_jbl_FAQ">FAQ</string>
    <string name="harmanbar_jbl_Legal">Legal</string>
    <string name="harmanbar_jbl_App_version">App version</string>
    <string name="harmanbar_jbl_Settings">Settings</string>
    <string name="harmanbar_jbl_Open_Google_Home_app">Open Google Home App</string>
    <string name="harmanbar_jbl_No_Wi_Fi_Connection">No Wi-Fi Connection</string>
    <string name="harmanbar_jbl_More_Features">More Features</string>
    <string name="harmanbar_jbl_Please_connect_your_phone_to_Wi_Fi_for_more_product_discovery_and_control_">Please connect your mobile phone to Wi-Fi for more product discovery and control.</string>
    <string name="harmanbar_jbl_Add_Product">Add Product</string>
    <string name="harmanbar_jbl_Only_compatible_speakers_listed_above_are_supported_by_this_app__We_are_continuously_working_on_supp">Only compatible speakers listed above are supported by this app. We are continuously working on supporting more products. Please visit JBL.com for more information about our speakers.</string>
    <string name="harmanbar_jbl_Ready_To_Play_Some_Music_">Ready To Play Some Music?</string>
    <string name="harmanbar_jbl_DISCOVER_PRODUCT">DISCOVER PRODUCT</string>
    <string name="harmanbar_jbl_You_re_ready_to_start_casting__Look_for_the_Cast_button_in_hundreds_of_apps_">You\'re ready to start casting. Look for the Cast button in hundreds of apps.</string>
    <string name="harmanbar_jbl_Connect_Product">Connect Product</string>
    <string name="harmanbar_jbl_Login_your_Alexa_account_to_start_using_Alexa_MRM_">Log into your Alexa account to start using Alexa MRM.&#160;</string>
    <string name="harmanbar_jbl_Enter_Wi_Fi_Setup_Mode">Enter Wi-Fi Setup Mode</string>
    <string name="harmanbar_jbl_Open_Spotify_App">Open Spotify App</string>
    <string name="harmanbar_jbl_Make_sure_product_is_powered_on_">Make sure product is powered on.</string>
    <string name="harmanbar_jbl_Press_and_hold_VOL__and_VOL__button_at_the_same_time_until_you_see_status_LED__start_breathing__abou">Press and hold VOL- and VOL+ buttons at the same time until LED flashes (hold for about 10 seconds).</string>
    <string name="harmanbar_jbl_Cannot_Find_Product">Cannot Find Product</string>
    <string name="harmanbar_jbl_Reset_Product">Reset Product</string>
    <string name="harmanbar_jbl_Manual_add">Manually add</string>
    <string name="harmanbar_jbl_Restore_Factory_Settings">Restore Factory Setting ?</string>
    <string name="harmanbar_jbl_Restore_will_clear_all_your_personal_settings_on_this_product_and_reset_the_product_software_to_Out_">This will erase all your personal settings and reset the product software to its default factory settings.</string>
    <string name="harmanbar_jbl_Are_you_sure_you_want_to_remove_this_product_from_JBL_One_App___">Are you sure you want to remove this product from %s App ?&#160;</string>
    <string name="harmanbar_jbl_Action_Is_Required">Action Is Required</string>
    <string name="harmanbar_jbl_RETRY">TRY AGAIN</string>
    <string name="harmanbar_jbl_Password">Password</string>
    <string name="harmanbar_jbl_Stop_Audio_In___">Stop Audio In %s</string>
    <string name="harmanbar_jbl_Network_Name">Network Name</string>
    <string name="harmanbar_jbl_Sorry__your_connection_with_the_device_was_interrupted_unexpectedly__please_try_again">Sorry, your connection with the device was interrupted, unexpectedly, please try again.</string>
    <string name="harmanbar_jbl_Invalid_password_for_____please_retry_">Invalid password for \"%s\", please retry.</string>
    <string name="harmanbar_jbl_Get_Spotify_Free">Get Spotify Free</string>
    <string name="harmanbar_jbl_Please_change_the_Wi_Fi_of_your_mobile_phone_to_network________">Please make sure your mobile phone is connected to network \"%s\".</string>
    <string name="harmanbar_jbl_Manage_services_for_music_streaming">Manage music streaming services.</string>
    <string name="harmanbar_jbl_Assign_Moment_Shortcut_from_here">Assign Moment Shortcut from here</string>
    <string name="harmanbar_jbl_Allow__JBL_One_App__to_Turn_on_bluetooth_">Allow \"%s App\" to Turn on Bluetooth?</string>
    <string name="harmanbar_jbl_Now_you_can_stream_music_to_your_JBL_products_via_Chromecast_built_in_">Now you can stream music to your JBL products via Google Cast.</string>
    <string name="harmanbar_jbl_Learn_more_from">Now you can stream music to your JBL products via Google Cast. Learn more from</string>
    <string name="harmanbar_jbl_If_you_want_to_enable_Amazon_Alexa_or_other_streaming_services__visit_the_3rd_party_service_page_to_">If you want to enable Amazon Alexa or other streaming services, visit the 3rd party service page to learn more.</string>
    <string name="harmanbar_jbl_ENABLE_OTHER_SERVICES">ENABLE OTHER SERVICES</string>
    <string name="harmanbar_jbl_Turn_on_Bluetooth_of_your_phone_to_discover_and_set_up_product_">Turn on Bluetooth of your mobile phone to discover and set up product.</string>
    <string name="harmanbar_jbl_Discovering">Discovering</string>
    <string name="harmanbar_jbl_Discovering_New_Product____">Discovering New Product ...</string>
    <string name="harmanbar_jbl_Dolby_Atmos_Level">Dolby Atmos Level</string>
    <string name="harmanbar_jbl_Auto_Off">Auto Off</string>
    <string name="harmanbar_jbl_RESTORE_FACTORY_SETTINGS">RESTORE FACTORY SETTINGS</string>
    <string name="harmanbar_jbl_Music___Content">Music &amp; Content</string>
    <string name="harmanbar_jbl_Setup_A_Product_to_start_your_music_journey_">Set up a product to start your music journey!</string>
    <string name="harmanbar_jbl_Setup">Setup</string>
    <string name="harmanbar_jbl_Remove_Music_Service_">Remove Music Service ?</string>
    <string name="harmanbar_jbl_Removing_Music_Service_will_also_log_you_out_if_this_service_from_JBL_One_App__please_confirm_">Removing this Music Service will also log you out of this service from %s App. Please confirm.</string>
    <string name="harmanbar_jbl_Please_make_sure_both_rear_speakers_are_undocked_and_wirelessly_connected_to_soundbar_">Please make sure both rear speakers are undocked and wirelessly connected to soundbar.</string>
    <string name="harmanbar_jbl_DONE">DONE</string>
    <string name="harmanbar_jbl_LOG_IN">LOG IN</string>
    <string name="harmanbar_jbl_Loading____">Loading....</string>
    <string name="harmanbar_jbl_Please_enter_the_subject_of_the_problem">Please enter the subject of the problem.</string>
    <string name="harmanbar_jbl_Login_unsuccessful">Login unsuccessful</string>
    <string name="harmanbar_jbl_Please_describe_the_problem_you_have_">Please describe the problem you have.</string>
    <string name="harmanbar_jbl_Hint">Hint</string>
    <string name="harmanbar_jbl_Enable_Chromecast_for_music_streaming">Enable Chromecast for music streaming.</string>
    <string name="harmanbar_jbl_Manage_AirPlay__Chromecast_built_in_or_other_3rd_party_streaming_services">Manage AirPlay, Google Cast or other 3rd party streaming services.</string>
    <string name="harmanbar_jbl_Invalid_Email_Address">Invalid Email Address</string>
    <string name="harmanbar_jbl_Network_Control">Network Control</string>
    <string name="harmanbar_jbl_Please_enter_a_valid_email_address_">Please enter a valid email address.</string>
    <string name="harmanbar_jbl_3rd_Party_Service">3rd Party Service</string>
    <string name="harmanbar_jbl_Home_Network">Home Network</string>
    <string name="harmanbar_jbl_Do_you_want_to_send_debug_log_">Do you want to send debug log?</string>
    <string name="harmanbar_jbl_Wi_Fi_Strength">Wi-Fi Strength</string>
    <string name="harmanbar_jbl_IP_Address">IP Address</string>
    <string name="harmanbar_jbl_MAC_Address">MAC Address</string>
    <string name="harmanbar_jbl_Your_confirmation_ID_is___">Your confirmation ID is %s</string>
    <string name="harmanbar_jbl_Would_you_like_to_Sign_Out_">Would you like to Sign Out?</string>
    <string name="harmanbar_jbl_We_always_listen_to_you__our_users__nIf_you_have_any_feedback_to_help_us_improve_our_app__please_let">Your feedback will help us improve our services to you.</string>
    <string name="harmanbar_jbl_SIGN_OUT">SIGN OUT</string>
    <string name="harmanbar_jbl_Please_describe_your_problem_in_the_message_box_below_">Please describe your problem in the message box below.</string>
    <string name="harmanbar_jbl_The_name_of_device_is_empty_">The name of device is empty.</string>
    <string name="harmanbar_jbl_The_length_of_name_is_too_long">The length of name is too long.</string>
    <string name="harmanbar_jbl_Reset_AirPlay">Reset AirPlay</string>
    <string name="harmanbar_jbl_Only_numbers__letters_and_underscore_are_allowed">Only numbers, letters and underscore are allowed.</string>
    <string name="harmanbar_jbl_You_re_ready_to_start_streaming__Look_for_the_AirPlay_button_in_hundreds_of_apps_on_your_iOS_devices">You\'re ready to start streaming. Look for the AirPlay button in \'Control Centre\' on your iOS mobile phone.</string>
    <string name="harmanbar_jbl_Name_exists">Name exists</string>
    <string name="harmanbar_jbl_Cannot_Stream_Via_AirPlay_">Cannot Stream Via AirPlay?</string>
    <string name="harmanbar_jbl_Allow_to_access_your_location_while_you_are_using_the_app_">Allow to access your location while you are using the app?</string>
    <string name="harmanbar_jbl_If_you_come_across_issue_to_stream_via_AirPlay__please_press_below_button_to_reset_">If you cannot stream content with AirPlay, please press below button to reset.&#160;</string>
    <string name="harmanbar_jbl_APP_needs_to_find_your_Wi_Fi_network_as_location_information__This_app_doesn_t_collect_your_location">APP needs to find your Wi-Fi network as location information. This app doesn\'t collect your location using GPS or Wi-Fi based positioning technologies.</string>
    <string name="harmanbar_jbl_Select_Product">Select Product</string>
    <string name="harmanbar_jbl_Don_t_Allow">Don\'t Allow</string>
    <string name="harmanbar_jbl_RESET_AIRPLAY">RESET AIRPLAY</string>
    <string name="harmanbar_jbl_Please_connect_to_your_new_JBL_product">Please connect to your new JBL product.</string>
    <string name="harmanbar_jbl_To_stream_music__you_will_need_to_log_in_to_your_Amazon_Music_account_for_this_product_">To stream music, you will need to log into your %s account for this product.</string>
    <string name="harmanbar_jbl_Self_Tuning">Self-Tuning</string>
    <string name="harmanbar_jbl_The_speaker_will_self_tune_to_give_you_the_best_sound_">The speaker will self-tune to give you the best sound.</string>
    <string name="harmanbar_jbl_Bluetooth_Configuration">Bluetooth Configuration</string>
    <string name="harmanbar_jbl_No_Music">No Music</string>
    <string name="harmanbar_jbl_To_continue_using_this_app__please_accept__n_n__End_User_License_Agreement_n__Harman_Privacy_Stateme">To continue using this app, please accept:\n\n· End User License Agreement\n· Harman Privacy Statement</string>
    <string name="harmanbar_jbl_Product_Registration">Product Registration</string>
    <string name="harmanbar_jbl_Sign_Up_Newsletter">Sign Up Newsletter</string>
    <string name="harmanbar_jbl_Choose_your_product">Choose your product</string>
    <string name="harmanbar_jbl_Calibration_Takes_About_1_Minute">Calibration Takes About 1 Minute</string>
    <string name="harmanbar_jbl_Product_Support">Product Support</string>
    <string name="harmanbar_jbl_Software_Update">Software Update</string>
    <string name="harmanbar_jbl_UPDATE">UPDATE</string>
    <string name="harmanbar_jbl_What_s_New_">What\'s New?</string>
    <string name="harmanbar_jbl_Don_t_unplug_the_power_supply_during_the_update_">Don\'t unplug the power supply during the update.</string>
    <string name="harmanbar_jbl____min_remain">%s min remain</string>
    <string name="harmanbar_jbl_Help_Improve_Chromecast">Help Improve Chromecast</string>
    <string name="harmanbar_jbl_Current_version___">Current version %s</string>
    <string name="harmanbar_jbl_YES__I_M_IN">YES, I\'M IN</string>
    <string name="harmanbar_jbl_You_Are_All_Set">You Are All Set</string>
    <string name="harmanbar_jbl_SDo_you_want_to_help_improve_everyone_s_experience_by_sharing_device_stats_and_crash_reports_with_Go">Do you want to help improve everyone\'s experience by sharing device stats and crash reports with Google? Learn more  %s&#160;</string>
    <string name="harmanbar_jbl_Downloading">Downloading</string>
    <string name="harmanbar_jbl_Installing">Installing</string>
    <string name="harmanbar_jbl_NO_THANKS">NO THANKS</string>
    <string name="harmanbar_jbl_Remote_Controller">Remote Controller</string>
    <string name="harmanbar_jbl_Download_Unsuccessful">Download Unsuccessful</string>
    <string name="harmanbar_jbl_WPA_3">WPA 3</string>
    <string name="harmanbar_jbl_Troubleshot">Troubleshot</string>
    <string name="harmanbar_jbl_Current">Current</string>
    <string name="harmanbar_jbl_Software_Update_available_">Software Update available!</string>
    <string name="harmanbar_jbl_Calibration_takes_about_1_minute__A_loud_sound_will_come_from_your_products_during_calibration_">A loud sound will come from your products during calibration.</string>
    <string name="harmanbar_jbl_Works_With_OK_Google">Works With OK Google</string>
    <string name="harmanbar_jbl_Spotify">Spotify</string>
    <string name="harmanbar_jbl_supported">supported</string>
    <string name="harmanbar_jbl_GRANT_PERMISSION">GRANT PERMISSION</string>
    <string name="harmanbar_jbl_Grant_Permission">Grant Permission</string>
    <string name="harmanbar_jbl_Harman_is_requesting_access_to_upload_logs_from_your_JBL_One_device__If_you_grant_Harman_access__Harman_will_collect_information_about_your_device__such_as_current_system_logs_and_performance_logs__and_upload_them_to_Harman_servers__Harman_accesses_your_device_s_logs_in_order_to_debug_and_understand_issues_on_your_device__Harman_will_delete_your_device_data_within_five_days__For_more_information_on_how_Harman_uses_your_information__please_review_Harman_s_privacy_policy__available_at___App_li">Harman is requesting access to your device. If you grant Harman access to the device, Harman will collect information about your device, such as current system logs and performance logs, and will be able to control certain features of your device, such as the volume level. Harman accesses your device’s information and features in order to debug and repair your device. Harman will not be able to control your device once the debugging session has ended and will delete your device data within five days. For more information on how Harman uses your information, please review Harman’s privacy policy, available at https://www.harman.com/privacy-policy.\n\nDo you grant Harman permission to upload system logs on your device %s?</string>
    <string name="harmanbar_jbl_DISABLE_REMOTE_DEBUG">DISABLE REMOTE DEBUG</string>
    <string name="harmanbar_jbl_To_learn_more_and_access_additional_features__go_to_Google_Home_App_">To setup voice control from a Google Assistant-enabled speaker and multiroom groups with other Chromecast-enabled speakers, download Google Home app.</string>
    <string name="harmanbar_jbl_Product_log_has_been_uploaded_">Product log has been uploaded.</string>
    <string name="harmanbar_jbl____supports_Dolby_Atmos___When_there_is_Dolby_source_input__Dolby_Atmos_effect_will_be_automatically">%s supports Dolby Atmos.  When there is Dolby source input, Dolby Atmos effect will be automatically applied – giving you the truly immersive 3D surround sound for all your favorite entertainment.</string>
    <string name="harmanbar_jbl_This_app_will_be_able_to_discover_and_connect_devices_on_the_networks_you_use_">This app will be able to discover and connect devices on the networks you use.</string>
    <string name="harmanbar_jbl_Bluetooth_permissions_are_needed_to__find_and_connect_to_nearby_JBL_products__provide_diagnostics__a">Bluetooth permissions are needed to  find and connect to nearby JBL products, provide diagnostics, and access some configuration settings.</string>
    <string name="harmanbar_jbl_Location_permissions_are_required_to_setup_and_connect_to_your_JBL_products_reliably__We_need_these_">Location permissions are required to setup and connect to your JBL products reliably. We need these permissions so we can identify networks, access your network name, and find nearby JBL products.</string>
    <string name="harmanbar_jbl_Confirm">Confirm</string>
    <string name="harmanbar_jbl_Cancel">Cancel</string>
    <string name="harmanbar_jbl_Alexa__set_an_alarm_for_6_a_m__on_JBL_Soundbar_nAlexa__play_Taylor_Swift_on_JBL_Soundbar_nAlexa__nex">Alexa, set an alarm for 6 a.m. on ‘Product name’\nAlexa, play Taylor Swift on ‘Product name’\nAlexa, next on ‘Product name’\nAlexa, shuffle on ‘Product name’\n\nProduct name: e.g. JBL Bar 300</string>
    <string name="harmanbar_jbl_Powered_by">Powered by</string>
    <string name="harmanbar_jbl___Privacy_Policy__">\"Google Privacy Policy\"</string>
    <string name="harmanbar_jbl_Agree_with___Google_Terms_Of_Service___and___Privacy_Policy___to_start_using_Chromecast_built_in_">By clicking \"Enable Service\" below, you are indicating your agreement to the \"Google Terms Of Service\" and \"Google Privacy Policy\", which govern your use of Google Cast.</string>
    <string name="harmanbar_jbl___Google_Terms_Of_Service__">\"Google Terms Of Service\"</string>
    <string name="harmanbar_jbl_MID">MID</string>
    <string name="harmanbar_jbl_Listen_to_your_favourite_music_by_pressing___on_the_remote_control__">Listen to your favourite music by pressing %s on the remote control :</string>
    <string name="harmanbar_jbl_The_product_will_reconnect_to_last_paired_mobile_device_via_Bluetooth_when_below_action_is_trigger_">The product will reconnect to last paired mobile device via Bluetooth when below action is trigger:</string>
    <string name="harmanbar_jbl_Auto">Auto</string>
    <string name="harmanbar_jbl_Auto_reconnect_when_power_on">Auto reconnect when power on</string>
    <string name="harmanbar_jbl_Manual">Manual</string>
    <string name="harmanbar_jbl_Press_Bluetooth_button_to_reconnect">Press Bluetooth button to reconnect</string>
    <string name="harmanbar_jbl_Please_go_to_Settings_to_grant__Location___Nearby_devices__permission_to_put_product_in_discovery_mo">Please go to Settings to grant \"Location &amp; Nearby devices\" permission to put product in discovery mode.</string>
    <string name="harmanbar_jbl_Harman_is_requesting_access_to_upload_logs_from_your_device__If_you_grant_Harman_access__Harman_will_collect_information_about_your_device__such_as_current_system_logs_and_performance_logs__and_upload_them_to_Harman_servers__Harman_accesses_your_device_s_logs_in_order_to_debug_and_understand_issues_on_your_device__Harman_will_delete_your_device_data_within_five_days__For_more_information_on_how_Harman_uses_your_information__please_review_Harman_s_privacy_policy__available_at_https___www_harm">Harman is requesting access to upload logs from your device. If you grant Harman access, Harman will collect information about your device, such as current system logs and performance logs, and upload them to Harman servers. Harman accesses your device’s logs in order to debug and understand issues on your device. Harman will delete your device data within five days. For more information on how Harman uses your information, please review Harman’s privacy policy, available at https://www.harman.com/privacy-policy.\n\nDo you grant Harman permission to upload system logs on your  device %s?</string>
    <string name="harmanbar_jbl_Press_the_____button_on_the_tone_playing_speaker_to_continue_">Press the  %s  button on the tone-playing speaker to continue.</string>
    <string name="harmanbar_jbl_Learn_more">Learn more</string>
    <string name="harmanbar_jbl_Learn_More">Learn More</string>
    <string name="harmanbar_jbl_TREBLE">TREBLE</string>
    <string name="harmanbar_jbl_Enter_password_to_connect_your_speaker_to_network________">Enter password to connect your speaker to network \"%s\".</string>
    <string name="harmanbar_jbl_Add_music_service_here">Add music service here.</string>
    <string name="harmanbar_jbl_Product_successfully_reboot__AirPlay_has_been_reset_">Product reboot successful. AirPlay has been reset.</string>
    <string name="harmanbar_jbl_Chromecast_Built_In">Google Cast</string>
    <string name="harmanbar_jbl_No_matter_where_you_place_your_speaker__it_automatically_calibrates_itself_to_give_you_the_best_soun">No matter where you place your speaker, it automatically calibrates itself to give you the best sound quality for any space.</string>
    <string name="harmanbar_jbl_Getting_ready__please_wait___">Getting ready. Please wait.</string>
    <string name="harmanbar_jbl_Press_the_button_on_the_tone_playing_speaker_to_continue_">Press the button on the tone-playing product to continue.</string>
    <string name="harmanbar_jbl_Checking_the_latest_firmware__it_may_take_20_seconds_">Checking the latest firmware. This will take up to 20 seconds.</string>
    <string name="harmanbar_jbl_Do_NOT_unplug_power_cable_or_leave_the_app">Do NOT unplug the power cable or leave the app</string>
    <string name="harmanbar_jbl_Sorry__we_are_unable_to_connect_your_speaker_to_Wi_Fi_">We are unable to connect your product to Wi-Fi.</string>
    <string name="harmanbar_jbl_Change_Phone_Network">Check Phone Network</string>
    <string name="harmanbar_jbl_Sorry__we_are_not_able_to_connect_to_your_product_at_the_moment__please_restart_">We are unable to connect to your product. Please restart.</string>
    <string name="harmanbar_jbl_BACK_TO_DASHBOARD">GO TO DASHBOARD</string>
    <string name="harmanbar_product_settings">Product Settings</string>
    <string name="harmanbar_jbl_Terms___Agreements">Terms and Conditions</string>
    <string name="harmanbar_jbl_We_need_your_verification_on_the_speaker_to_continue_setup__Please_try_again_">We need your verification on the product to continue setup. Please try again.</string>
    <string name="harmanbar_jbl_Your_speaker_is_connected_to_network________successfully_">Your product is now connected to network \"%s\".</string>
    <string name="harmanbar_jbl_Your_mobile_phone_couldn_t_connect_to_your_speaker_at_the_moment__let_s_try_something_else_">Your mobile phone could not connect to your product. Let\'s try something else.</string>
    <string name="harmanbar_jbl_Now_connect_this_phone_to_a_temporary_wireless_network_created_by_the_speaker_">Now connect this phone to a temporary wireless network created by the speaker.</string>
    <string name="harmanbar_jbl_Chromecast_Built_In_Available">Google Cast Available</string>
    <string name="harmanbar_jbl_Rotate_for_the_best_listening_experience_">Rotate for the best listening experience.</string>
    <string name="harmanbar_jbl_It_is_a_personalization_feature_that_allows_you_to_customize_your_favorite_music_track__auto_off_tim">It is a personalization feature that allows you to customize your favorite music track, auto-off timing and music volume.
 
Once setup, you can directly access it through the Moment button on product or in the App.</string>
    <string name="harmanbar_jbl_Serial_Number">Serial Number</string>
    <string name="harmanbar_jbl_In_some_cases_when_the_soundbar_is_connected_to_a_digital_TV__the_audio_may_not_synchronize_with_the">In some cases when the soundbar is connected to a digital TV, the audio may not synchronize with the video. If the video is behind the audio, adjust the audio delay time to match the video.</string>
    <string name="harmanbar_jbl_Sleep_in___">Sleep in %s</string>
    <string name="harmanbar_jbl_Music_Service_Login">Music Service Log In</string>
    <string name="harmanbar_jbl_Reset_AirPlay_will_reboot_product__this_takes_about_1_minute_before_your_can_stream_music_again_">Resetting AirPlay will reboot the product. This will take about 1 minute before you can stream music again.</string>
    <string name="harmanbar_jbl_Create_Your_Moment">Create Your Moment</string>
    <string name="harmanbar_jbl_How_to_Set_Up_Moment__">How to Set Up Moment ?</string>
    <string name="harmanbar_jbl_Set_Up_Moment">Set Up Moment</string>
    <string name="harmanbar_jbl_1__Tap_______to_add_your_preferred_music_service__n_n2__Sign_in_to_your_account__n_n3__Tap_the_first">1. Tap ‘ + ’ to add your preferred music service.\n\n2. Sign in to your account.\n\n3. Tap the first track of your favorite playlist or radio station.\n\n4. Set desired auto-off time and volume, then tap ‘SAVE’.\n\n5. From next time onwards, your favorite playlist / station will be accessible with the   %s   button press on the remote control.</string>

    <!--NewFavorite-->
    <string name="harmanbar_NewFavorite_Songs">Songs</string>
    <string name="harmanbar_NewFavorite_See_All">See All</string>
    <string name="harmanbar_NewFavorite_Play_some_music_first">Play some music first</string>
    <string name="harmanbar_NewFavorite_Your_favorite_song__playlist__station_will_show_here">Your favorite song, playlist, station will show here</string>
    <string name="harmanbar_NewFavorite_Shuffle_Play">Shuffle Play</string>
    <string name="harmanbar_NewFavorite_More">More</string>
    <string name="harmanbar_NewFavorite_In_order_to_play_entire_content_on_____please_use____to_login____on____and_it_will_overwrite_your_pr">In order to play entire content on %1$s, please use %2$s to login %3$s on %4$s and it will overwrite your previous account.</string>
    <string name="harmanbar_NewFavorite_You_don_t_have_logged_in____account_on_____we_will_sync_your____account____to______">You don\'t have logged in %1$s account on %2$s, we will sync your %3$s account %4$s to %5$s...</string>
    <string name="harmanbar_NewFavorite_In_order_to_play_entire_content_on_____please_login____on____and_ensure_the_account_is____">In order to play entire content on %1$s, please login %2$s on %3$s and ensure the account is %4$s.</string>
    <string name="harmanbar_NewFavorite_Recently_played">Recently played</string>
    <string name="harmanbar_NewFavorite_Liked_Songs">Liked Songs</string>
    <string name="harmanbar_NewFavorite_Playlists">Playlists</string>
    <string name="harmanbar_NewFavorite_Stations">Stations</string>
    <string name="harmanbar_NewFavorite_Nothing_here_yet">Nothing here yet</string>
    <string name="harmanbar_NewFavorite_Music_you_listen_will_appear_here">Music you listen will appear here</string>

    <!--SoundMachine-->
    <string name="harmanbar_SoundMachine_Log_Out">Log Out</string>
    <string name="harmanbar_SoundMachine_Log_out_failed">Log out failed</string>
    <string name="harmanbar_SoundMachine_Cancel">Cancel</string>
    <string name="harmanbar_SoundMachine_Logout">Logout</string>
    <string name="harmanbar_SoundMachine_Login_failed">Login failed</string>
    <string name="harmanbar_SoundMachine_Name">Name</string>
    <string name="harmanbar_SoundMachine_Country">Country</string>
    <string name="harmanbar_SoundMachine_Account">Account</string>
    <string name="harmanbar_SoundMachine_Other_operations">Other operations</string>
    <string name="harmanbar_SoundMachine_Setting">Setting</string>
    <string name="harmanbar_SoundMachine_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_SoundMachine_User_name_">User name：</string>
    <string name="harmanbar_SoundMachine_Enter_password_">Enter password：</string>
    <string name="harmanbar_SoundMachine_Login">Login</string>
    <string name="harmanbar_SoundMachine_Don_t_have_an_account">Don\'t have an account</string>
    <string name="harmanbar_SoundMachine_Remember_me">Remember me</string>
    <string name="harmanbar_SoundMachine_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_SoundMachine_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_SoundMachine_Play_failed">Play failed</string>
    <string name="harmanbar_SoundMachine_Added_successfully">Added successfully</string>
    <string name="harmanbar_SoundMachine_Successfully_deleted">Successfully deleted</string>
    <string name="harmanbar_SoundMachine_Failed">Failed</string>
    <string name="harmanbar_SoundMachine_Log_out__please_log_in_again">Log out, please log in again</string>
    <string name="harmanbar_SoundMachine_Skip">Skip</string>
    <string name="harmanbar_SoundMachine_Skipping_is_limited_to_6_skips_per_hour">Skipping is limited to 6 skips per hour</string>
    <string name="harmanbar_SoundMachine_Do_you_need_to_reply_to_play__">Do you need to reply to play ?</string>
    <string name="harmanbar_SoundMachine_OK">OK</string>
    <string name="harmanbar_SoundMachine_Continue">Continue</string>
    <string name="harmanbar_SoundMachine_No_Stations_Found">No Stations Found</string>
    <string name="harmanbar_SoundMachine_No_Mixes_Found">No Mixes Found</string>
    <string name="harmanbar_SoundMachine_No_Schedules_Found">No Schedules Found</string>
    <string name="harmanbar_SoundMachine_No_Found">No Found</string>
    <string name="harmanbar_SoundMachine_Log_out">Log out</string>
    <string name="harmanbar_SoundMachine_Genres">Genres</string>
    <string name="harmanbar_SoundMachine_Stations">Stations</string>
    <string name="harmanbar_SoundMachine_My_Stations">My Stations</string>
    <string name="harmanbar_SoundMachine_Mixes">Mixes</string>
    <string name="harmanbar_SoundMachine_Schedules">Schedules</string>

    <!--CalmRadio-->
    <string name="harmanbar_CalmRadio_Cancel">Cancel</string>
    <string name="harmanbar_CalmRadio_Logout">Logout</string>
    <string name="harmanbar_CalmRadio_Log_out_failed">Log out failed</string>
    <string name="harmanbar_CalmRadio_Don_t_have_an_account">Don\'t have an account</string>
    <string name="harmanbar_CalmRadio_You_last_played_channels">You last played channels</string>
    <string name="harmanbar_CalmRadio_Browse">Browse</string>
    <string name="harmanbar_CalmRadio_Videos">Videos</string>
    <string name="harmanbar_CalmRadio_Favorites">Favorites</string>
    <string name="harmanbar_CalmRadio_See_All">See All</string>
    <string name="harmanbar_CalmRadio_Play">Play</string>
    <string name="harmanbar_CalmRadio_Settings">Settings</string>
    <string name="harmanbar_CalmRadio_Account_Name">Account Name</string>
    <string name="harmanbar_CalmRadio_Quality">Quality</string>
    <string name="harmanbar_CalmRadio_Sign_Out">Sign Out</string>
    <string name="harmanbar_CalmRadio_High_Definition">High Definition</string>
    <string name="harmanbar_CalmRadio_Normal">Normal</string>
    <string name="harmanbar_CalmRadio_Free">Free</string>
    <string name="harmanbar_CalmRadio_Mobile">Mobile</string>
    <string name="harmanbar_CalmRadio_Login">Login</string>
    <string name="harmanbar_CalmRadio_Premium">Premium</string>
    <string name="harmanbar_CalmRadio_Premium_only">Premium only</string>
    <string name="harmanbar_CalmRadio_User_name_">User name：</string>
    <string name="harmanbar_CalmRadio_Enter_password_">Enter password：</string>
    <string name="harmanbar_CalmRadio_The_username_can_t_be_empty">The username can\'t be empty</string>
    <string name="harmanbar_CalmRadio_The_password_can_t_be_empty">The password can\'t be empty</string>
    <string name="harmanbar_CalmRadio_Login_failed">Login failed</string>
    <string name="harmanbar_CalmRadio_Remember_me">Remember me</string>
    <string name="harmanbar_CalmRadio_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_CalmRadio_Choose">Choose</string>

    <!--newtidal-->
    <string name="harmanbar_newtidal_Account">Account</string>
    <string name="harmanbar_newtidal_Account_Name">Account Name</string>
    <string name="harmanbar_newtidal_Quality">Quality</string>
    <string name="harmanbar_newtidal_Add_to_My_Music">Add to My Music</string>
    <string name="harmanbar_newtidal_Remove_from_My_Music">Remove from My Music</string>
    <string name="harmanbar_newtidal_BIOGRAPHY">BIOGRAPHY</string>
    <string name="harmanbar_newtidal_FREE">Free</string>
    <string name="harmanbar_newtidal_Normal">Normal</string>
    <string name="harmanbar_newtidal_Would_you_like_to_log_out_">Would you like to log out?</string>
    <string name="harmanbar_newtidal_order_all">order all</string>
    <string name="harmanbar_newtidal_Subscription">Subscription</string>
    <string name="harmanbar_newtidal_Give_your_playlist_a_name">Give your playlist a name</string>
    <string name="harmanbar_newtidal_name">name</string>
    <string name="harmanbar_newtidal_SIMILAR">SIMILAR</string>
    <string name="harmanbar_newtidal_Update_your_subscription_on_my_tidal_com_to_get_full_access_to_TIDAL_">Update your subscription on my.tidal.com to get full access to TIDAL!</string>
    <string name="harmanbar_newtidal_This_account_is_playing_on_other_devices">This account is playing on other devices</string>
    <string name="harmanbar_newtidal_NORMAL">NORMAL</string>
    <string name="harmanbar_newtidal_HIGH">HIGH</string>
    <string name="harmanbar_newtidal_Release_date">Release date</string>
    <string name="harmanbar_newtidal_All">All</string>
    <string name="harmanbar_newtidal_Filter">Filter</string>
    <string name="harmanbar_newtidal_Sorting">Sorting</string>
    <string name="harmanbar_newtidal_Custom_order">Custom order</string>
    <string name="harmanbar_newtidal_Date_added">Date added</string>
    <string name="harmanbar_newtidal_Alphabetical">Alphabetical</string>
    <string name="harmanbar_newtidal_Album">Album</string>
    <string name="harmanbar_newtidal_Artist">Artist</string>
    <string name="harmanbar_newtidal_You_don_t_have_any_Mixes_yet__TIDAL_will_generate_these_based_on_your_listening_activity_">You don’t have any Mixes yet, TIDAL will generate these based on your listening activity!</string>

    <string name="harmanbar_default_source">Default Source</string>
    <string name="harmanbar_default_source_wifi_text1">The product will automatically reconnect to Wi-Fi when powered on.</string>
    <string name="harmanbar_default_source_wifi_text2">Playback via Bluetooth</string>
    <string name="harmanbar_default_source_wifi_text3">Press Bluetooth button on product to pair with mobile and start playback.</string>

    <string name="harmanbar_default_source_bluetooth_text1">The product will automatically reconnect to the last paired mobile via Bluetooth when powered on. </string>
    <string name="harmanbar_default_source_bluetooth_text2">Stream via Wi-Fi</string>
    <string name="harmanbar_default_source_bluetooth_text3">The product will still reconnect to Wi-Fi, you can start streaming music manually.</string>
    <string name="harmanbar_portable_connect_product_setup_desc">Press and hold MOMENT &amp; BLUETOOTH buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="harmanbar_portable_connectnot_find_product_desc">Press and hold MOMENT &amp; PLAY buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="harmanbar_moment_unlock_feature">Unlock Feature</string>
    <string name="harmanbar_moment_unlock_feature_desc">Please connect your product to Wi-Fi to use advance feature.</string>
    <string name="harmanbar_support">Support</string>

    <string name="harmanbar_send_device_usage_and_crash_reports_to_google">Send Device Usage and Crash Reports to Google</string>


    <string name="harmanbar_speaker_how_to_setup_moment">1. Tap ‘ + ’ to add your preferred music service.\n\n2. Sign in to your account.\n\n3. Tap the first track of your favorite playlist or radio station.\n\n4. Set desired auto-off time and volume, then tap ‘SAVE’.\n\n5. From next time onwards, your favorite playlist / station will be accessible with the   %s   button press on your product.</string>
    <string name="bass_boost">Bassboost</string>
    <string name="personalize_your_product">Personalize Your Product</string>
    <string name="top_panel">Top Panel</string>
    <string name="mic_effect">Mic Effect</string>
    <string name="bass_boost_deep">Deep</string>
    <string name="bass_boost_punchy">Punchy</string>


    <string name="product_controls">Product Controls</string>
    <string name="enjoy_music">Enjoy Music</string>
    <string name="open_tidal">Open Tidal</string>
    <string name="music_streaming_with_tidal_connect">Music Streaming with Tidal Connect</string>
    <string name="music_streaming_with_tidal_connect_desc">Stream your favorite music seamlessly from the cloud straight to your devices.</string>
    <string name="product_management">Product Management</string>
    <string name="add_new_product">Add New Product</string>

    <string name="supported_music_services">Supported Music Services</string>
    <string name="tidal_music_service_desc">High Fidelity Music Streaming.</string>
    <string name="amazon_music_service_desc">90 million songs with new releases from today\'s most popular artists.</string>
    <string name="calm_radio_music_service_desc">Calming music to harmonize your day.</string>
    <string name="napster_music_service_desc">Music From Every Angle.</string>
    <string name="qobuz_music_service_desc">Unlimited high quality streaming.</string>
    <string name="sound_machine_music_service_desc">White noise rockwell ventures.</string>
    <string name="iheart_radio_music_service_desc">Music, Radio, and Podcasts.</string>
    <string name="vtuner_desc">Listen to thousands of radio stations that are streaming over the internet with vTuner.</string>
    <string name="bluetooth_reconnection_preference_desc">Bluetooth is prioritized for streaming when both Bluetooth and Wi-Fi are connected. \n\n\n If you use this speaker more often in Wi-Fi environment, it\'s recommended to keep auto Bluetooth reconnection OFF.</string>
    <string name="auto_bluetooth_reconnection">Auto Bluetooth Reconnection</string>
    <string name="auto_bluetooth_reconnection_desc">Product will be reconnected to last paired mobile when powered on.</string>
    <string name="getting_ready_for_wifi_streaming_desc">Enjoy audio in its highest quality and stream from your favorite music services without interruption over Wi-Fi.</string>
    <string name="streaming_with_cast_tips">1.Open music service app and start playback.\n2.Tap “Cast” button and select a speaker to stream.</string>
    <string name="other_services_for_stream">Other Services You Can Use to Stream on Your Product</string>
    <string name="use_on_alexa">Ready to play on Amazon Alexa App.</string>
    <string name="use_on_spotify">Ready to play on Spotify App.</string>
    <string name="use_on_tidal">Ready to play on Tidal App.</string>
    <string name="use_on_qobuz">Ready to play on Qobuz App.</string>
    <string name="use_on_airplay">Ready to play on iOS platform.</string>
    <string name="use_on_dlna">Ready to play on DLNA-compatible streaming services.</string>
    <string name="chromecast_setup_ready_desc">Product is ready to use. You can enjoy Wi-Fi streaming with Chromecast now.</string>

    <string name="ready_to_connect_desc">Connect to Wi-Fi or Bluetooth to start playback.</string>
    <string name="cannot_connect_your_product">Can\’t connect your product ?</string>
    <string name="disconnect_others">Disconnect Others</string>
    <string name="disconnect_others_desc">Disconnect your product from other JBL App.</string>
    <string name="restart_your_product">Restart your product</string>
    <string name="restart_your_product_desc">Learn how to turn on/off the product, refer to</string>

    <string name="ready_to_connect">Ready to Connect</string>
    <string name="ready_to_connect_or">Or</string>
    <string name="ready_to_connect_connect_to_wifi">Connect to Wi-Fi</string>
    <string name="connect_to_wifi_desc">Initialize Wi-Fi connection for your product.</string>
    <string name="pair_bluetooth">Pair Bluetooth</string>
    <string name="pair_bluetooth_desc">Follow the instruction to connect with mobile device.</string>
    <string name="press_bluetooth_button">Press Bluetooth Button</string>
    <string name="press_bluetooth_button_desc">Press Bluetooth button on your product to enter pairing mode, and you will hear a feedback tone.</string>
    <string name="i_have_pressed_button">I\'VE PRESSED BUTTON</string>

    <string name="software_update">Software Update</string>
    <string name="connect_to_wifi_content">Connect to Wi-Fi to start streaming and unlock more features.</string>
    <string name="please_pause_music">Please pause music playback before connecting to Wi-Fi.</string>
    <string name="btn_connect_to_wifi">CONNECT TO WI-FI</string>

    <string name="connect_with_others_desc">This Product is now connected with another phone.</string>
    <string name="create_multi_room_with">Create Multi-Room with: %1$s</string>
    <string name="support_for_other_products">Support for Other Products</string>


    <string name="stereo2_0">Stereo 2.0</string>
    <string name="party">Party</string>
    <string name="same_sound">Same sound</string>
    <string name="start">start</string>
    <string name="create_stereo_group">Create Stereo group</string>
    <string name="more_two">At the moment only two speakers are supported for \‘Play together\’</string>
    <string name="two_connect_type">Please connect  %1$s  to Wi-Fi to group.</string>
    <string name="two_cast_version">Please update %1$s\'s software to group.</string>
    <string name="group_info">Group Information</string>
    <string name="primary_speaker">Primary Speaker</string>
    <string name="break_this_group">Ungroup</string>
    <string name="break_msg">Are you sure you want to ungroup  the “ %1$s ” ?\nProducts in this group will become standalone speakers.</string>
    <string name="breaking">Ungrouping “ %1$s “ ,Please wait ...</string>
    <string name="break_group_fail_content">Sorry, we are not able to ungroup “ %1$s “ at the moment, please try again later.</string>
    <string name="group_as_stereo">Group as Stereo</string>
    <string name="group_as_stereo_sub_title">Do you want to group your products as a stereo 2.0 system for enhanced stereo sound?</string>
    <string name="group_speakers">GROUP SPEAKERS</string>
    <string name="group_as_stereo_sub_title_2">Do you want to group your products as a Stereo 2.0 system for an immersive sound experience?</string>
    <string name="product_missing">Product Missing</string>
    <string name="choose_products">Choose Products</string>
    <string name="status">Status</string>


    <string name="speakers_in_the_group">Speakers In The Group</string>
    <string name="identity">Identity</string>
    <string name="left_str">Left</string>
    <string name="right_str">Right</string>
    <string name="not_setup">Not Setup</string>
    <string name="wifi_is_not_connected">Wi-Fi is not connected</string>
    <string name="connect_to_wifi">Connect to Wi-Fi to start streaming and unlock more features.</string>


    <string name="multi_room">Multi-Room</string>
    <string name="multi_room_msg">Play music in any room or every room in the house from a central mobile device.</string>
    <string name="create_group">Create Group</string>
    <string name="stereo_msg">Upgrade your listening experience with stereo effect.</string>
    <string name="identical_speakers">2 identical speakers are required</string>
    <string name="create_multi_room">Create Multi-Room with ChromeCast</string>

    <string name="immersive_audio">Immersive Audio</string>
    <string name="place_both_speakers">Place both speakers side by side facing towards the listener.</string>
    <string name="communicating">Communicating...</string>
    <string name="cancel_grouping_will">Cancel grouping will take about 20 seconds.
Products will remain stand alone speakers.</string>
    <string name="cancel_grouping">cancel grouping</string>
    <string name="products_cannot_communicate">Products cannot communicate with each other at the moment, please try again.</string>
    <string name="calibratioon_tone">Calibration tone will come from your speakers for space detection.</string>
    <string name="keep_quiet_and">Keep quiet and do not block your speakers during the process.</string>
    <string name="start_calibration">start calibration</string>
    <string name="manual_assign_channel">cannot do calibration</string>
    <string name="calibrating">Calibrating...</string>
    <string name="which_product_is_go">Which product is lighting up the green LED in front of you now ?</string>
    <string name="group_successful">Grouping Successful!</string>
    <string name="try_demo">TRY DEMO</string>
    <string name="rename_is_not_available">Rename is not available since you have added product into Apple Home Kit.\nPlease customize product name from Apple Home App.</string>

    <string name="select_products">Select Products</string>
    <string name="check_environment">Checking product status ...</string>

    <string name="restart_and_try">Products cannot communicate with each other, please restart products and retry.</string>
    <string name="your_music">Your Music Starts From</string>

    <string name="online">Online</string>
    <string name="connected_by_others">Connected with Others</string>
    <string name="no_music_is_playing">No Music Is Playing</string>
    <string name="offline">Offline</string>

    <string name="create_tws_pair">Create a TWS pair</string>
    <string name="cant_find_device_to_pair">Cannot find the products to pair with.</string>
    <string name="check_compatible_speakers">Check compatible speakers ...</string>
    <string name="select_product_to_pair">Select the products that you want to pair with:</string>

    <!--Added on 2023.04.18-->
    <string name="jbl_Confirm">Confirm</string>
    <string name="jbl_Cancel">Cancel</string>
    <string name="jbl_Sign_Up_Newsletter">Sign Up Newsletter</string>
    <string name="jbl_Choose_your_product">Choose your product</string>
    <string name="jbl_Calibration_Takes_About_1_Minute">Calibration Takes About 1 Minute</string>
    <string name="jbl_Powered_by">Powered by</string>
    <string name="jbl_Do_NOT_unplug_power_cable">Do NOT unplug power cable</string>
    <string name="jbl_Product_Support">Product Support</string>
    <string name="jbl_To_learn_more_and_access_additional_features__go_to_Google_Home_App_">To setup voice control from a Google Assistant-enabled speaker and multiroom groups with other Chromecast-enabled speakers, download Google Home app.</string>
    <string name="jbl_Agree_with___Google_Terms_Of_Service___and___Privacy_Policy___to_start_using_Chromecast_built_in_">By clicking \"Enable Service\" below, you are indicating your agreement to the \"Google Terms Of Service\" and \"Google Privacy Policy\", which govern your use of Google Cast.</string>
    <string name="jbl___Privacy_Policy__">\"Google Privacy Policy\"</string>
    <string name="jbl_Learn_more">Learn more</string>
    <string name="jbl_Remote_Controller">Remote Controller</string>
    <string name="jbl_SDo_you_want_to_help_improve_everyone_s_experience_by_sharing_device_stats_and_crash_reports_with_Go">Do you want to help improve everyone\'s experience by sharing device stats and crash reports with Google? Learn more </string>
    <string name="jbl_GRANT_PERMISSION">GRANT PERMISSION</string>
    <string name="jbl_WPA_3">WPA 3</string>
    <string name="jbl_Software_Update">Software Update</string>
    <string name="jbl_UPDATE">UPDATE</string>
    <string name="jbl_What_s_New_">What\'s New?</string>
    <string name="jbl_Don_t_unplug_the_power_supply_during_the_update_">Don\'t unplug the power supply during the update.</string>
    <string name="jbl___holderd___min_remain">[[holderd]] min remain</string>
    <string name="jbl_Current_version___holders__">Current version [[holders]]</string>
    <string name="jbl_You_Are_All_Set">You Are All Set</string>
    <string name="jbl_Downloading">Downloading</string>
    <string name="jbl_Installing">Installing</string>
    <string name="jbl_Getting_ready__please_wait___">Getting ready, please wait...</string>
    <string name="jbl_Download_Unsuccessful">Download Unsuccessful</string>
    <string name="jbl_Troubleshot">Troubleshot</string>
    <string name="jbl_Current">Current</string>
    <string name="jbl_Software_Update_available_">Software Update available!</string>
    <string name="jbl_Works_With_OK_Google">Works With OK Google</string>
    <string name="jbl_Spotify">Spotify</string>
    <string name="jbl_supported">supported</string>
    <string name="jbl_Grant_Permission">Grant Permission</string>
    <string name="jbl_Harman_is_requesting_access_to_upload_logs_from_your_JBL_One_device__If_you_grant_Harman_access__Harman_will_collect_information_about_your_device__such_as_current_system_logs_and_performance_logs__and_upload_them_to_Harman_servers__Harman_accesses_your_device_s_logs_in_order_to_debug_and_understand_issues_on_your_device__Harman_will_delete_your_device_data_within_five_days__For_more_information_on_how_Harman_uses_your_information__please_review_Harman_s_privacy_policy__available_at___App_li">Harman is requesting access to upload logs from your device. If you grant Harman access, Harman will collect information about your device, such as current system logs and performance logs, and upload them to Harman servers. Harman accesses your device’s logs in order to debug and understand issues on your device. Harman will delete your device data within five days. For more information on how Harman uses your information, please review Harman’s privacy policy, available at https://www.harman.com/privacy-policy.\n\nDo you grant Harman permission to upload system logs on your  device [[holders]]?</string>
    <string name="jbl_DISABLE_REMOTE_DEBUG">DISABLE REMOTE DEBUG</string>
    <string name="jbl_Product_log_has_been_uploaded_">Product log has been uploaded.</string>
    <string name="jbl___holders___supports_Dolby_Atmos___When_there_is_Dolby_source_input__Dolby_Atmos_effect_will_be_automatically">[[holders]] supports Dolby Atmos.  When there is Dolby source input, Dolby Atmos effect will be automatically applied – giving you the truly immersive 3D surround sound for all your favorite entertainment.</string>
    <string name="jbl_Alexa__set_an_alarm_for_6_a_m__on_JBL_Soundbar_nAlexa__play_Taylor_Swift_on_JBL_Soundbar_nAlexa__nex">Alexa, set an alarm for 6 a.m. on ‘Product name’\nAlexa, play Taylor Swift on ‘Product name’\nAlexa, next on ‘Product name’\nAlexa, shuffle on ‘Product name’\n\nProduct name: e.g. JBL Bar 300</string>
    <string name="jbl_AirPlay_Is_Ready">AirPlay Is Ready</string>
    <string name="jbl_ENABLE_MORE_SERVICE">ENABLE MORE SERVICES</string>
    <string name="jbl_CALIBRATE">CALIBRATE</string>
    <string name="jbl_Chromecast_Built_In">Google Cast</string>
    <string name="jbl_ALLOW">ALLOW</string>
    <string name="jbl_ENABLE_ALEXA">ENABLE ALEXA</string>
    <string name="jbl_Available_for_iOS_devices">Available for iOS devices</string>
    <string name="jbl_Spotify_Connect">Spotify Connect</string>
    <string name="jbl_Qobuz_Connect">Qobuz Connect</string>
    <string name="jbl_DLNA">DLNA</string>
    <string name="jbl_Enabled">Enabled</string>
    <string name="jbl_Enable">Enable</string>
    <string name="jbl_Enabled_devices">Enabled devices</string>
    <string name="jbl_Equalizer">Equalizer</string>
    <string name="jbl_Wi_Fi">Wi-Fi</string>
    <string name="jbl_Dolby_Audio">Dolby Audio</string>
    <string name="jbl_No_content">No content</string>
    <string name="jbl_Play_____holders____">Play \"[[holders]]\"</string>
    <string name="jbl_Product_Model">Product Model</string>
    <string name="jbl_Build_date">Build Date</string>
    <string name="jbl_Software">Software</string>
    <string name="jbl_New_updat">New version available!</string>
    <string name="jbl_Dolby_Atmos">Dolby Atmos</string>
    <string name="jbl_Check_Wi_Fi">Check Wi-Fi</string>
    <string name="jbl_START">START</string>
    <string name="jbl_Tuning_finished">Tuning finished</string>
    <string name="jbl_Cancel_Calibration__">Cancel Calibration ?</string>
    <string name="jbl_Immersive_Audio_Demo">Immersive Audio Demo</string>
    <string name="jbl___Google_Terms_Of_Service__">\"Google Terms Of Service\"</string>
    <string name="jbl_Inactive">Inactive</string>
    <string name="jbl_With">With</string>
    <string name="jbl_Volume">Volume</string>
    <string name="jbl_Accept">Accept</string>
    <string name="jbl_ADD_PRODUCT_LATER">ADD PRODUCT LATER</string>
    <string name="jbl_GET_HELP">GET HELP</string>
    <string name="jbl_Turn_On_Bluetooth">Turn On Bluetooth</string>
    <string name="jbl_CONTINUE">CONTINUE</string>
    <string name="jbl_Security">Security</string>
    <string name="jbl_TURN_ON_BLUETOOTH">TURN ON BLUETOOTH</string>
    <string name="jbl_None">None</string>
    <string name="jbl_WEP">WEP</string>
    <string name="jbl_WPA">WPA</string>
    <string name="jbl_WPA_2">WPA 2</string>
    <string name="jbl_Up_to_date">Up To Date</string>
    <string name="jbl_You_are_all_set__nCurrent_version___holders__">You are all set.\nCurrent version [[holders]]</string>
    <string name="jbl_UPDATE_LATER">UPDATE LATER</string>
    <string name="jbl___JBL_Soundbar_xxx__">\"JBL Soundbar_xxx\"</string>
    <string name="jbl_TROUBLE_SHOOT">TROUBLESHOOT</string>
    <string name="jbl_CHANGE_NETWORK">CHANGE NETWORK</string>
    <string name="jbl_Language">Language</string>
    <string name="jbl_Support">Support</string>
    <string name="jbl_Feedback">Feedback</string>
    <string name="jbl_ACCEPT">ACCEPT</string>
    <string name="jbl_Version">Version</string>
    <string name="jbl_Soundbar">Soundbar</string>
    <string name="jbl_REMOVE">REMOVE</string>
    <string name="jbl_CANCEL">CANCEL</string>
    <string name="jbl_OK">OK</string>
    <string name="jbl_Minutes">Minutes</string>
    <string name="jbl_SELECT">SELECT</string>
    <string name="jbl_Exit_Setup__">Exit Setup ?</string>
    <string name="jbl_Chromecast_Built_In_Is_Ready">Google Cast Is Ready</string>
    <string name="jbl_Google_Home">Google Home</string>
    <string name="jbl_TURN_ON">TURN ON</string>
    <string name="jbl_RESTORE">RESTORE</string>
    <string name="jbl_REMOVE_THIS_PRODUCT__">REMOVE THIS PRODUCT</string>
    <string name="jbl_Favourite">Favourite</string>
    <string name="jbl_Enabled_music_service">Enabled music service</string>
    <string name="jbl_Available_music_service">Available music service</string>
    <string name="jbl_Bluetooth">Bluetooth</string>
    <string name="jbl_Location">Location</string>
    <string name="jbl_Calibration_Unavailable">Calibration Unavailable</string>
    <string name="jbl_KEEP">KEEP</string>
    <string name="jbl_Fail">Fail</string>
    <string name="jbl_Amazon_Alexa">Amazon Alexa</string>
    <string name="jbl_Retry">Retry</string>
    <string name="jbl_Thanks_for_your_feedback_">Thanks for your feedback!</string>
    <string name="jbl_SEND">SEND</string>
    <string name="jbl_Subject">Subject</string>
    <string name="jbl_Email">Email</string>
    <string name="jbl_CONFIRM">CONFIRM</string>
    <string name="jbl_AirPlay">AirPlay</string>
    <string name="jbl_Allow">Allow</string>
    <string name="jbl_Delete">Delete</string>
    <string name="jbl_RESET">RESET</string>
    <string name="jbl_Product_successfully_reboot__AirPlay_has_been_reset_">Product reboot successful, AirPlay has been reset.</string>
    <string name="jbl_Please_reconnect___holders___to_continue_configure_process">Please reconnect [[holders]] to continue configuring process.</string>
    <string name="jbl_Sorry__product_cannot_be_connected_at_the_moment__please_reconnect_">Sorry, product cannot be connected at the moment, please try again.</string>
    <string name="jbl_Password_length_needs_to_be_at_least_5_characters">Password length needs to be at least 5 characters.</string>
    <string name="jbl_RECONNECT_PRODUCT">RECONNECT PRODUCT</string>
    <string name="jbl_Edit_this_name_in_your_iPhone_Home_app">Edit this name using the Home App.</string>
    <string name="jbl_Rebooting____">Rebooting....</string>
    <string name="jbl_Reboot_success">Reboot success</string>
    <string name="jbl_Now_you_can_stream_favorite_music_to_your_JBL_products_through_AirPlay_">Now you can stream music to your JBL products via AirPlay.</string>
    <string name="jbl_Do_you_want_connect_to_____holders____">Do you want connect to \"[[holders]]\"</string>
    <string name="jbl_Discover_More">Discover More</string>
    <string name="jbl_If_you_want_to_enable_Google_Chromecast__Amazon_Alexa_or_other_streaming_service__visit_3rd_party_se">If you want to enable Google Chromecast/\nAmazon Alexa or other streaming services, visit the 3rd party service page to learn more.</string>
    <string name="jbl_LOGIN_ALEXA">LOG IN ALEXA</string>
    <string name="jbl_Here_Are_Some_Things_You_Can_Say">Here Are Some Things You Can Say</string>
    <string name="jbl_Setup_A_Preferred_Speaker">Setup A Preferred Speaker</string>
    <string name="jbl_Alexa_Preferred_Speaker_are_super_convenient_since_they_don_t_require_any_additional_commands_for_th">Alexa Preferred Speaker gives you the convenience of designating a preferred speaker to become the default music playback device. </string>
    <string name="jbl_Setup_Alexa_Multi_Room_Music_">Setup Alexa Multi-Room Music</string>
    <string name="jbl_Alexa_Multi_Room_Music_feature_lets_you_play_the_same_music_on_JBL_One_products_and_Alexa_enabled_sp">Alexa Multi-Room Music lets you play the same music on %s products and Alexa enabled speakers. </string>
    <string name="jbl_To_learn_more_and_access_additional_features__go_to_Amazon_Alexa_App_">To learn more and access additional features, go to Amazon Alexa App.</string>
    <string name="jbl_Open_Amazon_Alexa_App">Open Amazon Alexa App</string>
    <string name="jbl_Manage_streaming_services">Manage streaming services</string>
    <string name="jbl_Available_from_supported_music_App">Available from supported music App</string>
    <string name="jbl_Available_from_Amazon_music_App">Available from Amazon music App</string>
    <string name="jbl_Availale_from_Spotify_native_App">Available from Spotify native App</string>
    <string name="jbl_3rd_party_Service">3rd Party Service</string>
    <string name="jbl_Detach_rear_speakers_">Detach rear speakers.</string>
    <string name="jbl_Place_rear_speakers_in_audience_sitting_position_">Place rear speakers in audience sitting position.</string>
    <string name="jbl_Make_sure_rear_speakers_are_facing_the_soundbar_">Make sure rear speakers are facing the soundbar.</string>
    <string name="jbl_Available_devices">Available devices</string>
    <string name="jbl_If_you_come_across_issue_to_stream_via_AirPlay__please_press___REBOOT___button_below_to_reset__This_">If you’re having problems with streaming via AirPlay, please press \”REBOOT\” button below to reset.  This will take about 1 minute before you can stream music again. </string>
    <string name="jbl_Step_1_calibration_finished_">Step 1 calibration finished.</string>
    <string name="jbl_REBOOT">REBOOT</string>
    <string name="jbl_Place_rear_speakers_adjacent_to_the_regular_listening_area__">Place rear speakers adjacent to the regular listening area.</string>
    <string name="jbl_Rotate_45_degrees_for_the_best_listening_experience_">Rotate 45 degrees for the best listening experience.</string>
    <string name="jbl_Step_2_calibration_finished_">Step 2 calibration finished.</string>
    <string name="jbl_BASS">BASS</string>
    <string name="jbl_MID">MID</string>
    <string name="jbl_TREBLE">TREBLE</string>
    <string name="jbl_Product_cannot_be_connected_">Product cannot be connected.</string>
    <string name="jbl_No_connection">No connection</string>
    <string name="jbl_LOW">LOW</string>
    <string name="jbl_HIGH">HIGH</string>
    <string name="jbl_Rear_Speakers">Detachable Rear Speakers</string>
    <string name="jbl_Audio_Sync">Audio Sync</string>
    <string name="jbl_Rear_Channel">Rear Channel</string>
    <string name="jbl_Guide__Network_status__Info__Software_version">Guide, Network status, Info, Software version</string>
    <string name="jbl_Attached">Attached</string>
    <string name="jbl_Software_is_up_to_date">Software is up to date</string>
    <string name="jbl_Customer_service">Customer service</string>
    <string name="jbl_Remove_This_Product">Remove This Product ?</string>
    <string name="jbl_Rename_My_Product">Rename My Product</string>
    <string name="jbl_Network_Status">Network Status</string>
    <string name="jbl_Product_Info">Product Info</string>
    <string name="jbl_More_Audio_Settings">More Audio Settings</string>
    <string name="jbl_Customer_Service">Customer Service</string>
    <string name="jbl_Upload_Product_Log">UPLOAD PRODUCT LOG</string>
    <string name="jbl_Enable_Remote_Debug">ENABLE REMOTE DEBUG</string>
    <string name="jbl_How_To_Get_My_Product_Online">How To Get My Product Online</string>
    <string name="jbl_Reset_Product_Network">Reset Product Network</string>
    <string name="jbl_Power_On">Power On</string>
    <string name="jbl_Make_sure_your_mobile_phone_is_connected_to_the_same_Wi_Fi_as_the_product_and_the_Wi_Fi_router_is_wo">Make sure your mobile phone is connected to the same Wi-Fi network as the product and the Wi-Fi router is working as normal.</string>
    <string name="jbl_If_the_product_is_still_not_showing_up__please_reset_the_network_for_your_product_">If the product still cannot be connected, please reset the Wi-Fi network for your product</string>
    <string name="jbl_Make_sure_product_is_in_operation_mode_">Make sure product is in operation mode.</string>
    <string name="jbl_Reset_Network">Reset Network</string>
    <string name="jbl_SAVE">SAVE</string>
    <string name="jbl_Audio_Calibration">Audio Calibration</string>
    <string name="jbl_Keeping_Quiet">Keeping Quiet</string>
    <string name="jbl_Re_Calibrate_Product_When_Position_Changes">Re-Calibrate Product When Position Changes</string>
    <string name="jbl_Sound_Warning">Sound Warning</string>
    <string name="jbl_Do_not_make_loud_noises_or_walk_in_front_of_your_speaker_during_the_calibration_process_">Do not make loud noises or walk in front of your speaker during the calibration process.</string>
    <string name="jbl_Tuning____">TUNING ...</string>
    <string name="jbl_TRY_AGAIN">TRY AGAIN</string>
    <string name="jbl_If_you_change_the_environment_or_the_position_of_the_products_in_future__please_re_calibrate_">If you change the environment or the position of the products in future, please re-calibrate.</string>
    <string name="jbl_Let_s_start_the_amazing_music_journey_">Let\'s start the amazing music journey!</string>
    <string name="jbl_I_HAVE_PLACE_THEM">I HAVE PLACE THEM</string>
    <string name="jbl_Step_2__Move_Rear_speakers">Step 2: Move Rear speakers</string>
    <string name="jbl_Now_please_put_the_rear_speakers_where_you_want_them_to_be_placed_around_the_listening_area_">Now please put the rear speakers where you want them to be placed around the listening area.</string>
    <string name="jbl_Step_1__Place_Rear_speakers">Step 1: Place Rear speakers</string>
    <string name="jbl_Please_take_the_2_rear_speakers_off_from_soundbar_and_put_them_on_the_daily__listening_spot_next_to_">Please undock the detachable rear speakers and place them in your preferred listening position.\n\nDistance between soundbar and speakers should be 3 ~ 5 meters.</string>
    <string name="jbl_My_Products">My Products</string>
    <string name="jbl_Avoid_Obstacles">Avoid Obstacles</string>
    <string name="jbl_Check_for_Update">Check For Update</string>
    <string name="jbl_Do_not_block_soundbar__rear_speakers_or_put_them_too_close_to_wall_">Do not block soundbar / detachable rear speakers or put them too close to the wall. </string>
    <string name="jbl_Updating">Updating</string>
    <string name="jbl_Please_undock_both_rear_speakers_and_make_sure_you_have_placed_them_according_to_guide_">Please undock both rear speakers and make sure you have placed them according to the guide.</string>
    <string name="jbl_Listen_to_your_favourite_music_by_pressing___on_the_remote_control__">Listen to your favourite music by pressing^^^on the remote control :</string>
    <string name="jbl_To_continue_using_this_app__please_accept__n_n__End_User_License_Agreement_n__Harman_Privacy_Stateme">To continue using this app, please accept:\n\n· End User License Agreement\n· Harman Privacy Statement.</string>
    <string name="jbl_Add_Music">Add Music</string>
    <string name="jbl_You_cannot_use_the_JBL_One_app_for_product_control_and_other_service_if_do_not_accept_the_Terms_and_">You cannot use the %s app for product control and other services if you do not accept the Terms and Statement. Please accept to continue.</string>
    <string name="jbl_To_continue_using_this_app__please_agree_to_the_following_terms_">To continue using this app, please agree to the following terms.</string>
    <string name="jbl_GRANT">GRANT</string>
    <string name="jbl_Put_your_speaker_in_setup_mode_">Put your product in setup mode.</string>
    <string name="jbl_Sorry__we_are_unable_to_find_your_product__please_reset_and_try_again_">Sorry, we are unable to find your product, please reset and try again.</string>
    <string name="jbl_Please_grant_Permissions_to_put_JBL_products_in_discovery_mode_and_connection_">Please grant permission to put product in discovery mode.</string>
    <string name="jbl_Press_and_hold_VOL____VOL____SOURCE_button_at_the_same_time_until_you_see_status_LED__start_breathin">Press and hold VOL- &amp; VOL+ &amp; SOURCE buttons at the same time until LED flashes (hold for about 5 seconds).</string>
    <string name="jbl_No_personal_information_will_be_collected_">No personal information will be collected.</string>
    <string name="jbl_Local_Network">Local Network</string>
    <string name="jbl_Trigger_Your_product">Trigger Your Product</string>
    <string name="jbl_Bluetooth_Permission">Bluetooth Permission</string>
    <string name="jbl_Press_the_button_on_the_tone_playing_speaker_to_continue_">Press the button on the tone-playing speaker to continue.</string>
    <string name="jbl_Location_Permission">Location Permission</string>
    <string name="jbl_Your_mobile_phone_is_connected_to_your_speaker_">Your mobile phone is now connected to your speaker.</string>
    <string name="jbl_Connected">Connected</string>
    <string name="jbl_To_find_and_connect_to_devices_on_your_local_network__please_grant___Local_network___permission_in_A">Please go to Settings to grant \"Local network\" permission to put product in discovery mode.</string>
    <string name="jbl_Please_connect_your_phone_to_Wi_Fi_to_continue_setting_up_your_speaker_">Please connect your phone to Wi-Fi to continue setting up your speaker.</string>
    <string name="jbl_To_easily_connect_your_product__please_grant___Bluetooth___Permission_in_App_settings_">Please go to Settings to grant \"Bluetooth\" permission to put product in discovery mode.</string>
    <string name="jbl_GO_TO_SETTINGS">GO TO SETTINGS</string>
    <string name="jbl_To_easily_connect_your_product__please_grant___Location___Permission_in_App_settings_">Please go to Settings to grant \"Location\" permission to put product in discovery mode.</string>
    <string name="jbl_Not_Connected">Not Connected</string>
    <string name="jbl_location_is_required_to_setup_new_product">Location is required to set up new product</string>
    <string name="jbl_Change_Network">Change Network</string>
    <string name="jbl_Enter_password_to_connect_your_speaker_to_network_____holders_____">Enter password to connect your speaker to the network \"[[holders]]\".</string>
    <string name="jbl_Go_to_control_center_and_turn_on_Bluetooth_to_setup_JBL_One_product_">Go to control center and turn on Bluetooth to set up product.</string>
    <string name="jbl_Choose_your_Wi_Fi_Network_">Choose your Wi-Fi Network.</string>
    <string name="jbl_Discovering_product___">Discovering product ...</string>
    <string name="jbl_grant_permission_to_discover_nearby_product">Grant permission to discover nearby %s products.</string>
    <string name="jbl_turn_on_bluetooth_to_discover_nearby_products">Turn on Bluetooth to discover nearby %s products.</string>
    <string name="jbl_CONNECT">CONNECT</string>
    <string name="jbl_Checking_the_latest_firmware__it_may_take_20_seconds_">Checking the latest firmware, it will take up to 20 seconds.</string>
    <string name="jbl_CHECKING">CHECKING</string>
    <string name="jbl_Your_product_is_up_to_date_">Your product is up to date.</string>
    <string name="jbl_Please_wait_while_the_connection_is_completed__This_might_take_a_minute_">Please wait until the connection is completed. This will take up to 1 minute.</string>
    <string name="jbl_Connecting">Connecting</string>
    <string name="jbl___holderd___MINUTE">[[holderd]] MINUTE</string>
    <string name="jbl_Wi_Fi_Connected">Wi-Fi Connected</string>
    <string name="jbl_Connect_Failed">Connection Failed</string>
    <string name="jbl_product_will_be_updated_to_a_latest_software_to_ensure_better_user_experience">Product will be updated to the latest software to ensure the best performance.</string>
    <string name="jbl_Sorry__we_are_unable_to_connect_your_speaker_to_Wi_Fi_">Sorry, we are unable to connect your speaker to Wi-Fi.</string>
    <string name="jbl_Change_Phone_Network">Change Phone Network</string>
    <string name="jbl_Sorry__we_are_not_able_to_connect_to_your_product_at_the_moment__please_restart_">Sorry, we are not able to connect to your product at the moment, please restart.</string>
    <string name="jbl_Select___JBL_Soundbar_xxx___in_the_list_of_networks_">Select \"JBL Soundbar_xxx\" in the list of networks.</string>
    <string name="jbl_Unsuccessful">Unsuccessful</string>
    <string name="jbl_Open_your_phone_settings_and_tap_on_Wi_Fi_">Open your mobile phone settings and tap on Wi-Fi.</string>
    <string name="jbl_Return_to_this_App_">Return to this App.</string>
    <string name="jbl_Start_OTA_Failed">Start OTA Failed</string>
    <string name="jbl_BACK_TO_DASHBOARD">BACK TO DASHBOARD</string>
    <string name="jbl_Manage_Services">Manage Services</string>
    <string name="jbl_Add_Music_Service">Add A Music Service</string>
    <string name="jbl_Connections">Connections</string>
    <string name="jbl_GET_STARTED">GET STARTED</string>
    <string name="jbl_App_preference">App Preference</string>
    <string name="jbl_Welcome">Welcome</string>
    <string name="jbl_Enjoy_the_immersive_audio_experience_provided_by_JBL_One_Platform_">Enjoy amazing sound and more control.</string>
    <string name="jbl_Terms___Agreements">Terms &amp; Agreements</string>
    <string name="jbl_FAQ">FAQ</string>
    <string name="jbl_Legal">Legal</string>
    <string name="jbl_App_version">App version</string>
    <string name="jbl_Settings">Settings</string>
    <string name="jbl_Open_Google_Home_app">Open Google Home App</string>
    <string name="jbl_No_Wi_Fi_Connection">No Wi-Fi Connection</string>
    <string name="jbl_More_Features">More Features</string>
    <string name="jbl_Please_connect_your_phone_to_Wi_Fi_for_more_product_discovery_and_control_">Please connect your mobile phone to Wi-Fi for more product discovery and control.</string>
    <string name="jbl_Add_Product">Add Product</string>
    <string name="jbl_Manage_Device">Manage Device</string>
    <string name="jbl_Only_compatible_speakers_listed_above_are_supported_by_this_app__We_are_continuously_working_on_supp">Only compatible speakers listed above are supported by this app. We are continuously working on supporting more products. Please visit JBL.com for more information about our speakers.</string>
    <string name="jbl_DISCOVER_PRODUCT">DISCOVER PRODUCT</string>
    <string name="jbl_You_re_ready_to_start_casting__Look_for_the_Cast_button_in_hundreds_of_apps_">You\'re ready to start casting. Look for the Cast button in hundreds of apps.</string>
    <string name="jbl_Connect_Product">Connect Product</string>
    <string name="jbl_Login_your_Alexa_account_to_start_using_Alexa_MRM_">Log into your Alexa account to start using Alexa MRM. </string>
    <string name="jbl_Enter_Wi_Fi_Setup_Mode">Enter Wi-Fi Setup Mode</string>
    <string name="jbl_Open_Spotify_App">Open Spotify App</string>
    <string name="jbl_Make_sure_product_is_powered_on_">Make sure product is powered on.</string>
    <string name="jbl_Press_and_hold_VOL__and_VOL__button_at_the_same_time_until_you_see_status_LED__start_breathing__abou">Press and hold VOL- and VOL+ buttons at the same time until LED flashes (hold for about 10 seconds).</string>
    <string name="jbl_Learn_More">Learn More</string>
    <string name="jbl_Reset_Product">Reset Product</string>
    <string name="jbl_Manual_add">Manually add</string>
    <string name="jbl_Restore_Factory_Settings">Restore Factory Setting ?</string>
    <string name="jbl_Restore_will_clear_all_your_personal_settings_on_this_product_and_reset_the_product_software_to_Out_">This will erase all your personal settings and reset the product software to its default factory settings.</string>
    <string name="jbl_Press_the_____button_on_the_tone_playing_speakejbl_Press_the_____button_on_the_tone_playing_speaker_to_continue_r_to_continue_">Press the ^^^ button on the tone-playing speaker to continue.</string>
    <string name="jbl_We_need_your_verification_on_the_speaker_to_continue_setup__Please_try_again_">We need your verification on the speaker to continue setup. Please try again.</string>
    <string name="jbl_Are_you_sure_you_want_to_remove_this_product_from_JBL_One_App___">Are you sure you want to remove this product from %s App ? </string>
    <string name="jbl_Action_Is_Required">Action Is Required</string>
    <string name="jbl_try_again">TRY AGAIN</string>
    <string name="jbl_Stop_Audio_In___holders__">Stop Audio In [[holders]]</string>
    <string name="jbl_Network_Name">Network Name</string>
    <string name="jbl_Sorry__your_connection_with_the_device_was_interrupted_unexpectedly__please_try_again">Sorry, your connection with the device was interrupted, unexpectedly, please try again.</string>
    <string name="jbl_Invalid_password_for___holders____please_retry_">Invalid password for \"[[holders]]\", please retry.</string>
    <string name="jbl_Your_speaker_is_connected_to_network_____holders_____successfully_">Your speaker is now connected to network \"[[holders]]\" .</string>
    <string name="jbl_Your_mobile_phone_couldn_t_connect_to_your_speaker_at_the_moment__let_s_try_something_else_">Your mobile phone could not connect to your speaker. Let\'s try something else.</string>
    <string name="jbl_Get_Spotify_Free">Get Spotify Free</string>
    <string name="jbl_Now_connect_this_phone_to_a_temporary_wireless_network_created_by_the_speaker_">Now connect this mobile phone to a temporary wireless network created by the speaker.</string>
    <string name="jbl_Control_product_from_here">Control products from here.</string>
    <string name="jbl_Add_music_service_here">Add music services here.</string>
    <string name="jbl_Manage_services_for_music_streaming">Manage music streaming services.</string>
    <string name="jbl_Assign_Moment_Shortcut_from_here">Assign Moment Shortcut from here</string>
    <string name="jbl_Chromecast_Built_In_Available">Google Cast Available</string>
    <string name="jbl_stream_music_from_hundreds_of_apps_to_your_products_via_chromecast_built_in">Stream music from hundreds of apps to your products via Google Cast.</string>
    <string name="jbl_Now_you_can_stream_music_to_your_JBL_products_via_Chromecast_built_in_">Now you can stream music to your JBL products via Google Cast.</string>
    <string name="jbl_Learn_more_from">Now you can stream music to your JBL products via Google Cast. Learn more from</string>
    <string name="jbl_If_you_want_to_enable_Amazon_Alexa_or_other_streaming_services__visit_the_3rd_party_service_page_to_">If you want to enable Amazon Alexa or other streaming services, visit the 3rd party service page to learn more.</string>
    <string name="jbl_ENABLE_OTHER_SERVICES">ENABLE OTHER SERVICES</string>
    <string name="jbl_Turn_on_Bluetooth_of_your_phone_to_discover_and_set_up_product_">Turn on Bluetooth of your mobile phone to discover and set up product.</string>
    <string name="jbl_Discovering">Discovering</string>
    <string name="jbl_Discovering_New_Product____">Discovering New Product ...</string>
    <string name="jbl_Dolby_Atmos_Level">Dolby Atmos Level</string>
    <string name="jbl_Auto_Off">Auto Off</string>
    <string name="jbl_Please_go_to_Settings_to_grant__Location___Nearby_devices__permission_to_put_product_in_discovery_mo">Please go to Settings to grant \"Location &amp; Nearby devices\" permission to put product in discovery mode.</string>
    <string name="jbl_Music___Content">Music &amp; Content</string>
    <string name="jbl_Setup_A_Product_to_start_your_music_journey_">Set up a product to start your music journey!</string>
    <string name="jbl_Setup">Setup</string>
    <string name="jbl_Remove_Music_Service_">Remove Music Service ?</string>
    <string name="jbl_Removing_Music_Service_will_also_log_you_out_if_this_service_from_JBL_One_App__please_confirm_">Removing this Music Service will also log you out of this service from %s App. Please confirm.</string>
    <string name="jbl_Please_make_sure_both_rear_speakers_are_undocked_and_wirelessly_connected_to_soundbar_">Please make sure both rear speakers are undocked and wirelessly connected to soundbar.</string>
    <string name="jbl_Music_Service_Login">Music Service Login</string>
    <string name="jbl_LOG_IN">LOG IN</string>
    <string name="jbl_Loading____">Loading....</string>
    <string name="jbl_Please_enter_the_subject_of_the_problem">Please enter the subject of the problem.</string>
    <string name="jbl_Login_unsuccessful">Login unsuccessful</string>
    <string name="jbl_Please_describe_the_problem_you_have_">Please describe the problem you have.</string>
    <string name="jbl_Hint">Hint</string>
    <string name="jbl_Enable_Chromecast_for_music_streaming">Enable Chromecast for music streaming.</string>
    <string name="jbl_Manage_AirPlay__Chromecast_built_in_or_other_3rd_party_streaming_services">Manage AirPlay, Google Cast or other 3rd party streaming services.</string>
    <string name="jbl_Invalid_Email_Address">Invalid Email Address</string>
    <string name="jbl_Network_Control">Network Control</string>
    <string name="jbl_Please_enter_a_valid_email_address_">Please enter a valid email address.</string>
    <string name="jbl_3rd_Party_Service">3rd Party Service</string>
    <string name="jbl_Home_Network">Home Network</string>
    <string name="jbl_Do_you_want_to_send_debug_log_">Do you want to send debug log?</string>
    <string name="jbl_Wi_Fi_Strength">Wi-Fi Strength</string>
    <string name="jbl_IP_Address">IP Address</string>
    <string name="jbl_MAC_Address">MAC Address</string>
    <string name="jbl_Your_confirmation_ID_is___holders__">Your confirmation ID is [[holders]]</string>
    <string name="jbl_Would_you_like_to_Sign_Out_">Would you like to Sign Out?</string>
    <string name="jbl_We_always_listen_to_you__our_users__nIf_you_have_any_feedback_to_help_us_improve_our_app__please_let">Your feedback will help us improve our services to you.</string>
    <string name="jbl_SIGN_OUT">SIGN OUT</string>
    <string name="jbl_Please_describe_your_problem_in_the_message_box_below_">Please describe your problem in the message box below.</string>
    <string name="jbl_The_name_of_device_is_empty_">The name of device is empty.</string>
    <string name="jbl_The_length_of_name_is_too_long">The length of name is too long.</string>
    <string name="jbl_Reset_AirPlay">Reset AirPlay</string>
    <string name="jbl_Only_numbers__letters_and_underscore_are_allowed">Only numbers, letters and underscore are allowed.</string>
    <string name="jbl_You_re_ready_to_start_streaming__Look_for_the_AirPlay_button_in_hundreds_of_apps_on_your_iOS_devices">You\'re ready to start streaming. Look for the AirPlay button in \'Control Centre\' on your iOS mobile phone.</string>
    <string name="jbl_Name_exists">Name exists</string>
    <string name="jbl_Cannot_Stream_Via_AirPlay_">Cannot Stream Via AirPlay?</string>
    <string name="jbl_Allow_to_access_your_location_while_you_are_using_the_app_">Allow to access your location while you are using the app?</string>
    <string name="jbl_If_you_come_across_issue_to_stream_via_AirPlay__please_press_below_button_to_reset_">If you cannot stream content with AirPlay, please press below button to reset. </string>
    <string name="jbl_APP_needs_to_find_your_Wi_Fi_network_as_location_information__This_app_doesn_t_collect_your_location">APP needs to find your Wi-Fi network as location information. This app doesn\'t collect your location using GPS or Wi-Fi based positioning technologies.</string>
    <string name="jbl_Select_Product">Select Product</string>
    <string name="jbl_Don_t_Allow">Don\'t Allow</string>
    <string name="jbl_RESET_AIRPLAY">RESET AIRPLAY</string>
    <string name="jbl_Reset_AirPlay_will_reboot_product__this_takes_about_1_minute_before_your_can_stream_music_again_">Reset AirPlay will reboot product, this will take about 1 minute before your can stream music again.</string>
    <string name="jbl_Please_connect_to_your_new_JBL_product">Please connect to your new JBL product.</string>
    <string name="jbl_To_stream_music__you_will_need_to_log_in_to_your_Amazon_Music_account_for_this_product_">To stream music, you will need to log into your [[holders]] account for this product.</string>
    <string name="jbl_This_app_will_be_able_to_discover_and_connect_devices_on_the_networks_you_use_">This app will be able to discover and connect devices on the networks you use.</string>
    <string name="jbl_Bluetooth_permissions_are_needed_to__find_and_connect_to_nearby_JBL_products__provide_diagnostics__a">Bluetooth permissions are needed to  find and connect to nearby JBL products, provide diagnostics, and access some configuration settings.</string>
    <string name="jbl_Check_for_Updates">Check For Updates</string>
    <string name="jbl_resetting_airplay_will_reboot_the_product">Resetting AirPlay will reboot the product. This will take about 1 minute before you can stream music again.</string>
    <string name="jbl_Product_reboot_successful_AirPlay_has_been_reset">Product reboot successful. AirPlay has been reset.</string>
    <string name="jbl_Cannot_connect_to_the_product_Please_try_again">Cannot connect to the product. Please try again.</string>
    <string name="jbl_We_are_unable_to_connect_to_your_product_Please_restart">We are unable to connect to your product. Please restart.</string>
    <string name="jbl_stream_music_to_products_via_airplay">Now you can stream music to your %s products via AirPlay</string>
    <string name="jbl_location_permissions_are_required_to_setup">Location permissions are required to set up and connect to your %s products reliably. We need these permissions so we can identify networks, access your network name, and find nearby %s products.</string>
    <string name="jbl_required_networks_discover_connect_device">This app will be able to discover and connect devices on the networks you use.</string>
    <string name="jbl_bluetooth_permissions_required_find_connect_products">Bluetooth permissions are needed to find and connect to nearby %s products, provide diagnostics, and access some configuration settings.</string>
    <string name="harmanbar_jbl_Your_mobile_phone_is_connected_to_your_speaker_">Your mobile phone is connected to your product.</string>
    <string name="harmanbar_jbl_Manage_Device">Manage Products</string>
    <string name="software_update_not_connected_sub_title">We could not connect to your product, please try to restart.</string>
    <string name="manage_service_for_music_streaming">Manage services for music streaming</string>

    <string name="getting_ready_for_wifi_streaming">Getting Ready for\nWi-Fi Streaming</string>
    <string name="streaming_with_airplay">Streaming with AirPlay</string>
    <string name="streaming_with_airplay_desc">1. Tap “Open Airplay” button below.\n2. Select a speaker from the list.\n3. Open music service app and start playback.</string>
    <string name="open_air_play">Open AirPlay</string>
    <string name="streaming_with_chromecast">Streaming with Chromecast</string>
    <string name="check_connection">Check Connection</string>
    <string name="check_connection_desc">Make sure the product is connected to Bluetooth or the same Wi-Fi network as your mobile device.</string>
    <string name="harmanbar_jbl_Reset_Product_desc">If the product still cannot be found, please reset product to connect.</string>
    <string name="reset_guide">Reset Guide</string>
    <string name="hdmi_source">HDMI Source</string>
    <string name="aux_source">AUX Source</string>
    <string name="choose_a_wireless_connection">Choose a Wireless Connection</string>
    <string name="make_sure_speaker_and_mobile_same_network">Make sure your speaker and mobile device are connected to the same Wi-Fi network.</string>
    <string name="pair_product">Pair product</string>
    <string name="go_to_bluetooth_in_setting">Go to “Bluetooth” in Settings and select JBL product from the available device list.</string>
    <string name="pairing">Pairing …</string>
    <string name="go_to_settings_to_connect">Go To Settings to CONNECT</string>
    <string name="your_device_is_compatible_with_the_International">Your device is compatible with the International Version of Alexa. Some features may not be available in your area.</string>
    <string name="cannot_rename">Cannot Rename</string>
    <string name="cannot_rename_desc">Renaming cannot be done since you have added product in Apple Home Kit.
Please customize the product name from Apple Home App.</string>
    <string name="new_available">New available</string>
    <string name="product_update">Product Update</string>
    <string name="do_not_turn_off_product_or_unplug_power_cable">Do NOT turn off product or unplug power cable</string>
    <string name="sorry_the_speaker_is_currently_offline">Sorry, the speaker is currently offline. Please connect the speaker and try again. </string>
    <string name="sorry_the_update_was_not_complete_no_changes_were_made">Sorry, the update was not complete. No changes were made. </string>
    <string name="go_back_to_dashboard">go back to dashboard</string>
    <string name="unable_to_connect_to_server_make_sure_your_product_is_connected">Unable to connect to server, please make sure your product is connected to Wi-Fi network and try again later. </string>
    <string name="multi_channel_effect_msg">Upgrade your listening experience with multi-channel effect.</string>
    <string name="not_now">NOT NOW</string>
    <string name="create_group_from_here">Create group from here.</string>
    <string name="learn_more">LEARN MORE</string>
    <string name="skip_calibration">SKIP CALIBRATION</string>
    <string name="primary_speaker_desc">Green LED is lighting up on the primary speaker. Moment button is only available from this speaker.\n\nIf you want to play local source music to the group, please connect to this product.</string>
    <string name="reconnecting_to_group">Reconnecting to Group…</string>
    <string name="products_cannot_be_connected_at_this_moment">Products cannot be connected at this moment.</string>
    <string name="do_you_want_to_exit_the_wifi_setup_process_now">Do you want to exit the Wi-Fi setup process now? You can connect product to Wi-Fi later from product card.</string>
    <string name="product_is_ready_to_use">Product is ready to use.
You can enjoy Wi-Fi streaming with AirPlay now.</string>
    <string name="create_multi_room_with_airplay">Create Multi-Room with AirPlay</string>
    <string name="make_sure_your_speakers_and_mobile_device_are_connected_to_the_same_wifi_network">Make sure your speakers and mobile device are connected to the same Wi-Fi network.</string>
    <string name="tap_open_airplay_button_below">Tap “Open Airplay” button below.
Select all the speakers that you want to play the current audio on.</string>
    <string name="make_sure_your_speakers_and_mobile_device_google_home_app">Make sure your speakers and mobile device are connected to the same Wi-Fi network. Google Home app is needed to enable this feature.</string>
    <string name="go_to_google_home_app_and_tap_media">Go to the Google Home app and tap “Media”.
Tap “Create Group” and select the speakers to be included, then save.
Open music service app and start streaming on the group. </string>
    <string name="product_is_now_a_standalone_speaker">Product is now a standalone speaker.</string>
    <string name="product_belongs_to_groupname">Product Belongs to #groupname</string>
    <string name="your_products_will_automatically_regroup">Your products will automatically regroup when the other speaker is detected. Before that, you can use it as a standalone speaker.</string>
    <string name="want_to_quit_the_group">Want to quit the group?</string>
    <string name="go_to_track_radio">Go to Track Radio</string>
    <string name="artist_radio">Artist Radio</string>

    <string name="added_by_ui_team">Support for other products</string>
    <string name="go_to_jbl_portable_to_unlock">Go to JBL Portable to unlock all features  of your product.</string>
    <string name="go_to_jbl_partybox_to_unlock">Go to JBL Partybox to unlock all features  of your product.</string>
    <string name="go_to_jbl_bar_to_unlock">Go to JBL BAR Setup to unlock all features  of your JBL Bar 5.1</string>
    <string name="go_to_wifi_setting_to_set_jbl_bar">Go to “Wi-Fi” in Settings to set up your JBL Bar 9.1</string>
    <string name="go_to_google_home_to_set_jbl_bar">Go to Google Home to unlock all features  of your JBL Bar 9.1</string>
    <string name="bluetooth_reconnection_preference">Bluetooth Reconnection Preference</string>
    <string name="connected_with_others">Connected with Others</string>
    <string name="to_proceed_with_product_control">To proceed with product control, please connect to Wi-Fi and update the software.</string>


    <!--Added on 2023.04.21-->
    <string name="jbl_Calibration_takes_about_1_minute__A_loud_sound_will_come_from_your_products_during_calibration_">A loud sound will come from your product during calibration.</string>
    <string name="jbl_NOT_ACCEPT">DO NOT ACCEPT</string>
    <string name="jbl_NOT_ALLOW">DO NOT ALLOW</string>
    <string name="jbl_Calibration_Takes_About_3_Minute">Calibration takes about 3 minutes</string>
    <string name="jbl_If_you_change_the_environment_or_the_position_of_the_products_in_the_future__please_re_Calibrate_">If you change the environment or the position of the product in future, please re-calibrate.</string>
    <string name="jbl_HAVE_A_TRY">HEAR IT NOW</string>
    <string name="jbl_To_Calibrate_later__find_the__Calibration__option_from_product_control_page_">To calibrate later, find the [Calibration] option in product control card.</string>
    <string name="jbl_Grant_Permission_In_Settings">Set up</string>
    <string name="jbl_Set_up_Wi_Fi">Connect to Wi-Fi</string>
    <string name="jbl_turn_on_bluetooth_and_connect_wifi_to_discover_nearby_products">Turn on Bluetooth and connect to Wi-Fi to discover nearby products.</string>
    <string name="jbl_Sorry__there_was_a_problem_updating_your_product__No_changes_were_made_">An error occurred updating your product. No changes were made.</string>
    <string name="jbl_do_you_want_to_exit_the_set_up_process_now_you_can_do_it_later_in_add_product_page">Do you want to exit the setup process now? You can do it later from the Add Product tab.</string>
    <string name="jbl_Allow__JBL_One_App__to_Turn_on_bluetooth_">Allow \"%s\" to Turn on Bluetooth?</string>
    <string name="jbl_Location_permissions_are_required_to_setup_and_connect_to_your_JBL_products_reliably__We_need_these_">Location permissions are required to set up and connect to your JBL products reliably. We need these permissions so that we can identify networks, access your network name, and find nearby JBL products.</string>
    <string name="jbl_Checking_the_latest_firmware_This_will_take_up_to_20_seconds">Checking for the latest firmware. This will take up to 20 seconds.</string>
    <string name="newStructure_No_Music_Is_Playing">No Music Is Playing</string>
    <string name="newStructure_Online">Online</string>
    <string name="newStructure_Offline">Offline</string>
    <string name="newStructure_Add_New_Product">Add New Product</string>
    <string name="newStructure_Product_Management">Product Management</string>
    <string name="newStructure_Please_grant_permission">Please grant permission to put product in discovery mode.</string>
    <string name="newStructure_Turn_on_Bluetooth">Turn on Bluetooth and connect to Wi-Fi to discover nearby JBL products.</string>
    <string name="newStructure_Press_and_hold_MOMENT_BLUETOOTH_10_seconds">Press and hold MOMENT &amp; BLUETOOTH buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="newStructure_Press_and_hold_MOMENT_PLAY_10_seconds">Press and hold MOMENT &amp; PLAY buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="newStructure_Enjoy_Music">Enjoy Music </string>
    <string name="in_app_music">In-App Music</string>
    <string name="newStructure_Product_Controls">Product Controls</string>
    <string name="newStructure_Guide_Network_status_Info_Software_version">Guide, Network status, Info, Software version</string>
    <string name="newStructure_LightControl">Light\nControl</string>
    <string name="newStructure_EffectLab">Effect\nLab</string>
    <string name="newStructure_Top_PanelSettings">Top Panel Settings</string>
    <string name="newStructure_Mic_Effect">Mic Effect</string>
    <string name="newStructure_Bassboost">Bassboost</string>
    <string name="newStructure_Deep">Deep</string>
    <string name="newStructure_Punchy">Punchy</string>
    <string name="newStructure_Your_product_to_get_the_best_sound">Let\'s calibrate your product to get the best sound!</string>
    <string name="newStructure_Control_and_play_music_from_this_card_here">Control and play music from here.</string>
    <string name="newStructure_Getting_Ready_for_Wi_Fi_Streaming">Getting Ready for\nWi-Fi Streaming</string>
    <string name="newStructure_Enjoy_audio_in_its_highest_quality">Enjoy audio in its highest quality and stream from your favorite music services without interruption over Wi-Fi.</string>
    <string name="newStructure_Streaming_with_AirPlay">Streaming with AirPlay</string>
    <string name="newStructure_Open_Airplay_below">1. Tap “Open AirPlay” button below.\n2. Select a speaker from the list.\n3. Open music service app and start playback.</string>
    <string name="newStructure_Open_AirPlay">Open AirPlay</string>
    <string name="newStructure_Other_Services_You_Can_Use_to_Stream_on_Your_Product">Other Services You Can Use to Stream on Your Product</string>
    <string name="newStructure_Ready_to_use_on_Spotify_App">Ready to play on Spotify App.</string>
    <string name="newStructure_Ready_to_use_on_Tidal_App">Ready to play on Tidal App.</string>
    <string name="newStructure_Ready_to_use_on_iOS_platform">Ready to play on iOS platform.</string>
    <string name="newStructure_Streaming_with_Chromecast">Streaming with Chromecast</string>
    <string name="newStructure_Open_music_service_string">1. Open music service app and start playback.\n2. Tap “Cast” button and select a speaker to stream.</string>
    <string name="newStructure_CONNECT_TO_WI_FI">CONNECT TO WI-FI</string>
    <string name="newStructure_Connect_to_Wi_Fi_to_start_streaming">Connect to Wi-Fi to start streaming and unlock more features.</string>
    <string name="newStructure_Connecting">Connecting...</string>
    <string name="newStructure_Connect_to_Wi_Fi_or_Bluetooth_to_start_playback">Connect to Wi-Fi or Bluetooth to start playback.</string>
    <string name="newStructure_Connect_to_Wi_Fi">Connect to Wi-Fi</string>
    <string name="newStructure_Initialize_Wi_Fi_connection_for_your_product">Initialize Wi-Fi connection for your product.</string>
    <string name="newStructure_Or">Or</string>
    <string name="newStructure_Pair_Bluetooth">Pair Bluetooth</string>
    <string name="newStructure_Follow_the_instruction_to_connect_with_mobile_device">Follow the instruction to connect with mobile device.</string>
    <string name="newStructure_Press_Bluetooth_Button">Press Bluetooth Button</string>
    <string name="newStructure_your_product_to_enter_pairing_mode">Press Bluetooth button on your product to enter pairing mode, and you will hear a feedback tone.</string>
    <string name="newStructure_I_PRESSED_BUTTON">I’VE PRESSED BUTTON</string>
    <string name="newStructure_Are_you_sure_you_want_to_remove_this_product">Are you sure you want to remove this product from %s App ? </string>
    <string name="newStructure_REMOVE">REMOVE</string>
    <string name="newStructure_How_to_Get_Product_Online">How to Get Product Online</string>
    <string name="newStructure_Power_on">Power on</string>
    <string name="newStructure_Make_sure_product_is_in_operation_mode">Make sure product is in operation mode.</string>
    <string name="newStructure_Check_Connection">Check Connection</string>
    <string name="newStructure_product_is_connected_to_Bluetooth">Make sure the product is connected to Bluetooth or the same Wi-Fi network as your mobile device.</string>
    <string name="newStructure_Reset_Product">Reset Product</string>
    <string name="newStructure_cannot_be_found_please_reset_product_to_connect">If the product still cannot be found, please reset product to connect.</string>
    <string name="newStructure_Reset_Guide">Reset Guide</string>
    <string name="newStructure_supports_Dolby_Atmos">%s supports Dolby Atmos. When there is Dolby source input, Dolby Atmos effect will be automatically applied – giving you the truly immersive 3D suround sound for all your favorite entertainment.</string>
    <string name="newStructure_No_music">No music</string>
    <string name="newStructure_HDMI_Source">HDMI Source</string>
    <string name="newStructure_AUX_Source">AUX Source</string>
    <string name="newStructure_Choose_a_Wireless_Connection">Choose a Wireless Connection</string>
    <string name="newStructure_Wi_Fi">Wi-Fi</string>
    <string name="newStructure_device_are_connected_to_the_same_Wi_Fi_network">Make sure your speaker and mobile device are connected to the same Wi-Fi network.</string>
    <string name="newStructure_Press_Bluetooth_button_on_your_product">Press Bluetooth button on your product to enter pairing mode, and you will hear a feedback tone.</string>
    <string name="newStructure_Pair_product">Pair product</string>
    <string name="newStructure_Settings_and_select_JBL_product_from_the_available">Go to “Bluetooth” in Settings and select JBL product from the available device list.</string>
    <string name="newStructure_Pairing">Pairing …</string>
    <string name="newStructure_Go_To_Settings_to_CONNECT">Go To Settings to CONNECT</string>
    <string name="newStructure_High_Fidelity_Music_Streaming">High Fidelity Music Streaming.</string>
    <string name="newStructure_90_million_songs_with_new_releases">90 million songs with new releases from today\'s most popular artists.</string>
    <string name="newStructure_Calming_music_to_harmonize_your_day">Calming music to harmonize your day.</string>
    <string name="newStructure_Music_From_Every_Angle">Music From Every Angle.</string>
    <string name="newStructure_Unlimited_high_quality_streaming">Unlimited high quality streaming.</string>
    <string name="newStructure_Music_Radio_and_Podcasts">Music, Radio, and Podcasts.</string>
    <string name="newStructure_Listen_to_thousands_of_radio_stations">Listen to thousands of radio stations that are streaming over the internet with vTuner.</string>
    <string name="newStructure_White_noise_rockwell_ventures">White noise rockwell ventures.</string>
    <string name="newStructure_Manage">Manage</string>
    <string name="newStructure_Removing_this_music_service">Removing this music service will also log you out of this service from %s. Please confirm.</string>
    <string name="newStructure_Start_casting_Look_for_the_Cast_button_in_hundreds_of_apps">You’re ready to start casting. Look for the Cast button in hundreds of apps.</string>
    <string name="newStructure_Works_with_OK_Google">Works with OK Google</string>
    <string name="newStructure_To_setup_voice_control_from_a_Google_Assistant">To setup voice control from a Google Assistant-enabled speaker and multiroom groups with other Chromecast-enabled speakers, download Google Home app.</string>
    <string name="newStructure_Send_Device_Usage_and_Crash_Reports_to_Google">Send Device Usage and Crash Reports to Google</string>
    <string name="newStructure_This_selection_does_not_affect_your_Google_Account">This selection does not affect your Google Account controls at &lt;My Account>.google.com</string>
    <string name="newStructure_Your_device_is_compatible_with_the_International_Version">Your device is compatible with the International Version of Alexa. Some features may not be available in your area.</string>
    <string name="newStructure_Setup_alexa_multi_room_music_Set_up_Alexa_Multi_Room_Music">Set up Alexa Multi-Room Music</string>
    <string name="newStructure_Alexa_Multi_Room_Music_lets_you_play_the_same_music">Alexa Multi-Room Music lets you play the same music on JBL products and other Alexa enabled speakers.</string>
    <string name="newStructure_Open_Tidal">Open Tidal</string>
    <string name="newStructure_Stream_your_favorite_music_seamlessly">Stream your favorite music seamlessly from the cloud straight to your devices.</string>
    <string name="newStructure_Cannot_Rename">Cannot Rename</string>
    <string name="newStructure_Rename_is_not_available_Renaming">Renaming cannot be done since you have added product in Apple Home Kit.
Please customize the product name from Apple Home App.</string>
    <string name="newStructure_New_available">New available</string>
    <string name="newStructure_Product_Update">Product Update</string>
    <string name="newStructure_Do_NOT_turn_off_product_or_unplug_power_cable">Do NOT turn off product or unplug power cable</string>
    <string name="newStructure_Sorry_the_speaker_is_currently_offline">Sorry, the speaker is currently offline. Please connect the speaker and try again. </string>
    <string name="newStructure_Sorry_the_update_was_not_complete_No_changes_were_made">Sorry, the update was not complete. No changes were made. </string>
    <string name="newStructure_UPDATE_LATER">UPDATE LATER</string>
    <string name="newStructure_go_back_to_dashboard">go back to dashboard</string>
    <string name="newStructure_Fail_connecting_to_server_Unable_to_connect_to_serve">Unable to connect to server, please make sure your product is connected to Wi-Fi network and try again later. </string>
    <string name="newStructure_Bluetooth_Reconnection_Preference">Bluetooth Reconnection</string>
    <string name="newStructure_Bluetooth_is_prioritized_for_streaming">Bluetooth is prioritized for streaming when both Bluetooth and Wi-Fi are connected. \n\nIf you use this speaker more often in Wi-Fi environment, it\'s recommended to keep auto Bluetooth reconnection OFF.</string>
    <string name="newStructure_Auto_Bluetooth_Reconnection">Auto Bluetooth Reconnection</string>
    <string name="newStructure_Product_will_be_reconnected_to_last_paired_mobile_when_power_on">Product will be reconnected to the last paired mobile when powered on.</string>
    <string name="newStructure_Upgrade_your_listening_experience_with_multi_channel_effect">Upgrade your listening experience with multi-channel effect.</string>
    <string name="newStructure_CREATE_GROUP">CREATE GROUP</string>
    <string name="newStructure_NOT_NOW">NOT NOW</string>
    <string name="newStructure_Create_group_from_here">Create group from here.</string>
    <string name="newStructure_Upgrade_your_listening_experience_with_stereo_effect">Upgrade your listening experience with stereo effect.</string>
    <string name="newStructure_2_identical_speakers_are_required">2 identical speakers are required</string>
    <string name="newStructure_Multi_Room">Multi-Room</string>
    <string name="newStructure_Play_music_in_any_room_or_every">Play music in any room or every room in the house from a central mobile device.</string>
    <string name="newStructure_LEARN_MORE">LEARN MORE</string>
    <string name="newStructure_Select_products">Select products</string>
    <string name="newStructure_Step_d">Step %d</string>
    <string name="newStructure_Checking_grouping_environment_product_status">Checking product status …</string>
    <string name="newStructure_Place_both_speakers_side_by_side_facing_towards_the_listener">Place both speakers side by side facing towards the listener.</string>
    <string name="newStructure_Communicating">Communicating …</string>
    <string name="newStructure_Cancel_grouping_will_take_about_20_seconds">Cancel grouping will take about 20 seconds. \nProducts will remain standalone speakers.</string>
    <string name="newStructure_STAY">STAY</string>
    <string name="newStructure_CANCEL_GROUPING">CANCEL GROUPING</string>
    <string name="newStructure_Please_Wait">Please Wait …</string>
    <string name="newStructure_Products_cannot_communicate_with_each_other_at_the_moment">Products cannot communicate with each other at the moment, please try again.</string>
    <string name="newStructure_Calibration_tone_will_come_from_your_speakers">Calibration tone will come from your speakers for space detection.</string>
    <string name="newStructure_Keep_quiet_and_do_not_block_your_speakers">Keep quiet and do not block your speakers during the process.</string>
    <string name="newStructure_START_CALIBRATION">START CALIBRATION</string>
    <string name="newStructure_CANNOT_DO_SKIP_CALIBRATION">SKIP CALIBRATION</string>
    <string name="newStructure_Calibrating">Calibrating …</string>
    <string name="newStructure_Immersive_Audio">Immersive Audio</string>
    <string name="newStructure_Which_product_is_lighting_up_the_green_LED_in_front_of_you_now">Which product is lighting up the green LED in front of you now ?</string>
    <string name="newStructure_LEFT">LEFT</string>
    <string name="newStructure_RIGHT">RIGHT</string>
    <string name="newStructure_Grouping_Successful">Grouping Successful!</string>
    <string name="newStructure_FEEL_THE_POWER">FEEL THE POWER</string>
    <string name="newStructure_Please_customize_product_name_from_Apple_Home_App">Rename is not available since you have added product into Apple Home Kit.
Please customize product name from Apple Home App.</string>
    <string name="newStructure_Your_Music_Start_From">Your Music Starts From</string>
    <string name="newStructure_Ungrouping_please_wait">Ungrouping \"%s\", please wait …</string>
    <string name="newStructure_Are_you_sure_you_want_to_ungroup">Are you sure you want to ungroup \"%s\" ? Products in this group will become standalone speakers.</string>
    <string name="newStructure_UNGROUP">UNGROUP</string>
    <string name="newStructure_Sorry_we_are_not_able_to_ungroup">Sorry, unable to un-group at the moment, please try again later.</string>
    <string name="newStructure_Speakers_in_the_Group">Speakers in the Group</string>
    <string name="newStructure_Identity">Identity</string>
    <string name="newStructure_Primary_Speaker">Primary Speaker</string>
    <string name="newStructure_Green_LED_is_lighting_up_on_the_primary_speake">Green LED is lighting up on the primary speaker. Moment button is only available from this speaker.\n\nIf you want to play local source music to the group, please connect to this product.</string>
    <string name="newStructure_Please_update_the_software_to_create_group">Please update the software of \"%s\" to create group.</string>
    <string name="newStructure_Please_connect_to_Wi_Fi_network_to_create_group">Please connect \"%s\" to Wi-Fi network to create group.</string>
    <string name="newStructure_Products_cannot_communicate">Products cannot communicate with each other, please restart products and retry.</string>
    <string name="newStructure_Reconnecting_to_Group">Reconnecting to Group…</string>
    <string name="newStructure_Products_cannot_be_connected_at_this_moment">Products cannot be connected at this moment.</string>
    <string name="newStructure_Can_not_Connect_Your_Product">Can\'t Connect Your Product?</string>
    <string name="newStructure_Disconnect_Others">Disconnect Others</string>
    <string name="newStructure_Disconnect_your_product_from_other_JBL_App">Disconnect your product from other JBL App.</string>
    <string name="newStructure_Restart_your_product">Restart your product</string>
    <string name="newStructure_Learn_how_to_turn_on_off_the_product_refer_to">Learn how to turn on/off the product, refer to </string>
    <string name="newStructure_Do_you_want_to_exit_the_Wi_Fi_setup">Do you want to exit the Wi-Fi setup process now? You can connect product to Wi-Fi later from product card.</string>
    <string name="newStructure_You_can_enjoy_Wi_Fi_streaming_with_AirPlay_now">Product is ready to use. \nYou can enjoy Wi-Fi streaming with AirPlay now.</string>
    <string name="newStructure_You_can_enjoy_Wi_Fi_streaming_with_Chromecast_now">Product is ready to use.\nYou can enjoy Wi-Fi streaming with Chromecast now.</string>
    <string name="newStructure_Supported_Music_Services">Supported Music Services</string>
    <string name="newStructure_Create_Multi_Room_with_AirPlay">Create Multi-Room with AirPlay</string>
    <string name="newStructure_Device_are_connected_to_the_same_Wi_Fi_network">Make sure your speakers and mobile device are connected to the same Wi-Fi network.</string>
    <string name="newStructure_Tap_Open_Airplay_button_below">Tap \"Open AirPlay\" button below.\nSelect all the speakers that you want to play the current audio on.</string>
    <string name="newStructure_Create_Multi_Room_with">Create Multi-Room with:</string>
    <string name="newStructure_Create_Multi_Room_with_ChromeCast">Create Multi-Room with ChromeCast</string>
    <string name="newStructure_Google_Home_app_is_needed_to_enable_this_feature">Make sure your speakers and mobile device are connected to the same Wi-Fi network. Google Home app is needed to enable this feature.</string>
    <string name="newStructure_Go_to_the_Google_Home_app_and_tap">Go to the Google Home app and tap “Media”.\nTap \"Create Group\" and select the speakers to be included, then save.\nOpen music service app and start streaming on the group.</string>
    <string name="newStructure_Product_is_now_a_standalone_speaker">Product is now a standalone speaker.</string>
    <string name="newStructure_Product_Belongs_to">Product Belongs to %s</string>
    <string name="newStructure_Your_products_will_automatically_regroup_when_the_other_speaker">Your products will automatically regroup when the other speaker is detected. Before that, you can use it as a standalone speaker.</string>
    <string name="newStructure_Want_to_quit_the_group">Want to quit the group?</string>
    <string name="newStructure_Connected_with_Others">Connected with Others</string>
    <string name="newStructure_Product_is_now_connected_with_another_phone">Product is now connected with another phone.</string>
    <string name="newStructure_Go_to_Track_Radio">Go to Track Radio</string>
    <string name="newStructure_Artist_Radio">Artist Radio</string>
    <string name="newStructure_Name">Name</string>
    <string name="newStructure_multi_Room_openAirplay">Open Control Center on your mobile and tap AirPlay icon.</string>
    <string name="newStructure_Select_all_the_speakers_that_you_want_to_play_the_current_audio_on">Select all the speakers that you want to play music to.</string>
    <string name="newStructure_multi_Room_openMusicService">Open music service app and start playback.</string>
    <string name="newStructure_multi_Room_CreateMulit_Room">Create Multi-Room in Google Home app</string>
    <string name="newStructure_multi_Room_GotoGH">Go to the Google Home app after connecting all products to Wi-Fi.</string>
    <string name="newStructure_multi_Room_CreateHome">Create a home</string>
    <string name="newStructure_multi_Room_CrateHome_skip">Create a home in the Google Home app following the prompts. If you already have a home, skip this step.</string>
    <string name="newStructure_multi_Room_Adddevice">Add device to room</string>
    <string name="newStructure_multi_Room_taplocal">Tap the local device you want to add.</string>
    <string name="newStructure_multi_Room_addtoroom">On the device control screen, choose ’Add to a room’ to continue.</string>
    <string name="newStructure_multi_Room_CreateGroup">Create a group</string>
    <string name="newStructure_multi_Room_TapButton">Tap “+”  button, choose “Create speaker group”.</string>
    <string name="newStructure_multi_Room_SelectDevice_you_want">Select devices you want to include, then save.</string>
    <string name="newStructure_multi_Room_PlayMusicToGroup">Play music to group</string>
    <string name="newStructure_multi_Room_OpenAnymusic">Open any music app and start playing</string>
    <string name="newStructure_multi_Room_selectyourgroup">Select your group from cast list</string>
    <string name="newStructure_connect_with_other">Connected with Others</string>
    <string name="newStructure_Now_connect_with_another_phone">This Product is now connected with another phone.</string>
    <string name="newStructure_Rename_This_Group">Rename This Group</string>
    <string name="newStructure_Channel_Assignment">Channel Assignment</string>
    <string name="newStructure_Channel_Group_Controls">Group Controls</string>
    <string name="newStructure_Status">Status</string>
    <string name="newStructure_No_response">No response</string>
    <string name="newStructure_Channel">Channel</string>
    <string name="newStructure_Secondary">Secondary</string>
    <string name="newStructure_Primary">Primary</string>
    <string name="newStructure_Default_Source">Default Source</string>
    <string name="newStructure_Do_NOT_turn_off_product">Do NOT turn off product or leave the app</string>
    <string name="newStructure_Do_NOT_Unplug_Power_Cable_product">Do NOT unplug power cable or leave the app</string>
    <string name="newStructure_Moment_Subject_Auto_off_String">It is a personalization feature that allows you to customize your favorite music track, auto-off timing and music volume.\n\nOnce setup, you can directly access it through the Moment button on product or in the App.</string>
    <string name="newStructure_AppSetting_Product_Support_String">Support</string>
    <string name="newStructure_jbl_Self_Tuning">Self-Tuning</string>
    <string name="newStructure_jbl_The_speaker_will_self_tune_to_give_you_the_best_sound_">No matter where you place your speaker, it automatically calibrates itself to give you the best sound quality for any space.</string>
    <string name="Music_Streaming_with_Tidal_Connect">Music Streaming with Tidal Connect</string>
    <string name="Bluetooth_Reconnection_Preference">Bluetooth Reconnection Preference</string>
    <string name="Ready_to_play_some_music_Google_assistant_cast">Ready to play some music? Tap the Cast button in hundreds of apps or ask Google Assistant to play music.</string>
    <string name="Google_Assistant_Settings">Google Assistant Settings</string>
    <string name="google_assistant_cast_update_default_music_manage_settings">Update your default music service and manage other settings.</string>
    <string name="My_Assistant_Activity">My Assistant Activity</string>
    <string name="View_and_manage_your_data">View and manage your data.</string>
    <string name="Go_to_Your_Assistant_Data">Go to Your Assistant Data</string>
    <string name="Enable_Google_Service_in_the_Google_Home_App">Enable Google Service in the Google Home App</string>
    <string name="Wi_Fi_Streaming">Wi-Fi Streaming</string>
    <string name="Stream_music_from_hundreds_of_apps_via_Chromecast_built_in">Stream music from hundreds of apps via Google Cast.</string>
    <string name="Activate_Google_Assistant_on_your_product">Activate Google Assistant on your product.</string>
    <string name="Amazon_Alexa_Service">Amazon Alexa</string>
    <string name="Music_Streaming_with_Amazon_MRM">Music Streaming with Amazon MRM</string>
    <string name="Create_Multi_Room_with_Alexa">Create Multi-Room with Alexa</string>
    <string name="Alexa_Multi_Room_Music_amazon_alexa">Alexa Multi-Room Music lets you play the same music on JBL products and Alexa enabled speakers.</string>
    <string name="Go_to_Alexa_App">Go to Alexa app</string>
    <string name="newStructure_redirection_support_for_other">Support for Other Products</string>
    <string name="new_nearby_product">New Nearby Product</string>
    <string name="newStructure_redirection">Go to %s to unlock all features  of your %s.</string>
    <string name="newStructure_open">OPEN</string>
    <string name="newStructure_redirection_download">DOWNLOAD</string>
    <string name="newStructure_redirection_GotoWifiSetting">Go to "Wi-Fi" in Settings to set up your JBL Bar 9.1</string>
    <string name="newStructure_redirection_gotoSettings">GO TO SETTINGS</string>
    <string name="newStructure_redirection_gotounlock">Go to %s to unlock all features  of your product.</string>
    <string name="newStructure_redirection_pleasePauseMusic">Please pause music playback before connecting to Wi-Fi.</string>
    <string name="jbl_Please_change_the_Wi_Fi_of_your_mobile_phone_to_network_____holders_____">Please make sure your mobile phone is connected to network \"%s\".</string>
    <string name="harmanbar_jbl_Plug_in_power_to_continue">Plug in power to continue</string>
    <string name="newStructure_Create_Your_Moment_Portable_String">1. Tap \"+\" to add your preferred music service.\n\n2. Sign in to your account.\n\n3. Tap the first track of your favorite playlist or radio station.\n\n4. Set desired auto-off time and volume, then tap \‘SAVE\’.\n\n5. From next time onwards, your favorite playlist / station will be accessible with the  %s   button press on your product.</string>
    <string name="send_crash_report_desc">This selection does not affect your Google Account controls at myaccount.google.com</string>
    <string name="create_multi_room_with_alexa_desc">Alexa Multi-Room Music lets you play the same music on JBL products and Alexa enabled speakers.</string>

    <string-array name="light_show_item">
        <item>Neon</item>
        <item>Loop</item>
        <item>Bounce</item>
        <item>Trim</item>
        <item>Switch</item>
        <item>Freeze</item>
    </string-array>
    <string-array name="array_dj_voice_title">
        <item>\"PARTY\"</item>
        <item>\"DANCE\"</item>
        <item>\"HANDSUP\"</item>
        <item>\"HEY\"</item>
        <item>\"YO\"</item>
    </string-array>
    <string-array name="party_pad_item">
        <item>Repeater</item>
        <item>LetsGo</item>
        <item>Filter</item>
    </string-array>

    <string name="str_123" translatable="false">1,2,3</string>
    <string name="text_new" translatable="false">New</string>
    <string name="text_tm" translatable="false">TM</string>
    <string name="party_pad_tm" translatable="false">PartyPad™</string>
    <string name="ready_to_play_some_music">“Ready To Play Some Music?”</string>
    <string name="to_continue_using_this_app_please_accept">To continue using this app, please accept:</string>

    <!--Update on 2023.08.08-->
    <string name="activate_voice_assistants">Activate Amazon Alexa and Google Assistant</string>
    <string name="activate_voice_assistants_desc">Now you can easily get help at home from either assistant with just your voice.</string>
    <string name="enable_wi_fi_streaming">Enable Wi-Fi streaming</string>
    <string name="enable_wi_fi_streaming_desc">Stream music from hundreds of apps with Google Cast™.</string>
    <string name="select_your_preferred_services">Select your preferred voice assistant</string>
    <string name="amazon_alexa_google_assistant">Amazon Alexa and Google Assistant</string>
    <string name="google_assistant_with_chromecast_built_in">Google Assistant with Google Cast</string>
    <string name="each_voice_service_will_be_set_up_separately">Each voice service will be set up separately.</string>
    <string name="with_chromecast_built_in">With Google Cast</string>
    <string name="you_can_always_make_changes_in_product_settings">You can always make changes in product settings.</string>
    <string name="your_voice_assistants_are_ready_just_say">Your voice assistants are ready,\njust say :</string>
    <string name="play_some_music">“Play some music”</string>
    <string name="turn_up_the_volume">“Turn up the volume”</string>
    <string name="stop_the_music">“Stop”</string>
    <string name="set_a_timer_for_15_minutes">“Set a timer for 15 minutes”</string>
    <string name="discover_more_things_you_can_ask_alexa_or_google_assistant_learn_more">Discover more things you can ask Alexa or Google Assistant. Learn More</string>
    <string name="enable_google_services_in_the_google_home_app">Enable Google Assistant to stream music with Google Cast.</string>
    <string name="chromecast_built_in_desc">Stream your favorite music, radio, or podcasts from your mobile device to your speaker.</string>
    <string name="get_hands_free_help_at_home_just_say_hey_google">Get hands-free help at home. Just say “Hey Google”.</string>
    <string name="alexa_is_ready_let_us_set_up_google_assistant_in_the_google_home_app">Let\'s set up Google Assistant with Google Cast in the Google Home app.</string>
    <string name="don_t_forget_to_return_to_the_jbl_one_app_for_audio_settings_when_you_re_done">Don\'t forget to return to the %s app for audio settings when you\'re done.</string>
    <string name="harmanbar_jbl_Are_you_sure_you_want_to_remove_this_group_from_JBL_One_App___">Are you sure you want to remove this group from %s App ?</string>
    <string name="multi_room_music">Multi-Room Music</string>
    <string name="multi_room_music_desc">Alexa Multi-Room Music lets you play the same music on JBL products and Alexa enabled speakers.</string>
    <string name="my_assistant_activity_desc">View and manage your data.</string>
    <string name="go_to_google_home_app_desc">Update your default music services and other settings for Google Assistant. </string>
    <string name="music_streaming_with_alexa_cast">Music Streaming with Alexa Cast</string>
    <string name="cast_music_to_your_speaker_from_the_amazon_music_app">Cast music to your speaker from the Amazon Music app.</string>
    <string name="go_to_amazon_alexa_app">Go to Amazon Alexa App</string>
    <string name="go_to_amazon_alexa_app_desc">Update your default music service and manage other settings for Alexa voice assistant.</string>
    <string name="try_saying">Try Saying</string>
    <string name="alexa_va_try_saying_desc">“Alexa, what\'s the weather?”\n“Alexa, set a timer for 20 minutes”\n“Alexa, play my Flash Briefing”</string>
    <string name="google_multi_room_music_desc">Group your speakers in the Google Home app to play the same music across multiple rooms.</string>
    <string name="light_show_picker">Light Picker</string>
    <string name="alexa_is_ready_try_saying">Alexa is ready on the product. \nTry saying :</string>
    <string name="alexa_is_ready_play_radio">“Alexa, play music”</string>
    <string name="alexa_is_ready_what_is_today_news">“Alexa, what’s today’s news”</string>
    <string name="what_is_the_weather">“Alexa, what’s the weather”</string>
    <string name="set_an_alarm_for_10_45_am">“Alexa, set an alarm for 10:45 am”</string>
    <string name="set_up_alexa_va_go_app">To learn more and access additional features, download the Amazon Alexa app</string>
    <string name="amazon_alexa_app">Amazon Alexa app</string>
    <string name="explore_more">Explore More</string>
    <string name="ready_to_setup">Ready to set up</string>
    <string name="learn_about">Learn more about</string>
    <string name="dual_voice_assistants">Your voice assistants </string>
    <string name="alexa">Alexa</string>
    <string name="are_you_sure">Are you sure ?</string>
    <string name="exit_google_assistant_setup_content">By exiting, Google Assistant will not be enabled on this product. You can always enable later in [Product Settings].</string>
    <string name="continue_setup">Continue setup ?</string>
    <string name="alexa_is_not_enabled_on_this_product">Alexa is not enabled on this product. Do you want to continue setting up Google assistant?</string>
    <string name="setup_google_assistant">Set up Google Assistant</string>
    <string name="setup_alexa">Set up Alexa</string>
    <string name="try_saying_this">Try Saying</string>
    <string name="try_saying_this_desc">\"Hey Google, play some music\"\n\"Hey Google, what’s the latest news?\"\n\"Hey Google, Set a timer for 15 minutes\"</string>
    <string name="voice_language">Voice Language</string>
    <string name="harmanbar_pandora_Account">Account</string>
    <string name="harmanbar_alexa_AMAZON_ALEXA_SETTINGS">Amazon Alexa Settings</string>
    <string name="start_of_request">Start of Request</string>
    <string name="end_of_request">End of Request</string>
    <string name="start_of_request_desc">Play an audible tone when you speak the Alexa wake word.</string>
    <string name="end_of_request_desc">Play an audible tone when you are finished speaking.</string>
    <string name="voice_settings">Voice Settings</string>
    <string name="voice_language_sub_title">We’ll recognize your accent when you say the wake word.</string>
    <string name="harmanbar_voice_assistant">Voice Assistant</string>
    <string name="harmanbar_google_assistant">Google Assistant</string>
    <string name="android_translated_amazon_alexa">Amazon Alexa</string>
    <string name="android_translated_audio_calibration">Audio Calibration</string>
    <string name="android_translated_product_management">Product Management</string>
    <string name="android_translated_add_new_product">Add New Product</string>
    <string name="android_translated_supported_services">Supported Services</string>
    <string name="android_translated_in_app_music">In-App Music</string>
    <string name="android_translated_top_panel_settings">Top Panel Settings</string>
    <string name="android_translated_mic_effect">Mic Effect</string>
    <string name="android_translated_enjoy_music">Enjoy Music</string>
    <string name="my_google_activity">My Google Activity</string>
    <string name="music_services">Music Services</string>
    <string name="google_account_controls">This selection does not affect your Google Account controls at myaccount.google.com.</string>
    <string name="text_available">Available</string>
    <string name="remove_this_group_uppercase">REMOVE THIS GROUP</string>
    <string name="group_settings">Group Settings</string>
    <string name="log_out">LOG OUT</string>
    <string name="product_software_update_available">Product software update available!</string>
    <string name="learn_about_simultaneous_voice_assistants">Learn about *** simultaneous voice assistants</string>
    <string name="your_voice_assistants">Learn more about\nYour voice assistants</string>
    <string name="how_to_create_multiroom_group">Set up Multi-Room Control</string>
    <string name="google_activity_link">Activity link</string>
    <string name="discover_more_things_you_can_ask_Alexa_or_Google_Assistant">Discover more things you can ask Alexa or Google Assistant.</string>
    <string name="play_music_by_qq_app">Play music from QQ Music app to speakers</string>
    <string name="airplay_streaming_description">1. Open Control Center on your mobile and tap AirPlay icon. \n2. Select a speaker from the list. \n3. Open music service app and start playback.</string>
    <string name="voice_assistants_ready_to_setup">Ready to set up \nVoice assistants</string>
    <string name="google_assistant_cast_chromecast_build_in">Ready to play some music? Tap the \"Google Cast\" button in hundreds of apps or ask Google Assistant to play music.</string>
    <string name="two_same">Please select only two of the same speakers for Stereo mode.</string>
    <string name="group_calibration">Group Calibration</string>
    <string name="eq_save">Save</string>
    <string name="citation_time_content">Once started, you cannot exit the process until calibration is finished.</string>
    <string name="browser_setup_wifi">Connect product to Wi-Fi to unlock Wi-Fi streaming feature.</string>
    <string name="create_multi_room_msg">Make sure your speakers and mobile device are connected to the same Wi-Fi network. Google Home app is needed to enable this feature.\n\n
1. Go to the Google Home app and add your speaker to the device list.
2. Tap “Media”, choose “Create Group” and select the speakers, then save.
3. Open music service app and start streaming to the group.</string>
    <string name="let_is_setup_google_Assistant_inGHAPP">Let\'s set up Google Assistant with Google Cast in the Google Home app.</string>
    <string name="setup_GVA_and_Chromecast">Set up Google Assistant with Google Cast service in the Google Home app.</string>
    <string name="chromecast_later_setup_desc">If you want to enable Google Cast later, select [Product Settings] from product card to continue.</string>
    <string name="harmanbar_moment_connect_wifi">CONNECT WI-FI</string>
    <string name="harmanbar_self_turning_sub_title">No matter where your speaker is placed, it automatically calibrates itself to give you the best sound quality for any space.</string>
    <string name="go_to_alexa_app">Go to Alexa app</string>
    <string name="get_tidal_free">Get Tidal Free</string>
    <string name="enable_cast_built_in">Enable Google Cast</string>
    <string name="software_update_desc">To proceed with product control, please connect to Wi-Fi and update the software.</string>
    <string name="stream_service_qplay">QPlay</string>
    <string name="citation">Citation</string>
    <string name="update_your_default_music_service">Update your default music services and other settings for Google Assistant. To log out, remove this device from the Google Home app.</string>
    <string name="harmanbar_NewPlayView_Off">Off</string>
    <string name="jbl_on">On</string>
    <string name="jbl_setup_moment">Setup Moment</string>
    <string name="jbl_edit">EDIT</string>
    <string name="would_you_like_to_log_out">Would you like to log out ?</string>
    <string name="after_logging_out_alexa">After logging out, Alexa will no longer be accessible on this product. </string>
    <string name="after_logging_out_google_assistant">After logging out, Google assistant will no longer be accessible on this product.</string>
    <string name="alexa_built_in">Alexa Built-in</string>
    <string name="to_setup_voice_control_from_an_alexa_enabled">To setup voice control from an Alexa-enabled speaker and multiroom groups with other Alexa MRM speakers, download Amazon Alexa app.</string>
    <string name="works_with_ok_google">Works with OK Google</string>
    <string name="to_setup_voice_control_from_a_google_assistant">To setup voice control from a Google Assistant-enabled speaker and multiroom groups with other Chromecast-enabled speakers, download Google Home app.</string>
    <string name="set_up_google_assistant_get_chromecast_built_in">Set up Google Assistant in the Google Home app to get Google Cast.</string>
    <string name="continue_setup_not_question_remark">CONTINUE SETUP</string>
    <string name="google_assistant_activity">Google Assistant Activity</string>
    <string name="light_show_controller">Light Control</string>
    <string name="light_show_knob_desc">You can choose to show or hide light shows on device. Only the light shows shown can be toggled through the lighting knob on your speaker.</string>
    <string name="light_control_bounce">BOUNCE</string>
    <string name="light_control_neon">NEON</string>
    <string name="light_control_loop">LOOP</string>
    <string name="light_control_switch">SWITCH</string>
    <string name="light_control_trim">TRIM</string>
    <string name="light_control_freeze">FREEZE</string>
    <string name="go_to_google_home_app">Go to Google Home app</string>
    <string name="my_partyPad">My PartyPad</string>
    <string name="my_party_pad">MY PARTYPAD</string>
    <string name="by_jbl_official">By JBL Official</string>
    <string name="by_tigerlily">By Tigerlily</string>
    <string name="party_pad_effect">Effect</string>
    <string name="party_pad_voice">Voice</string>
    <string name="party_pad_tone">Tone</string>
    <string name="party_pad">PartyPad</string>
    <string name="create_your_own">CREATE YOUR OWN</string>
    <string name="light_show_knob_last_uncheck_tips">We are not able to hide this light show from device lighting knob, it’s the only one assigned.</string>
    <string name="enable_services_to_enjoy_more">Enable services to enjoy more</string>
    <string name="effect_lab">Effect Lab</string>
    <string name="effect">Effect</string>
    <string name="lab">Lab</string>
    <string name="color_loop">Color Loop</string>
    <string name="color_picker">Color Picker</string>
    <string name="dj_sound">DJ Sound</string>
    <string name="dj_effect">DJ Effect</string>
    <string name="pad_1">Pad 1</string>
    <string name="pad_2">Pad 2</string>
    <string name="pad_3">Pad 3</string>
    <string name="dj_effect_repeater">REPEATER</string>
    <string name="dj_effect_filter">FILTER</string>
    <string name="dj_effect_echo">ECHO</string>
    <string name="dj_effect_gater">GATER</string>
    <string name="dj_effect_wipeout">WIPEOUT</string>
    <string name="light_show">Light Show</string>
    <string name="light_show_knob">Light Show Knob</string>
    <string name="tidal_connect">Tidal Connect</string>
    <string name="product_software">Product Software</string>
    <string name="unavailable">Unavailable</string>
    <string name="still_cannot_discover">Still cannot discover?</string>
    <string name="multi_room_amazon_mrm">Amazon MRM</string>
    <string name="jbl_soundbar_series">JBL Soundbar Series</string>
    <string name="stereo">Stereo</string>
    <string name="place_2_speakers">Place both speakers side by side</string>
    <string name="make_sure">Make sure both speakers are facing forward</string>
    <string name="calibrate_finish">Calibration finished</string>
    <string name="have_a_try">PLEASE TRY</string>
    <string name="str_repeater_lowercase">Repeater</string>
    <string name="str_filter_lowercase">Filter</string>
    <string name="str_gater_lowercase">Gater</string>
    <string name="str_wipeout_lowercase">WipeOut</string>
    <string name="str_horn_lowercase">Horn</string>
    <string name="str_game_lowercase">Game</string>
    <string name="str_like_lowercase">Like</string>
    <string name="str_boo_lowercase">Boo</string>
    <string name="str_scratch_1_lowercase">Scratch 1</string>
    <string name="str_scratch_2_lowercase">Scratch 2</string>
    <string name="str_scratch_3_lowercase">Scratch 3</string>
    <string name="str_clap_lowercase">Clap</string>
    <string name="str_hands_lowercase">Hands</string>
    <string name="str_hey_lowercase">Hey</string>
    <string name="str_party_lowercase">Party</string>
    <string name="str_jump_lowercase">Jump</string>
    <string name="str_echo">Echo</string>
    <string name="str_bass">Bass</string>
    <string name="str_treble">Treble</string>
    <string name="step_1">Step 1</string>
    <string name="step_2">Step 2</string>
    <string name="step_3">Step 3</string>
    <string name="rotate_your_phone">Rotate your phone</string>
    <string name="insert_the_mic_to_continue">Insert the mic to continue</string>
    <string name="multi_channel_Upgrade_your_listening_experience_with_stereo_or_multichannel_effect">Upgrade your listening experience with stereo or multi-channel effect.</string>
    <string name="multi_channel_More_speakers_are_required">More speakers are required</string>
    <string name="multi_channel_Immersive_">Immersive %s</string>
    <string name="multi_channel_Dont_see_the_group_you_want">Don\'t see the group you want?</string>
    <string name="multi_channel_For_better_acoustics_performance_not_all_products_can_be_grouped_as_an_immersive_system_You_can_still_play_music_on_these_products_using_AirPlay_ChromeCast">For better sound performance, not all products can be grouped as an immersive system. You can still play music on these products using AirPlay / ChromeCast.</string>
    <string name="multi_channel_Recommended_For_You">Recommended for You</string>
    <string name="multi_channel_Also_Available">Also Available</string>
    <string name="multi_channel_There_are_multiple_products_please_check_">There are multiple products, \nplease check :</string>
    <string name="multi_channel_Choose__product">Choose %s product:</string>
    <string name="multi_channel_Grouping_">Grouping...</string>
    <string name="multi_channel_Grouping_Successful">Grouping Successful</string>
    <string name="multi_channel_Name_Your_Group_">Name Your Group</string>
    <string name="multi_channel_Confirm">Confirm</string>

    <string name="placement_reminderString_frontLR">Place %s side by side in front, facing towards the listener.</string>
    <string name="placement_reminderString_subwoofer">Place %s  on the floor, facing towards the listener.</string>
    <string name="placement_reminderString_center">Place %s in front, facing towards the listener.</string>
    <string name="placement_reminderString_sideLR">Place %s in the back, facing towards the listener.</string>
    <string name="multi_channel_There_are_multiple_products_please_choose__to_continue_">There are multiple products, please choose %s to continue :</string>
    <string name="multi_channel_Sound_Performance">Sound Performance</string>
    <string name="multi_channel_Immersiveness">Immersiveness</string>
    <string name="multi_channel_Loudness">Loudness</string>
    <string name="multi_channel_Products_used_in_this_group">Products used in this group</string>
    <string name="multi_channel_Subwoofer">Subwoofer</string>
    <string name="multi_channel_Center">Center</string>
    <string name="multi_channel_Rear_left">Rear left</string>
    <string name="multi_channel_Rear_right">Rear right</string>
    <string name="multi_channel_set_up">Set Up</string>
    <string name="multi_channel_previous">Previous</string>
    <string name="multi_channel_next">Next</string>
    <string name="multi_channel_Immersive">Immersive</string>
    <string name="multi_channel_nou_try_out">Now try out how immersive your speaker system is</string>
    <string name="multi_channel_check_the_guide">Check the Guide</string>
    <string name="multi_channel_Bass">Bass</string>
    <string name="multi_channel_calibrating_tip">Speakers will be calibrated for better performance during grouping</string>
    <string name="multi_channel_green_led">green LED</string>
    <string name="multi_channel_calibrate_speakers">Calibrate speakers for better performance</string>
    <string name="multi_channel_battery">Battery</string>
    <string name="multi_channel_calibration_successful">Calibration Successful</string>
    <string name="eq_default">Default</string>
    <string name="audio_tones">Audio Tones</string>
    <string name="audio_tones_desc">You will hear a tone when activating or deactivating features.</string>
    <string name="auto_power_off">Auto-Power Off</string>
    <string name="auto_power_off_desc">When not connected to power source, product will automatically turn off if no music is playing.</string>
    <string name="roon_ready">Roon Ready</string>
    <string name="a_platform_for_all_your_usic">A Platform for All Your Music</string>
    <string name="roon_ready_sub_desc">Browsing, discovering, collecting and listening to music with Roon.</string>
    <string name="go_to_roon_remote_app">Go to Roon Remote app</string>
    <string name="minute_format">%s minutes</string>
    <string name="hour_format">%s hours</string>
    <string name="updating_language_dialog_title">Updating Your Language May Take A While.</string>
    <string name="updating_language_dialog_content">You can continue using your current language during this update. Alexa will notify you when the new language is ready.</string>
    <string name="jbl_Press_the_____button_on_the_front_right_speaker_to_continue_">Press %s button on the front right speaker to continue.</string>
    <string name="We_need_your_verification_on_the_speaker_to_continue_setup__Please_try_again_">We need your verification on the speaker to continue setup. Please try again.</string>
    <string name="verified">Verified</string>
    <string name="your_products_are_about_to_start_grouping">Your products are about to start grouping...</string>
    <string name="rename_network_support_service">Rename, Network, Supported Service</string>
    <string name="rename_network_voice_assistant">Rename, Network, Voice assistant </string>
    <string name="product_information">Product Information</string>
    <string name="serial_number_software_version_guide">Serial number, Software version, Guide</string>
    <string name="product_usage">Product Usage</string>
    <string name="total_power_on_time">Total Power-on Time</string>
    <string name="total_playback_time">Total Playback Time</string>
    <string name="getting_ready_for">Getting Ready For</string>
    <string name="i_press">I\'ve Pressed it,BACK TO DASHBOARD</string>
    <string name="by_jbl_signature">JBL SIGNATURE</string>
    <string name="begin_the_party">BEGIN THE PARTY</string>
    <string name="boost_the_party">BOOST THE PARTY</string>
    <string name="non_stop_the_party">NON-STOP PARTY</string>
    <string name="create_a_group">Create A Group</string>
    <string name="seconary_info">Green LED is lighting up on the primary speaker. If you want to play local source music to the group, please connect to this product.</string>
    <string name="switch_channel">Switch Channel</string>
    <string name="tap_info">Tap on L / R button to check speaker channel.Tap Switch button to switch Left / Right Channel.</string>
    <string name="light_show_knob_tips">Switch below button to change knob sequence</string>
    <string name="customize_party_pad">Customize PartyPad</string>
    <string name="multi_channel_output_center">Center</string>
    <string name="multi_channel_output_side_left">Left Surround</string>
    <string name="multi_channel_output_side_right">Right Surround</string>
    <string name="multi_channel_output_subwoofer">Subwoofer</string>
    <string name="multi_channel_output_back_left">Left Rear Surround</string>
    <string name="multi_channel_output_back_right">Right Rear Surround</string>
    <string name="press_the_button_tone_to_continue">Press the *** button on the product playing a tone to continue.</string>
    <string name="please_wait_until_the_connection_complete">Please wait until the connection is complete. This may take a while.</string>
    <string name="checking_for_the_latest_firmware">Checking for the latest firmware. This may take a while.</string>
    <string name="grant_premission_to_put_product">Grant permission to put product in discovery mode.</string>
    <string name="go_to_settings_to_grant_local_network">Go to Settings to grant “ Local Network “ permission to put product in discovery mode.</string>
    <string name="go_to_settings_to_grant_bluetooth">Go to Settings to grant ” Bluetooth ” permission to put product in discovery mode.</string>
    <string name="go_to_settings_to_grant_location">Go to Settings to grant ” Location ” permission to put product in discovery mode.</string>
    <string name="go_to_settings_to_grant_location_nearby">Go to Settings to grant “ Location &amp; Nearby devices “ permission to put product in discovery mode.</string>
    <string name="set_up_google_home_go_app">To learn more and access additional features, go to Google Home app.</string>
    <string name="setting_network">Network</string>
    <string name="we_need_your_verifcation_on_the_product">We need your verification on the product to continue setup. Please try again.</string>

    <string name="light_control_neon_not_translate" translatable="false">NEON</string>
    <string name="light_control_loop_not_translate" translatable="false">LOOP</string>
    <string name="light_control_bounce_not_translate" translatable="false">BOUNCE</string>
    <string name="light_control_trim_not_translate" translatable="false">TRIM</string>
    <string name="light_control_switch_not_translate" translatable="false">SWITCH</string>
    <string name="light_control_freeze_not_translate" translatable="false">FREEZE</string>

    <string name="dj_effect_repeater_not_translate" translatable="false">REPEATER</string>
    <string name="dj_effect_filter_not_translate" translatable="false">FILTER</string>
    <string name="dj_effect_echo_not_translate" translatable="false">ECHO</string>
    <string name="dj_effect_gater_not_translate" translatable="false">GATER</string>
    <string name="dj_effect_wipeout_not_translate" translatable="false">WIPEOUT</string>
    <string name="dj_effect_effect" translatable="false">Effect</string>
    <string name="dj_effect_lab" translatable="false">Lab</string>

    <string name="diff_harmancast_version_tip">For stable performance, please update product software to continue grouping.</string>
    <string name="make_sure_all_products_are_connected_to_wifi">Make sure all products are connected to Wi-Fi</string>
    <string name="multi_channel_groupInfo_condition_fail">%s belongs to %s, please ungroup to start a new group.</string>
    <string name="product_cannot_be_connected_at_this_moment">Product cannot be connected at this moment.</string>
    <string name="select_immersive_group">Select Product Set</string>
    <string name="Performance_enhancement">Performance enhancement</string>
    <string name="For_optimal_calibration_accuracy">For optimal calibration accuracy, try the steps below :</string>
    <string name="Reduce_noise">Reduce environment noise</string>
    <string name="Remain_quiet_during_the_calibration">Remain quiet during the calibration.</string>
    <string name="Check_the_speakers">Check the speakers</string>
    <string name="Keep_clear_of_the_space_between_the_speakers_and_do_not_cover_them">Keep clear of the space between the speakers and do not cover them.</string>
    <string name="Face_all_speakers_towards_the_listener">Face all speakers towards the listener.</string>
    <string name="Speakers_should_not_be_too_far_apart_from_each_other">Speakers should not be too far apart from each other.</string>
    <string name="Position_all_speakers_at_a_similar_height.">Position all speakers at a similar height.</string>
    <string name="re_calibrate">RE-CALIBRATE</string>
    <string name="finish_grouping">FINISH GROUPING</string>
    <string name="chromecast_built_in" translatable="false">Google Cast</string>

    <string name="x_product_is_missing_from_your_group">%d product is missing from your group.</string>
    <string name="x_product_missing">%d product  missing</string>
    <string name="x_product_is_missing">%d product is missing</string>
    <string name="Above_products_are_disconnected_from_the_group">Above products are disconnected from the group. When the products get reconnected, group will be re-formed.</string>
    <string name="How_to_get_product_back_to_group">How to get product back to the group ?</string>
    <string name="Make_sure_the_product_is_in_the_same_room_as_the_group">Make sure the product is in the same room as the group.</string>
    <string name="Reboot_Product">Reboot Product</string>
    <string name="Turn_the_product_OFF_and_ON_again">Turn the product OFF and ON again.</string>
    <string name="Group_will_be_auto">Group will be auto re-formed when the other product is detected. Before that, you can use this product as a standalone speaker.</string>
    <string name="group_calibration_cannot_be_done_at_the_moment">Sorry, group calibration cannot be done at the moment, please try again later.</string>
    <string name="tap_here_to_enable_later">Tap <bold>here</bold> to enable later.</string>
    <string name="text_here">here</string>
    <string name="Cancel_Calibration_To_Calibrate_later_find_the_Calibration_option_from_product_control_page">Cancel Calibration? To Calibrate later, find the [Calibration] option from product control page.</string>
    <string name="calibration_result_number_of_tips">We found %s item to improve the calibration result.</string>
    <string name="rear_speakers_reversed_tips">Rear speakers are in reversed position, swap them and recalibrate.</string>
    <string name="rear_speakers_disconnected_tips">Rear speakers disconnected, dock speakers back into the soundbar and recalibrate.</string>
    <string name="recalibrate_uppercase">RECALIBRATE</string>
    <string name="reduce_environment_noise">Reduce environment noise</string>
    <string name="reduce_environment_noise_desc">Remain quiet during the calibration.</string>
    <string name="adjust_the_soundbar_position">Adjust the soundbar position</string>
    <string name="adjust_the_soundbar_position_desc">Do not put the soundbar too close to side walls or large objects.</string>
    <string name="check_the_rear_speakers_desc">1.Keep clear of the space between the soundbar and the rear speakers.\n2.Do not cover or hide the rear speakers.\n3.Face the rear speakers towards the listener.\n4.Soundbar and rear speakers should not be too far apart.\n5.Keep clear of the soundbar top panel.</string>
    <string name="check_the_rear_speakers_desc_1">Keep clear of the space between the soundbar and the rear speakers.</string>
    <string name="check_the_rear_speakers_desc_2">Do not cover or hide the rear speakers.</string>
    <string name="check_the_rear_speakers_desc_3">Face the rear speakers towards the listener.</string>
    <string name="check_the_rear_speakers_desc_4">Soundbar and rear speakers should not be too far apart.</string>
    <string name="check_the_rear_speakers_desc_5">Keep clear of the soundbar top panel.</string>
    <string name="go_to_tidal_app">Go to Tidal App</string>
    <string name="go_to_spotify_app">Go to Spotify App</string>
    <string name="enable_chromecast_built_in_to_continue">Enable Google Cast to continue</string>
    <string name="Step_1_Finish_product_setup">Step 1: Finish product setup</string>
    <string name="Make_sure_all_your_products_are_connected_to_WiFi_desc">Make sure all your products are connected to Wi-Fi and enabled with Google Cast.</string>
    <string name="Chromecast_built_in_can_be_enabled_desc">Google Cast can be enabled in [Product Settings] page.</string>
    <string name="Step_2_Log_into_Google_Home_app">Step 2: Log into Google Home app</string>
    <string name="Open_Google_Home_app_and_login_with_your_Google_account">Open Google Home app and login with your Google account.</string>
    <string name="Step_3_Add_device_to_room">Step 3: Add product to a room</string>
    <string name="Under_devices_tab_choose_the_product_under_local_devices">Under <bold>‘Devices’</bold> tab, choose the product under <bold>‘Local devices’</bold>.</string>
    <string name="On_the_device_control_screen_choose_add_to_a_room_to_continue">On the device control screen, choose <bold>‘Add to a room’</bold> to continue.</string>
    <string name="Repeat_the_above_process_for_all_products">Repeat the above process for all products.</string>
    <string name="Step_4_Create_a_group">Step 4: Create a group</string>
    <string name="Under_settings_tab_tap_button_on_bottom_right_choose_speaker_group">Under <bold>‘Settings’</bold> tab, tap “+” button on bottom right, choose <bold>‘Speaker group’</bold>.</string>
    <string name="Select_products_you_want_to_group_then_save">Select products you want to group, then save.</string>
    <string name="Ready_for_some_music">Ready for some music ?</string>
    <string name="Start_playback_in_your_preferred_music_app">Start playback in your preferred music app.</string>
    <string name="Open_player_in_full_screen_mode_look_for_the_cast_button">Open player in full screen mode, look for the cast button.</string>
    <string name="Select_your_group_from_device_list">Select your group from device list.</string>
    <string name="Chromecast_built_in_for_music_streaming_and_multi_room_audio">Google Cast for music streaming and multi-room audio</string>
    <string name="To_enable_Chromecast_for_music_streaming_and_multi_room_audio_later_select_Product_Settings_from_product_card_to_continue">To enable Chromecast for music streaming and multi-room audio later, select [Product Settings] from product card to continue.</string>
    <string name="Ready_to_play_some_music_chromecast_built_in">Ready to play some music? Tap the \"Google Cast\" button in hundreds of apps.</string>
    <string name="Enable_Chromecast_for_music_streaming_and_multi_room_audio">Enable Chromecast for music streaming and multi-room audio.</string>
    <string name="connecting_please_wait">Connecting, please wait</string>
    <string name="HK_Enchant_Bar">HK Enchant Bar</string>
    <string name="connect_speaker_to_wifi">Connect speaker to Wi-Fi</string>
    <string name="text_quit_uppercase">QUIT</string>
    <string name="text_retry_uppercase">RETRY</string>
    <string name="cannot_setup_wifi_now_set_up_later">Cannot setup Wi-Fi now? %1$s</string>
    <string name="set_up_later">Set up later</string>
    <string name="choose_networks">Choose networks</string>
    <string name="NewPlayView_This_song_isn_t_existed_in_an_album">This song isn’t existed in an album</string>
    <string name="Enable_Google_Assistant_for_music_streaming_and_multi_room_music_with_Chromecast_built_in">Enable Google Assistant for music streaming and multi-room music with Google Cast.</string>
    <string name="There_has_been_a_connection_issue_continue_with_basic_controls_and_do_Wi_Fi_setup_later">There has been a connection issue, continue with basic controls and do Wi-Fi setup later?</string>
    <string name="Checking_the_latest_software_This_will_take_up_to_20_seconds">Checking the latest software. This will take a while.</string>
    <string name="text_other_uppercase">OTHER</string>
    <string name="connect_to_bluetooth">Connect to Bluetooth</string>
    <string name="ready_to_play_some_music_desc">Ready to play some music? Tap the “Google Cast” button in hundreds of apps. </string>
    <string name="to_learn_more_and_access_additional_features_go_to_google_home_app">To learn more and access additional features, go to Google Home app.</string>
    <string name="to_learn_more_and_access_additional_features_go_to_amazon_alexa_app">To learn more and access additional features, go to Amazon Alexa app.</string>
    <string name="to_setup_voice_control_from_alexa_enabled_speaker_and_multi_room_groups_tips">To setup voice control from an Alexa-enabled speaker and multi-room groups with other Alexa MRM speakers, download Amazon Alexa app.</string>
    <string name="make_sure_your_phone_is_connected_to_the_same_network_with_speaker">Make sure your phone is connected to the same network with product.</string>
    <string name="jbl_Your_mobile_device_could_not_connect_to_your_product_at_the_moment__please_try_again_">Your mobile device could not connect to your product at the moment, please try again.</string>
    <string name="enable_Google_Assistant_to_stream_music">Enable Google Assistant to stream music and set up multi-room control with Google Cast.</string>


    <!--App need to translate strings 2023.11.06 ================================================================================================   -->
    <string name="lr_channel">L / R channel</string>
    <string name="play_together">Play together</string>
    <string name="boost_volume">Boost volume</string>
    <string name="sound_warning_content">A loud sound will come from your speakers during calibration.</string>
    <string name="keep_quiet_title">Keep Quiet</string>
    <string name="group_products">GROUP PRODUCTS</string>
    <string name="grouping_fail_content">We are not able to group the products at the moment, please try again later.</string>
    <string name="channel">Channel</string>
    <string name="secondary">Secondary</string>
    <string name="speaker_name">Name</string>
    <string name="can_not_group">Cannot Group</string>
    <string name="continue_to_setup_wifi">Continue to setup Wi-Fi.</string>
    <string name="remove_this_group">Remove This Group</string>


    <!--App need to translate strings 2023.12.08 ================================================================================================   -->
    <string name="getting_ready_for_home_theatre_system">Getting Ready for\nHome Theatre System</string>
    <!--    <string name="manual_trigger_factory_reset_a">Press and hold VOL- &amp; VOL+ &amp; SOURCE buttons at the same time until the LED flashes (hold for about 5 seconds).</string>-->


    <string name="do_not_block_the_microphone">Do not block the microphone</string>
    <string name="check_the_rear_speakers">Make sure of the following</string>
    <string name="calibration_takes_about_3_minutes">Calibration takes about 3 minutes.</string>
    <string name="remote_control">Remote Control</string>
    <string name="Press_on_the_speaker_to_authenticate">Press %s on the speaker to authenticate.</string>
    <string name="authenticate_timeout">We need your authentication to continue. Please try again.</string>
    <string name="Play_music_on_your_speaker_to_start">Play music on your speaker to start.</string>
    <string name="battery_saving_mode_title">Battery Saving Mode</string>
    <string name="battery_saving_mode_subtitle_on">Maximize playtime by optimizing playback volume and bass.</string>
    <string name="moment_available_state_desc">Select your favourite ambient audio or playlist to set up one touch playback on your product.</string>
    <string name="moment_unavailable_state_desc">Select your favourite playlist to set up one touch playback on your product.</string>
    <string name="moment_forest">Forest</string>
    <string name="moment_rain">Rain</string>
    <string name="moment_ocean">Ocean</string>
    <string name="moment_city_walk">Citywalk</string>
    <string name="moment_your_playlist">Your Playlist</string>
    <string name="moment_forest_desc">Indulge in the soothing melodies of the woods.</string>
    <string name="moment_rain_desc">Unwind to the enchanting rhythm of rain.</string>
    <string name="moment_ocean_desc">Relax with the healing voices of the ocean.</string>
    <string name="moment_city_walk_desc">Step into the vibrant atmosphere of city streets.</string>
    <string name="moment_your_playlist_desc">Setup your own playlist</string>
    <string name="authentication_method_a">The product is now playing a tone, press %s button to continue.</string>
    <string name="authentication_method_b">The product is now playing a tone, press %s button to continue.</string>
    <string name="authentication_method_c">The product is now playing a tone, press the setup button on the back to continue.</string>
    <string name="manual_trigger_wifi_setup_mode_a">Press and hold VOL- &amp; VOL+ buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="manual_trigger_wifi_setup_mode_b">Press and hold MOMENT &amp; BLUETOOTH  buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="manual_trigger_wifi_setup_mode_c">Double press the setup button on the back side, LED will flash when it enters setup mode.</string>
    <string name="manual_trigger_factory_reset_a">Press and hold VOL- &amp; VOL+ &amp; SOURCE buttons at the same time until the LED flashes (hold for about 5 seconds).</string>
    <string name="manual_trigger_factory_reset_b">Press and hold MOMENT &amp; PLAY buttons at the same time until the LED flashes (hold for about 10 seconds).</string>
    <string name="manual_trigger_factory_reset_c">Press and hold the setup button on the backside until the LED flashes (hold for about 10 seconds).</string>
    <string name="enter">enter</string>
    <string name="Share_your_music_to_nearby_speakers_to_amplify_the_party">Share your music to nearby speakers to amplify the party.</string>
    <string name="Connect_a_product_to_start_party">Connect a product to start party</string>
    <string name="NewStructure_PARTY">PARTY</string>
    <string name="jbl_Waitting">Waiting</string>
    <string name="quit_the_party">Quit the party</string>
    <string name="press_button_on_the_speaker_to_quit_party">Press %s on the speaker to quit the party.</string>
    <string name="press_button_on_the_speaker_to_stop_listening_from_party">Press %s on the speaker to stop listening from the party.</string>
    <string name="Wirelessly_connect_multiple_speakers_to_create_an_instant_">Wirelessly connect multiple speakers to create an instant party with synced-up music.</string>
    <string name="Enjoy_boosted_sound_in_a_single_indoor_space_">Enjoy boosted sound in a single indoor space or an outdoor open area.</string>
    <string name="Are_you_sure_you_want_to_remove_this_product_from_the_product_list">Are you sure you want to remove this product from the product list?</string>
    <string name="moment_set_up_a_playlist_on_moment">Set up a playlist on Moment</string>
    <string name="moment_set_up_a_playlist_on_moment_content">1. Tap ‘ + ’ to add your preferred music service.\n\n2. Sign into your account.\n\n3. Tap the first track of your favorite playlist or radio station.\n\n* Set desired timer in settings if needed.</string>
    <string name="moment_button_will_not_be_activated_if_you_exit_now">Moment button will not be activated if you exit now.</string>
    <string name="moment_timer_hint">Auto stop playback in some time after triggering moment button.</string>
    <string name="Now_try_out_how_good_is_your_new_immersive_system">Now try out how good your new immersive system is.</string>
    <string name="Verify_on_the_speaker_to_continue">Verify on the speaker to continue</string>
    <string name="Listen_to_free_internet_radio__news__sports_music__audiobooks_and_podcasts">Listen to free internet radio, news, sports, music, audiobooks, and podcasts.</string>
    <string name="nearby_product">Nearby Product</string>
    <string name="If_the_above_methods_don_t_work_please_ungroup_and_group_again">If the above methods don’t work, please ungroup and group again.</string>
    <string name="Checking_the_latest_software_This_will_take_a_while">Checking the latest software. This will take a while.</string>
    <string name="Connect_your_phone_to_Wi_Fi_in_settings_before_continuing">Connect your phone to Wi-Fi in settings before continuing.</string>
    <string name="Home_Theatre_System">Home Theatre System</string>
    <string name="group_soundbar_with_your_subwoofer">Group soundbar with your subwoofer</string>
    <string name="set_up_soundbar">Set up soundbar</string>
    <string name="Turn_on_and_connect_soundbar_to_the_same_Wi_Fi_network_as_your_subwoofer">Turn on and connect soundbar to the same Wi-Fi network as your subwoofer.</string>
    <string name="Group_products">Group products</string>
    <string name="the_app_will_guide_to_group_soundbar_and_subwoofer_together_as_a_home_theatre_system">The app will guide to group soundbar and subwoofer together as a home theatre system.</string>
    <string name="Group_soundbar_with_subwoofer_to_add_on_more_bass_to_your_home_theatre_system">Group soundbar with subwoofer to add on more bass to your home theatre system.</string>
    <string name="moment_customize_moment">Customize Moment</string>
    <string name="moment_timer">Timer</string>
    <string name="moment_timer_x_min">%d minutes</string>
    <string name="ir_learning">IR Learning</string>
    <string name="Through_IR_learning_your_infrared_TV_remote_control_can_learn_some_commands_of_the_soundbar">Through IR learning, your infra-red TV remote control can learn some commands of the soundbar.</string>
    <string name="Start_IR_Learning">Start IR Learning</string>
    <string name="Point_your_TV_remote_control_at_the_soundbar_Follow_the_soundbar_display_guide_to_operate_the_buttons">Point your TV remote control at the soundbar. Follow the soundbar display guide to operate the buttons.</string>
    <string name="The_soundbar_will_exit_the_TV_remote_control_learning_mode_automatically_if_no_action_is_taken_for_30_seconds">The soundbar will exit the TV remote control learning mode automatically if no action is taken for 30 seconds.</string>
    <string name="Smart_Mode">Smart Mode</string>
    <string name="Automatically_adapts_the_EQ_to_the_content_for_an_optimized_sound_experience">Automatically adapts the EQ to the content for an optimized sound experience.</string>
    <string name="You_are_sharing_music_to_other_speakers_at_the_party">You are sharing music to other speakers at the party.</string>
    <string name="Go_to_control_center_and_turn_on_Bluetooth_to_discover_nearby_products">Go to control center and turn on Bluetooth to discover nearby products.</string>
    <string name="Turn_on_Bluetooth_to_discover_nearby_products">Turn on Bluetooth to discover nearby products.</string>
    <string name="Please_ungroup_your_multi_channel_system_to_add_these_speakers_into_a_party">Please ungroup your multi-channel system to add these speakers into a party.</string>
    <string name="Compatible_products_for_PartyTogether">Compatible products for
<bold>PartyTogether</bold></string>
    <string name="Current_PartyTogether_compatible_models"><bold>Current PartyTogether compatible models are:</bold>
JBL Charge 5 Wi-Fi
JBL Boombox 3 Wi-Fi
JBL PartyBox Ultimate
The speakers need to be updated to the latest software OneOS 2.3 to enjoy the feature.</string>
    <string name="How_to_create_a_party">How to create a party</string>
    <string name="How_to_create_a_party_steps">Make sure the speakers are powered on.
Tap on the speaker icon from the Nearby speaker list to join the party.
Start music playback on any of the speakers.
New speaker can be added to the party at any time after it shows up as a Nearby speaker.</string>
    <string name="How_to_quit_a_party">How to quit a party</string>
    <string name="Tap_on_the_icon_of_the_speaker_to_be_removed_from_the_party_and_follow_on_screen_instructions">Tap on the icon of the speaker to be removed from the party and follow on screen instructions.</string>
    <string name="Join_party_as_a_group">Join party as a group</string>
    <string name="If_your_speakers_are_setup_as_a_stereo_multi_channel_audio_system_please_ungroup_them_first_before_they_can_join_any_party">If your speakers are setup as a stereo/ home audio system, please ungroup them first before they can join any party.</string>
    <string name="newtidal_Mixes_For_You">Mixes For You</string>
    <string name="Personal_Listening_Mode">Personal Listening Mode</string>
    <string name="Mute_soundbar_and_subwoofer">Mute soundbar and subwoofer to make a more silence sound environment.</string>
    <string name="Open_this_mode_to_create_a_more">Open this mode to create a more silent sound environment.</string>
    <string name="Personal_Listening">Personal Listening</string>
    <string name="Soundbar_and_subwoofer_have_been_muted">Soundbar and subwoofer have been muted.</string>
    <string name="Feature_Guide">Feature Guide</string>
    <string name="Detach_rear_speakers">Detach rear speakers.</string>
    <string name="Put_rear_speakers_in_front_of_you">Put rear speakers in front of you.</string>
    <string name="Enjoy_the_immersive_performance_around_you">Enjoy the immersive performance around you.</string>
    <string name="Your_will_auto_leave_this_mode_when">Your will auto leave this mode when:</string>
    <string name="your_soundbar_is_turned_off">your soundbar is turned off.</string>
    <string name="no_audio_input_for_more_than_10_minutes">no audio input for more than 10 minutes.</string>


    <!--App need to translate strings 2023.12.08 ================================================================================================   -->
    <string name="text_tips">Tips</string>
    <string name="How_to_get_product_connected">How to reconnect</string>


    <string name="close">close</string>
    <string name="min_remaining">%s min remaining</string>
    <string name="transfer_unsuccessful">Transfer Unsuccessful</string>
    <string name="update_unsuccessful">Update Unsuccessful</string>
    <string name="reconnect_to_continue">Reconnect to Continue</string>
    <string name="checking_for_update">Checking for update…</string>
    <string name="estimated_update_time">Estimated update time: %s min</string>
    <string name="make_sure_your_partylights_are_close">Make sure your Partylights are close to your mobile device. Do not turn off during update.</string>
    <string name="all_products_are_up_to_date">All products are up to date.</string>
    <string name="version_with_num">Version: %1$s</string>
    <string name="update_available">Update Available</string>
    <string name="your_partystage_settings_are_not_complete_yet">Your PartyStage settings are not complete yet. The current settings will not be saved if you exit now.</string>
    <string name="resume">Resume</string>

    <string name="your_phone_could_not_connect_to_your_product_at_the_moment">Your phone could not connect to your product at the moment.</string>
    <string name="your_product_has_been_added">Your product has been added.</string>
    <string name="plug_in_power_to_continue_update">Plug in power to continue update.</string>


    <string name="connecting_to_server">Connecting to server…</string>
    <string name="fail_connecting_to_server">Fail connecting to server, please make sure your product is connected to Wi-Fi network and try again later.</string>

    <string name="location_permission_is_required_to_set_up_new_product">Location permission is required to set up new product.</string>
    <string name="go_to_settings_to_grant_location_permission_to_put_product_in_discovery_mode">Go to settings to grant "Location" permission to put product in discovery mode.</string>
    <string name="nearby_permission">Nearby Permission</string>
    <string name="grant_permission_to_discover_nearby_products">Grant permission to discover nearby products.</string>

    <string name="make_sure_another_product_of_the_same_model_is_powered_on_and_placed_nearby">Make sure another product of the same model is powered on and placed nearby.</string>
    <string name="in_case_of_not_showing_up_perform_a_power_cycle_on_another_product">In case of not showing up, perform a power cycle on another product.</string>
    <string name="make_sure_no_daisy_chain_plugged_in">Make sure no daisy chain is plugged in.</string>
    <string name="place_the_two_speakers_one_on_the_left_and_one_on_the_right_in_front_of_the_listening_area">Place the two speakers, one on the left and one on the right, in front of the listening area.</string>
    <string name="i_have_placed_it">I’ve placed it</string>
    <string name="group_succeed_lets_name_your_pair">Group succeed! Let’s name your pair</string>
    <string name="supported_models_partybox_110_310_710_encore_encore_essential">Supported models: PartyBox 110, 310, 710, Encore, Encore Essential.</string>
    <string name="quit">Quit</string>

    <string name="Connect_at_least_one_product">Connect at least one product in ‘My Products’ list to start a party.</string>
    <string name="You_are_sharing_music_with_other_speakers_at_the_party">You are sharing music with other speakers at the party.</string>

    <string name="create_party">create party</string>

    <string name="sound_tuning">Sound Tuning</string>

    <string name="To_enter_online_diagnosis_mode_Remote_access_to_your_product_is_required">To enable online diagnosis, you are about to grant access right to your product.</string>
    <string name="do_not_grant">DO NOT GRANT</string>
    <string name="Remote_access_granted_this_feature_will_be_auto_dismiss_after_the_call">Remote access granted. Access will be automatically disabled after the call ends.</string>
    <string name="jbl_account">JBL Account</string>
    <string name="sign_in_or_sign_up">Sign in or sign up</string>
    <string name="invalid_email_address">Invalid email address</string>
    <string name="account_text">Account</string>
    <string name="edit_profile">Edit Profile</string>
    <string name="register_my_product">Register My Product</string>
    <string name="sign_in_uppercase">SIGN IN</string>
    <string name="sign_out_uppercase">SIGN OUT</string>
    <string name="profile_text">Profile</string>
    <string name="send_me_promotional_emails_with_coupons_sales_and_other_perks">Send me promotional emails with coupons, sales and other perks.</string>
    <string name="registered">Registered</string>
    <string name="to_be_registered">To be registered</string>
    <string name="no_product_can_be_registered_currently">No product can be registered currently</string>
    <string name="find_other_registered_product_click_here">Find other registered Product? <bold>Click here</bold></string>
    <string name="click_here">Click here</string>
    <string name="other_register_product">Other Registered Products</string>
    <string name="no_other_registered_product_at_present">No other registered product at present.</string>
    <string name="Play_music_on_your_speaker_to_start_party">Play music on your speaker to start party.</string>
    <string name="create_password">Create Password</string>
    <string name="Enter_Password">Enter Password</string>
    <string name="Forgot_Password">Forgot Password?</string>
    <string name="Reset_Password">Reset Password</string>
    <string name="at_least_8_characters">At least 8 characters</string>
    <string name="including_upper_and_lower_case_letters_numbers_and_symbols">Including upper and lower case letters, numbers, and symbols</string>
    <string name="Wrong_password">Wrong password</string>
    <string name="Account_does_not_exist">Account does not exist</string>
    <string name="about_you">About You</string>
    <string name="first_name">First name</string>
    <string name="last_name">Last name</string>
    <string name="signing_up_terms_and_conditions_and_acknowledge">By signing up, you agree to our <bold>Terms &amp; Conditions</bold> and acknowledge that our <bold>Privacy Policy</bold> applies to you</string>
    <string name="terms_and_conditions_placeholder">Terms &amp; Conditions</string>
    <string name="privacy_and_policy_placeholder">Privacy Policy</string>
    <string name="Activate_Your_Account">Activate Your Account</string>
    <string name="You_havent_activated_your_account_please_check_your_inbox">You haven’t activated your account, please check your inbox.</string>
    <string name="BACK_TO_SIGN_IN">BACK TO SIGN IN</string>
    <string name="something_went_wrong_please_try_again_later">Something went wrong, please try again later.</string>
    <string name="are_you_sure_you_want_to_sign_out">Are you sure you want to sign out?</string>
    <string name="You_edited_your_profile_elsewhere__please_sign_in_again">You edited your profile elsewhere, please sign in again.</string>
    <string name="text_register">Register</string>
    <string name="date_of_purchase">Date of purchase</string>
    <string name="where_did_you_buy_the_product">Where did you buy the product ?</string>
    <string name="are_you_sure_you_want_to_remove_this_registration">Are you sure you want to remove this registration ?</string>
    <string name="Enter_an_Email_address_to_create_or_sign_in_your_JBL_account">Enter an email address to create or sign in to your JBL account.</string>
    <string name="order_number">Order Number</string>
    <string name="airplay_streaming_description_2">1. Open Control Center on your mobile and tap AirPlay icon.\n2. Select all the speakers that you want to play music to.\n3. Open music service app and start playback.</string>
    <string name="tune_device_for_optimized_sound_performance">Tune your %s for an optimized sound performance.</string>
    <string name="your_product_has_been_tuned_tips">Your product has been tuned and will automatically adapt to different environments.</string>
    <string name="text_tuning">Tuning ...</string>
    <string name="hint_select">Select</string>
    <string name="opt_others">Others</string>
    <string name="registration_id">Registration ID</string>
    <string name="registration_date">Registration Date</string>
    <string name="Your_Are_Almost_There">Your Are Almost There</string>
    <string name="Your_account_has_been_created_Please_check_your_email_for_the_activation_link">Your account has been created.\nPlease check your email for the activation link.</string>
    <string name="Reset_Your_Password">Reset Your Password</string>
    <string name="sent_an_email_to_reset_your_password_tips">We have sent an email to %s with the information to reset your password. Please complete the reset process in the link provided.</string>

    <string name="perform_calibration_for_this_group">Perform calibration for this group.</string>
    <string name="group_calibration_failed_tips">Your products are grouped successfully. However, calibration cannot be done at the moment.\n\nPlease perform a calibration from group control page later when all products are in operation mode.</string>
    <string name="for_optimal_calibration_accuracy_try_the_steps_below">For optimal calibration accuracy, try the steps below :</string>
    <string name="start_60_day">Start 60-day free trial with</string>
    <string name="roon">Roon</string>
    <string name="redeem_60_day">Redeem 60-Day Free Trial for New User</string>
    <string name="H5_Your_product_has_been_upgraded_for_better_performance">Your product has been upgraded for better performance.</string>
    <string name="H5_Roon_is_now_supported">Roon is now supported</string>
    <string name="H5_has_Roon_s_streaming_technology_built_in__for_better_sounding__easier_to_navigate__highest_quality_music_streaming_throughout_your_home">%s has Roon\'s streaming technology built in,for better-sounding, easier to navigate, highest-quality music streaming throughout your home.</string>
    <string name="Start_by_creating_a_Roon_account_with_below_coupon_code">Start by creating a Roon account with below coupon code :</string>
    <string name="valid_for_new_roon">Valid for new Roon users only, the redemption period might end without prior notice.</string>
    <string name="H5_REDEEM">REDEEM</string>
    <string name="To_redeem_later">To redeem later, select [Product Settings] > [Roon Ready] from product card to continue.</string>
    <string name="Later_you_can_still_do_so_from_product_control_section">You can set up any time by tapping Product > Audio Settings.</string>
    <string name="Create_a_System">Create a System</string>
    <string name="Create_a_stereo_pair_or_connect_with_other_speakers">Create a stereo pair or connect with other speakers.</string>
    <string name="Create_a_Home_Theatre">Create a Home Theatre</string>
    <string name="Connect_to_a_subwoofer_or_other_speakers">Connect to a subwoofer or other speakers.</string>
    <string name="Create_a_Stereo_Pair">Create a Stereo Pair</string>
    <string name="Connect_to_a_Soundbar">Connect to a Soundbar</string>
    <string name="Make_sure_the_other_products_are_powered_on_and_connected_to_the_same_network">Make sure the other products are powered on and connected to the same network.</string>
    <string name="For_better_sound_performance_not_all_products_can_be_grouped_as_an_system">To guarantee optimal sound performance, only certain products can be grouped as a system.</string>
    <string name="Cannot_find_the_products_to_pair_with">Cannot find the products to pair with ?</string>
    <string name="Make_sure_another_product_of_the_same_model_is_powered_on_and_placed_nearby">Make sure another product of the same model is powered on and placed nearby.</string>
    <string name="In_case_of_not_showing_up_perform_a_power_cycle_on_another_product">In case of not showing up, perform a power cycle on another product.</string>
    <string name="Make_sure_the_Enchant_Bar_is_powered_on_and_connected_to_the_same_network">Make sure the Enchant Bar is powered on and connected to the same network.</string>
    <string name="Cannot_find_the_Soundbar_to_pair_with">Cannot find the Soundbar to pair with ?</string>
    <string name="Check_compatible_speakers">Check compatible speakers ...</string>
    <string name="For_other_registered_products_click_here">For other registered products click here</string>
    <string name="Create_Multi_room">Create Multi-room</string>
    <string name="With_Airplay">With AirPlay</string>
    <string name="With_Google_Home">With Google Home</string>

    <string name="jbl_save_text">SAVE</string>
    <string name="jbl_reset">RESET</string>

    <string name="mix_with_music">Mix with Music</string>
    <string name="mix_with_music_playback_as_ambient_atmosphere_background_sound">Mix with music playback as ambient background sound.</string>
    <string name="play_demo_track">Play Demo Track</string>
    <string name="stop">Stop</string>
    <!--    todo zrz not translate start-->
    <!--    party light start -->
    <string name="ptl_back">BACK</string>
    <string name="ptl_light_controls">Light Controls</string>
    <string name="ptl_product_info_title">Product Information</string>
    <string name="ptl_firmware_upgrade">Product Software</string>
    <string name="ptl_lear_more">Learn more</string>
    <string name="ptl_reset_product">Reset Product</string>
    <string name="ptl_start_guide">Quick Start Guide</string>
    <string name="ptl_serial_number">Serial Number</string>
    <string name="ptl_product_usage">Product Usage</string>
    <string name="ptl_total_power_time">Total Power-on Time</string>
    <string name="ptl_hour">Hours</string>
    <string name="ptl_minute">Minutes</string>
    <string name="ptl_power_on">Power On</string>
    <string name="ptl_power_on_desc">Make sure product is powered on.</string>
    <string name="ptl_reset">Trigger Reset</string>
    <string name="ptl_reset_desc">Press and hold Light button until the product reboots (hold for about %1$s seconds).</string>
    <string name="ptl_update_tip">Make sure your Partylights are close to your mobile device. Do not turn off during update.</string>
    <string name="ptl_update_all">UPDATE ALL</string>
    <string name="ptl_check_update">Checking for update...</string>
    <string name="ptl_estimate_time">Estimated update time: %1$smin</string>
    <string name="ptl_all_updated">All products are up to date.</string>
    <string name="ptl_retry">RETRY</string>
    <string name="ptl_low_battery_tip">Product battery needs to be above %1$s</string>
    <string name="ptl_update_avaliable_with_version">Update Available (Version: %1$s)</string>
    <string name="ptl_updated">Update to date</string>
    <string name="ptl_version">Version: </string>
    <string name="ptl_low_battery">Low battery</string>
    <string name="ptl_update_failed">Update failed</string>
    <string name="ptl_party_stage_tip">Build your PartyStage with at least %1$s PartyLight Sticks and play music on the connected PartyBox for a more exciting lighting experience.</string>
    <string name="ptl_got_it">GOT IT</string>
    <string name="ptl_press_start">Place the PartyLight Sticks in order, then start to build your PartyStage.</string>
    <string name="ptl_press_start2">Press the light button on the product sequentially, the product will light up.</string>
    <string name="ptl_start">START</string>
    <string name="ptl_restart">RESTART</string>
    <string name="ptl_done">DONE</string>
    <string name="ptl_continue">CONTINUE</string>
    <string name="ptl_stage_success_tip">Partystage is set successfully. Let’s Party now!</string>
    <string name="ptl_stage_show_demo">SHOW ME THE DEMO</string>
    <string name="ptl_stage_empty">PartyStage is empty...</string>
    <string name="ptl_stage_empty_desc">Please ensure you have at least four PartyLight Sticks on the stage.</string>
    <string name="ptl_quit_stage_warning">Your PartyStage settings are not complete, exit now the current settings will not be saved.</string>
    <string name="ptl_on_stage">You have an ongoing PartyStage. Enjoy your party!</string>
    <string name="ptl_stage_dismiss">DISMISS THE STAGE</string>
    <string name="ptl_stage_dismiss_desc">Dismissing the stage will clear your PartyStage settings, are you sure you want to proceed?</string>
    <string name="ptl_dismiss">DISMISS</string>
    <!--    party light end-->
    <string name="night_listening">Night Listening</string>
    <string name="activate_this_feature_to_mute_soundbar_and_subwoofer">Activate this feature to mute soundbar and subwoofer.</string>
    <string name="the_main_soundbar_and_subwoofer_have_been_muted">The main soundbar and subwoofer have been muted.</string>
    <string name="mute_soundbar_and_subwoofer_to_create_a_quiet_environment">Mute soundbar and subwoofer to create a quiet environment.</string>
    <string name="night_listening_will_automatically_turn_off_when">Night listening will automatically turn off when:</string>
    <string name="after10_minutes_of_no_audio_input">there has been no audio input for 10 minutes.</string>
    <string name="place_rear_speakers_in_front_of_you">Place rear speakers in front of you.</string>
    <string name="enjoy_movies_at_night_without_disturbing_others">Enjoy movies at night without disturbing others.</string>
    <string name="no_internet">No Internet</string>
    <string name="please_go_online_and_retry">Please go online and retry.</string>
    <string name="loading_fail">Loading Fail</string>
    <string name="function_is_initializing_please_enter_later">Function is initializing, please enter later</string>
    <string name="must_keep_at_least1_light_show">Must keep at least 1 light show</string>
    <string name="make_sure_all_your_products_are_connected_to_wi_fi_and_enabled_with_google_cast">Make sure all your products are connected to Wi-Fi and enabled with Google Cast.</string>
    <string name="google_cast_can_be_enabled_in_product_settings_page">Google Cast can be enabled in [Product Settings] page.</string>
    <string name="music_streaming_with_qobuz_connect">Music Streaming with Qobuz Connect</string>
    <string name="go_to_qobuz_app">Go to Qobuz app</string>
    <string name="plug_in_to_start_updating">Plug in to start updating</string>
    <string name="plug_one_side_of_the_cable_type_c_to_type_c_to_the_speaker_and_the_other_side_to_your_phone_to_start_the_updating_process">Plug one side of the cable (Type C to Type C) to the speaker and the other side to your phone to start the updating process</string>
    <string name="guitarist">Guitarist</string>
    <string name="guitar_vocalist">Guitar Vocalist</string>
    <string name="singer">Singer</string>
    <string name="hello_what_type_of_musician_you_are">Hello! What type of musician you are?</string>
    <string name="music_track_function">Music Track Function</string>
    <string name="music_track_related_function">Music Track Related Function</string>
    <string name="stem_separation">Stem Separation</string>
    <string name="guitar">Guitar</string>
    <string name="vocal">Vocal</string>
    <string name="others">Others</string>
    <string name="karaoke_mode">Karaoke mode</string>
    <string name="tone_shifter">Tone Shifter</string>
    <string name="whats_karaoke_mode">What‘s Karaoke mode?</string>
    <string name="karaoke_low_latency_mode_is_for_singing_with_scrolling_lyrics_using_a_basic_filter_for_speed">Karaoke/Low latency mode is for singing with scrolling lyrics, using a basic filter for speed.</string>
    <string name="drum">Drum</string>
    <string name="time_signature">Time Signature</string>
    <string name="bpm">BPM</string>
    <string name="metronome">Metronome</string>
    <string name="styles">Styles</string>

    <string name="floor">Floor</string>
    <string name="beat">Beat</string>
    <string name="rock">Rock</string>
    <string name="funk">Funk</string>
    <string name="clave">Clave</string>
    <string name="reggaeton">Reggaeton</string>
    <string name="hiphop">Hiphop</string>
    <string name="jazz">Jazz</string>
    <string name="backward_screen">Backward Screen</string>
    <string name="flip_the_on_device_screen_backward">Flip the on-device screen backward</string>
    <string name="auto">Auto</string>
    <string name="tuner">Tuner</string>
    <string name="continue_update">Continue Update？</string>
    <string name="current_wifi_connection_not_detected_would_you_like_to_continue_downloading_the_update_package_with_your_cellular_data">Current Wi-Fi connection not detected. Would you like to continue downloading the update package with your cellular data?</string>
    <string name="instrument">Instrument</string>
    <string name="customize_tuning">Customize Tuning</string>
    <string name="reference_pitch">Reference Pitch</string>
    <string name="in_tune_sound_effect">“In Tune” Sound Effect</string>
    <string name="guitar_33">Guitar 3+3</string>
    <string name="guitar_6_in_line">Guitar 6-in-line</string>
    <string name="bass_4_string">Bass 4-String</string>
    <string name="ukulele">Ukulele</string>
    <string name="tuner_settings">Tuner Settings</string>
    <string name="tuning">Tuning</string>
    <string name="edited">Edited</string>
    <string name="pluck_string_to_start_tuning">Pluck string to start tuning</string>
    <string name="tone_up_slightly">Tone up slightly</string>
    <string name="tone_up">Tone up</string>
    <string name="auto_mode">Auto Mode</string>
    <string name="manual_mode">Manual Mode</string>
    <!--usb output start-->
    <string name="this_phone">This Phone</string>
    <string name="flash_drive">Flash Drive</string>
    <string name="output_to">Output to</string>
    <string name="please_connect_your_phone">Please connect your phone to product by USB cable</string>
    <string name="record">Record</string>
    <string name="The_music_channel_is">The music channel is not included</string>
    <string name="separate_channel_output">Separate Channel Output</string>
    <string name="recored_file">Recored File</string>
    <string name="file_name">File Name</string>
    <string name="discard">Discard</string>
    <string name="do_you_want_to">Do you want to discard the unsaved audio file?</string>
    <string name="do_you_want_to_delete">Do you want to delete the audio file?</string>
    <string name="changing_output_source_it">Changing output source, it takes about 15s</string>
    <string name="please_connect_this_phone">Please connect this phone to the correct product with USB cable</string>
    <string name="please_connect_your_flash">Please connect your flash drive to product</string>
    <string name="title_guitar_looper">Loop</string>
    <string name="title_output">Output</string>
    <string name="title_save_as">Save As</string>
    <string name="the_product_is_in_a_stereo_group">The product is in a stereo group</string>
    <string name="the_product_is_in_partytogether_mode">The product is in PartyTogether mode</string>
    <!--usb output end-->
    <!--todo zrz not translate end-->

    <string name="edit_forest_elements">Edit Forest elements</string>
    <string name="edit_rain_elements">Edit Rain elements</string>
    <string name="edit_ocean_elements">Edit Ocean elements</string>
    <string name="edit_city_walk_elements">Edit Citywalk elements</string>
    <string name="thunders">Thunders</string>
    <string name="frogs">Frogs</string>
    <string name="raindrops">Raindrops</string>
    <string name="insects">Insects</string>
    <string name="creek">Creek</string>
    <string name="birds">Birds</string>
    <string name="seabirds">Seabirds</string>
    <string name="currents">Currents</string>
    <string name="wave">Wave</string>
    <string name="city">City</string>
    <string name="transport">Transport</string>
    <string name="life">Life</string>

    <string name="wifi_strength">Wi-Fi Strength</string>
    <string name="alexa_cast" translatable="false">Alexa Cast</string>

    <string name="google_cast" translatable="false">Google Cast</string>
    <string name="activate_google_cast">Activate Google Cast</string>
    <string name="ready_to_play_some_music_tap_the_google_cast_button_in_hundreds_of_apps">Ready to play some music? Tap the “Google Cast” button in hundreds of apps.</string>

    <string name="works_with_google_home">Works with Google Home</string>
    <string name="google_cast_available">Google Cast Available</string>
    <string name="you_can_cast_music_from_your_favourite_mobile_apps_to_your_device">You can cast music from your favourite mobile apps to your device.</string>
    <string name="google_cast_built_in_for_music_streaming_and_multi_room_audio">Google Cast for music streaming and multi-room audio</string>
    <string name="to_enable_google_cast_for_music_streaming_and_multi_room_audio_later_select_product_settings_from_product_card_to_continue">To enable Google Cast for music streaming and multi-room audio later, select [Product Settings] from product card to continue.</string>

    <string name="product_software_update_is_needed_to_use_moment">Product software update is needed to use Moment</string>
    <string name="device_cannot_rename_desc">Renaming cannot be done since you have added product into Apple Home Kit.\nPlease customize the product name from Apple Home App.</string>

    <!--Customer Service-->
    <string name="email_us">Email Us</string>
    <string name="call_us">Call Us</string>
    <string name="text_us">Text Us</string>
    <string name="whats_app_us">WhatsApp Us</string>
    <string name="diagnosis_report">Diagnosis Report</string>
    <string name="no_product_can_generate_a_report_currently">No product can generate a report currently</string>
    <string name="generate_report_and_share_the_code_with_our_customer_service_team_for_further_assistance">Generate report and share the code with our customer service team for further assistance</string>
    <string name="keep_quiet_and_do_not_block_the_speakers_during_the_process">Keep quiet and do not block the speakers during the process.</string>
    <string name="make_sure_the_product_is_connected_to_the_same_network_with_your_phone">Make sure the product is connected to the same network with your phone.</string>
    <string name="feature_is_not_supported_by_the_current_software_version">This feature is not supported by the current software version.</string>


    <string name="detach_rear_speakers_and_place_them_in_the_sitting_position">Detach rear speakers and place them in the sitting position.</string>
    <string name="ive_placed_them">I\'ve placed them</string>
    <string name="make_sure_the_rear_speakers_orientation_is_matching_with_the_illustration_guide">Make sure the rear speakers\' orientation is matching with the illustration guide.</string>
    <string name="second_placement">2nd Placement</string>
    <string name="cancel_calibration">Cancel Calibration</string>
    <string name="undock_the_rear_speakers_to_continue_audio_calibration">Detach the rear speakers to continue audio calibration.</string>
    <string name="make_sure_the_other_products_are_connected_to_the_same_network">Make sure the other products are connected to the same network.</string>
    <string name="immersive_type">IMMERSIVE  %1$s</string>
    <string name="cannot_find_available_products">Cannot find available products to create a system.</string>
    <string name="enable_google_cast_to_continue">Enable Google Cast to continue</string>

    <string name="do_you_want_to_help_improve_everyones_experience_by_sharing_device_stats_and_crash_reports_with_google">Do you want to help improve everyone’s experience by sharing device stats and crash reports with Google? </string>
    <string name="alexa_international_version">Alexa International Version</string>
    <string name="some_features_may_not_be_available_in_your_area">*Some features may not be available in your area.</string>
    <string name="one_hour">1 hour</string>

    <string name="bluetooth_reconnection">Bluetooth Reconnection</string>
    <string name="copied">Copied</string>

    <string name="audio">Audio</string>
    <string name="aux" translatable="false">AUX</string>
    <string name="tv" translatable="false">TV</string>
    <string name="hdmi" translatable="false">HDMI</string>
    <string name="wifi" translatable="false">WiFi</string>
    <string name="bluetooth" translatable="false">Bluetooth</string>
    <string name="usb" translatable="false">USB</string>

    <string name="your_system_is_ready">Your system is ready</string>
    <string name="name_your_system">Name your system</string>
    <string name="if_one_of_the_speakers_has_been_connected_to__">if one of the speakers has been connected to Wi-Fi, connect both speakers to the same network to continue.</string>
    <string name="cannot_find_available_products_to_create_the_stereo">Cannot find available products to create the stereo.</string>
    <string name="you_can_perform_a_calibration_from_the_control_page_later">You can perform a calibration from the control page later.</string>
    <string name="connect_the_products_to_wi_fi_and_update_to_the_latest_software_first">Connect the products to Wi-Fi and update to the latest software first.</string>
    <string name="sorry_ungrouping_cannot_be_done_at_the_moment_please_try_again_later">Sorry, ungrouping cannot be done at the moment, please try again later.</string>
    <string name="connect_product_to_wi_fi_to_continue">Connect product to Wi-Fi to continue</string>
    <string name="update_product_software_to_continue">Update product software to continue</string>

    <string name="automatically_send_device_usage_and_crash_reports_to_google">Automatically send device usage and crash reports to Google. This setting does not affect your Google Account controls (myaccount.google.com)</string>
    <string name="you_are_ready_to_start_casting">You are ready to start casting</string>
    <string name="to_setup_voice_control_from_a_google_assistant_enabled_speaker">To setup voice control from a Google Assistant-enabled speaker and multiroom groups with Cast-enabled speakers, download the Google Home app.</string>
    <string name="learn_how_to_cast_music">Learn how to cast music</string>

    <string name="tap_settings_below_connect_your_phone_to_a_commonly_used_network_and_come_back">Tap settings below, connect your phone to a commonly used network and come back</string>
    <string name="the_product_is_now_playing_a_tone_press_the_button_to_continue">The product is now playing a tone, press the button to continue.</string>
    <string name="select_product">Select Product:</string>
    <string name="your_products_are_grouped_successfully_however_calibration_cannot_be_done_at_the_moment_you_can_perform_a_calibration_from_the_control_page_later">Your products are grouped successfully. However, calibration cannot be done at the moment.\n\nYou can perform a calibration from the control page later.</string>

    <string name="mixed_with_forest">Mixed with Forest</string>
    <string name="mixed_with_citywalk">Mixed with Citywalk</string>
    <string name="mixed_with_ocean">Mixed with Ocean</string>
    <string name="mixed_with_rain">Mixed with Rain</string>
    <string name="start_tuning">START TUNING</string>


    <string name="placement_reminderString_sideLR_new">Place %s behind the listening area, with one on the left and one on the right. Make sure both speakers are facing you.</string>
    <string name="placement_reminderString_subwoofer_new">Place %s on the floor, facing the listening area.</string>
    <string name="placement_reminderString_center_bar">Place %s in front of the listening area and facing towards you.</string>
    <string name="placement_reminderString_frontLR_new">Place %s in front of the listening area, with one on the left and one on the right. Make sure both speakers are facing you.</string>
    <string name="placement_reminderString_front3">Place %s in front of the listening area. Make sure all speakers are facing you.</string>

    <string name="lets_calibrate_your_product_to_get_the_best_sound">Let’s calibrate your product to get the best sound !</string>

    <string name="perform_a_power_cycle_if_the_product_still_does_not_show_up">If the product still does not show up, power it off and power back on.</string>
    <string name="is_the_speaker_that_is_playing_a_tone_on_the_left_or_on_the_right">Is the speaker that is playing a tone on the left or on the right?</string>
    <string name="play_again">Play Again</string>

    <string name="google_assistant_with_google_cast">Google Assistant with Google Cast</string>
    <string name="with_google_cast">with Google Cast</string>

    <string name="auracast_broadcast_quality">Auracast Broadcast Quality</string>
    <string name="your_product_will_play_music_and_broadcast_in_">Your product will play music and broadcast in high quality by default to offer the best audio performance.\n\nEnable standard quality broadcasting will:</string>
    <string name="stream_audio_and_broadcast_to_other_products_">Stream audio and broadcast to other products in standard quality.</string>
    <string name="enable_compatibility_with_hearing_aid_product_">Enable compatibility with hearing-aid product or other standard audio quality audio receivers.</string>
    <string name="disable_broadcasting_to_other_harman_products">Disable broadcasting to other Harman products.</string>
    <string name="leaving_this_page_will_disable_standard_quality_">Leaving this page will disable standard quality audio streaming and broadcasting.</string>
    <string name="disable">Disable</string>

    <string name="ready_to_play_from_roon_app">Ready to play from Roon app.</string>
    <string name="cant_find_product">Can’t find product?</string>
    <string name="please_restart_your_mobile_device_to_connect_with_product">Please restart your mobile device to connect your products.</string>

    <string name="bluetooth_off">Bluetooth Off</string>
    <string name="gps_off">GPS Off</string>
    <string name="permission_required">Permission Required</string>

    <string name="press_the_shown_button_on_the_product_to_quit">Press the shown button on the product to quit.</string>
    <string name="tuning_tone_will_come_from_your_products_for_space_detection">Tuning tone will come from your products for space detection. </string>
    <string name="keep_quiet_and_do_not_block_your_products_during_the_process">Keep quiet and do not block your products during the process.</string>
    <string name="this_will_erase_all">This will erase all your personal settings and reset the product software to its default factory settings.</string>
    <string name="products_will_be_tuned_for_better_performance_during_grouping">Products will be tuned for better performance during grouping</string>

    <string name="play_music_on_any_added_speaker_to_start_a_party">Play music on any added speaker to start a party.</string>
    <string name="turn_on_gps_to_discover_nearby_product">Turn on GPS to discover nearby products.</string>
    <string name="turn_on">Turn On</string>

    <string name="ambient_audio">Ambient Audio</string>
    <string name="other_settings">Other Settings</string>

    <!--for ss5 2025/2/20-->
    <string name="connected_the_product_to_bluetooth_to_continue">Connected the product to Bluetooth to continue.</string>


    <string name="go_to_settings_to_grant_local_network_2">Go to settings to grant "Local Network" permission to discover nearby products.</string>
    <string name="text_tuning_2">Tuning…</string>
    <string name="jbl_Remote_Controller_2">Remote control</string>
    <string name="newStructure_Rename_is_not_available_Renaming_2">Renaming could not be completed because you have added the product in Apple Home Kit.
        Rename product from the Apple Home app.</string>

    <string name="we_need_your_verifcation_on_the_product_2">Confirmation on the product is needed to proceed. Please try again.</string>
    <string name="jbl_Press_the_____button_on_the_front_right_speaker_to_continue_2">Press the %s button on the front right speaker to continue.</string>
    <string name="multi_channel_There_are_multiple_products_please_check_2">Select products to form a group:</string>
    <string name="multi_channel_Choose__products">Choose %s products:</string>
    <string name="setting_network_2">Wi-Fi Connection</string>
    <string name="rear_speakers_reversed_tips_2">Rear speakers are in reverse position, swap them and recalibrate.</string>
    <string name="Remain_quiet_during_the_calibration_2">Keep the environment quiet during the calibration.</string>
    <string name="re_calibrate_2">RECALIBRATE</string>
    <string name="go_back_to_dashboard_2">GO BACK</string>

    <string name="newStructure_Initialize_Wi_Fi_connection_for_your_product_2">Go to Wi-Fi setup.</string>

    <string name="sorry_the_update_was_not_complete_no_changes_were_made_2">Something went wrong. No changes were made.</string>
    <string name="press_bluetooth_button_2">Set the Product to Pairing Mode</string>

    <string name="jbl_do_you_want_to_exit_the_set_up_process_now_you_can_do_it_later_in_add_product_page_2">Exit the setup process now? You can complete it later in [Settings] > [Add New Product].</string>

    <string name="newStructure_Upgrade_your_listening_experience_with_multi_channel_effect_2">Upgrade your listening experience with multi-channel effect.</string>
    <string name="newStructure_Ready_to_use_on_Spotify_App_2">Ready to play on Spotify app.</string>
    <string name="other_network_2">Other networks</string>
    <string name="choose_networks_2">Choose a network</string>

    <string name="ptl_power_on_desc_2">Make sure the product is powered on.</string>
    <string name="product_cannot_be_connected_at_this_moment_2">Product cannot be connected at the moment.</string>

    <string name="to_connect_your_product_to_wi_fi_connect_your_phone_to_a_network_in_phone_settings_and_continue">To connect your product to Wi-Fi, connect your phone to a network in phone Settings and continue</string>
    <string name="how_to_remove_this_speaker">How to remove this speaker</string>

    <!--update 2025/6/12-->
    <string name="pattern_aurora">AURORA</string>
    <string name="pattern_ocean">OCEAN</string>
    <string name="pattern_blossom">BLOSSOM</string>
    <string name="pattern_fireplace">FIREPLACE</string>
    <string name="pattern_sunrise">SUNRISE</string>
    <string name="pattern_static">STATIC</string>
    <string name="pattern_audio_source">Audio Source</string>
    <string name="audio_source_mood">Mood</string>



    <!--alarm function-->
    <string name="add_alarm">Add Alarm</string>
    <string name="set_alarm">Set Alarm</string>
    <string name="no_alarm">No Alarm</string>
    <string name="light_gap_time">%s min</string>
    <string name="light_on">Light On</string>
    <string name="do_not_turn_off_the_product_unplug_the_power_cable_or_leave_the_app">Do NOT turn off the product, unplug the power cable, or leave the app</string>
    <string name="repeat">Repeat</string>
    <string name="delete_alarm">DELETE ALARM</string>
    <string name="sunrise">Sunrise</string>
    <string name="weekday">Weekday</string>
    <string name="sound">Sound</string>
    <string name="ring_tone">Ring tone</string>
    <string name="radio">Radio</string>
    <string name="alarm_setting">Alarm Setting</string>
    <string name="snooze">Snooze</string>
    <string name="short_press_the_product_knob_will_trigger_something">Short press the product knob will trigger a 10 minutes snooze. The long press can stop the alarm.</string>
    <string name="alarms_light_switch_tip">The sunrise effect gradually brightens before your wake-up time, creating a pleasant experience. At the set-time, the sound plays and the light stays on.</string>
    <string name="alarms_maximum_number_tips">Maximum number of alarms reached.</string>
    <string name="ring_tone_daybreak">Daybreak</string>
    <string name="ring_tone_sunbeam">Sunbeam</string>
    <string name="ring_tone_soft_chimes">Soft Chimes</string>
    <string name="ring_tone_buzzer">Buzzer</string>
    <string name="deep_sleep_mode">Deep Sleep Mode</string>
    <string name="deep_sleep_mode_desc">Product enters Deep Sleep Mode to save energy after 10 minutes of inactivity.</string>
    <string name="deep_sleep_mode_tips">When product enters deep sleep mode, it will consume the least amount of power.\n\nTo wake up from deep sleep mode, press any button or the power button on the remote control. The product will then be ready for use.</string>

    <string name="please_disable_partytogether_on_your_product_to_continue">Please disable PartyTogether on your product to continue.</string>

    <!--for productsetting start-->
    <string name="product_settings">Product Settings</string>
    <string name="feedback_tone">Feedback Tone</string>
    <string name="time_and_date">Time &amp; Date</string>
    <string name="product_language">Product Language</string>
    <string name="restore_factory_settings">RESTORE FACTORY SETTINGS</string>

    <!--time date-->
    <string name="time_sync">Time Sync</string>
    <string name="auto_sync_with_dab">Auto sync with DAB</string>
    <string name="auto_sync_with_mobile_device">Auto sync with mobile device</string>
    <string name="no_auto_sync">No auto sync</string>
    <!--for productsetting end-->

    <!--for screendisplay start-->
    <!--for bedtime-->
    <string name="set_bedtime">Set Bedtime</string>
    <string name="bedtime">Bedtime</string>
    <string name="automatically_switches_screen_brightness_at_your_selected_bedtime">Automatically switches screen brightness at your selected bedtime.</string>
    <string name="bedtime_period">Bedtime Period</string>
    <string name="screen_brightness">Screen brightness</string>
    <string name="during_bedtime_press_knob_will_light_up_the_screen_for_5_seconds">During Bedtime, press knob will light up the screen for 5 seconds.</string>
    <string name="set_time">Set Time</string>
    <!--for screen saver-->
    <string name="set_screen_saver">Set Screen Saver</string>
    <string name="the_display_will_switch_to_selected_screen_saver_after_the_product_is_idle_for_10_minutes">The display will switch to selected screen saver after the product is idle for 10 minutes.</string>
    <!--for screendisplay end-->
    <string name="fm">FM</string>
    <string name="dab">DAB</string>
    <string name="scan">Scan</string>
    <string name="scan_to_find_all_available_stations_it_will_take_a_few_minutes">Scan to find all available stations. It will take a few minutes.</string>
    <string name="scan_to_find_all_stations_it_will_takes_few_minutes">Scan to find all stations. It will take a few minutes.</string>
    <string name="no_station_found">No station found</string>
    <string name="add_to_preset">Add to preset</string>
    <string name="no_preset">No preset</string>

    <string name="failed_to_add">Failed to add</string>
    <string name="failed_to_remove">Failed to remove</string>
    <string name="removed_successfully">Removed successfully</string>
    <string name="sign_in_sign_up">Sign in / Sign up</string>

    <string name="the_product_is_currently_offline_please_reconnect_the_product_and_try_again">The product is currently offline. Please reconnect the product and try again.</string>
    <string name="restart_the_product_make_sure_it_s_not_connected_to_othermobile_devices">Make sure the product is not connected to other mobile devices. If it still cannot be connected, restart to continue. </string>

    <string name="auto_standby">Auto-Standby</string>
    <string name="auto_standby_desc">When not connected to a power source, the product will automatically turn off if no music is playing.</string>
    <string name="auto_standby_x_hour">%d hours</string>

    <string name="connect_to_the_internet_to_continue">Connect to the internet to continue.</string>
    <string name="help_improve_google_cast">Help Improve Google Cast</string>

    <string name="check_your_inbox">Check Your Inbox</string>

</resources>