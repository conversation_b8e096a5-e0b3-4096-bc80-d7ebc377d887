<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.va.VAGuideDialog" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:background="@drawable/radius_large_top_purple_1">

        <TextView
            style="@style/Text_Title_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/dimen_84dp"
            android:paddingBottom="@dimen/dimen_24dp"
            android:text="@string/enable_services_to_enjoy_more"
            android:textColor="@color/fg_primary" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_16dp">

            <ImageView
                android:id="@+id/ic_record_voice_over"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dimen_12dp"
                android:src="@drawable/ic_record_voice_over"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/layout_record_voice_over"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_12dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ic_record_voice_over"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    style="@style/Text_Body_Strong"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/activate_voice_assistants"
                    android:textColor="@color/fg_primary" />

                <TextView
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:text="@string/activate_voice_assistants_desc"
                    android:textColor="@color/fg_secondary" />

                <TextView
                    style="@style/Text_Caption_1_Strong"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:onClick="@{() -> dialog.onLearnMoreClick()}"
                    android:text="@string/learn_more"
                    android:textColor="@color/fg_activate" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_16dp">

            <ImageView
                android:id="@+id/ic_google_cast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dimen_12dp"
                android:src="@drawable/ic_brand_google_cast_fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/layout_google_cast"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_12dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ic_google_cast"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    style="@style/Text_Body_Strong"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/enable_wi_fi_streaming"
                    android:textColor="@color/fg_primary" />

                <TextView
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:text="@string/enable_wi_fi_streaming_desc"
                    android:textColor="@color/fg_secondary" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_setup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_60dp"
            android:onClick="@{() -> dialog.onSetupBtnClick()}"
            app:btn_text="@string/jbl_Setup"
            app:btn_type="btn_regular" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_later"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_60dp"
            android:onClick="@{() -> dialog.onLaterBtnClick()}"
            app:btn_text="@string/harmanbar_jbl_LATER"
            app:btn_type="btn_text_button" />
    </LinearLayout>
</layout>