<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.music.player.MusicPlayerMomentDialog" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_20dp"
        android:paddingBottom="@dimen/dimen_48dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_8dp"
            android:layout_marginBottom="@dimen/dimen_16dp">

            <TextView
                android:id="@+id/tv_customize_moment"
                style="@style/Text_Title_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_10dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/harmanbar_jbl_Moment"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/iv_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="@{() -> dialog.onCloseBtnClick()}"
                android:padding="@dimen/dimen_2dp"
                android:src="@drawable/ic_close_fg_secondary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_customize_moment"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/layout_control"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_32dp"
            android:background="@drawable/radius_medium_bg_on_card"
            android:orientation="vertical"
            android:padding="@dimen/dimen_16dp">

            <TextView
                style="@style/Text_Body_Medium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dimen_2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/mix_with_music"
                android:textColor="@color/fg_primary" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_volume_control"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/dimen_12dp"
                android:paddingStart="@dimen/dimen_5dp"
                android:paddingEnd="@dimen/dimen_15dp">

                <ImageView
                    android:id="@+id/ic_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dimen_15dp"
                    android:src="@drawable/ic_volume"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/sb_volume"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <SeekBar
                    android:id="@+id/sb_volume"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:max="100"
                    android:maxHeight="@dimen/dimen_4dp"
                    android:minHeight="@dimen/dimen_4dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:paddingStart="0dp"
                    android:paddingEnd="0dp"
                    android:progressDrawable="@drawable/progressbar_fg_primary_fg_disabled"
                    android:thumb="@drawable/thumb_radius_12_fg_primary"
                    android:thumbOffset="0dp"
                    android:splitTrack="false"
                    android:background="@null"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ic_volume"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:progress="50" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_stop"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_48dp"
            android:layout_marginStart="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_60dp"
            android:onClick="@{() -> dialog.onStopBtnClick()}"
            app:btn_text="@string/stop"
            app:btn_type="btn_warning"/>
    </LinearLayout>
</layout>