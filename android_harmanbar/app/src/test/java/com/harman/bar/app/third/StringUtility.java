package com.harman.bar.app.third;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import android.content.Context;
import android.preference.PreferenceManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

public class StringUtility {
    private final static String TAG = StringUtility.class.getSimpleName();
    /**
     * method for split a string after every n characters
     * @param text string to split
     * @param n split string after every n character
     */
    public static ArrayList<String> splitEqually(String text, int n) {
        // Give the list the right capacity to start with. You could use an array
        // instead if you wanted.
        ArrayList<String> ret = new ArrayList<String>((text.length() + n - 1) / n);
        String str = "";
        for (int start = 0; start < text.length(); start += n) {
            str = text.substring(start, Math.min(text.length(), start + n));
            if(!str.equalsIgnoreCase("00")) {
                ret.add(str);
            }
        }
        return ret;
    }

    public static String getString(ArrayList<String> list) {
        StringBuilder sb = new StringBuilder();
        for (String s : list) {
            sb.append(s);
            //sb.append("\t");
        }
        String str =  String.format("%-6s", sb.toString() ).replace(' ', '0');
        return str;
    }

    public static String getStringGA(String str) {
        String newString = str.toString().replaceAll("0", "");
        newString = newString.replaceAll("(.)(?!$)", "$1,");
        return newString;
    }

    /*---------------------Data Analytics OTA fail frequency-------- End -----------------------*/
    //Big Endian or Little Endian
    public static  String changeToLittleEndian(String value){
        if(value != null && value.length() == 4){
            String littleEndian = value.substring(2, 4);
            String bigEndian = value.substring(0, 2);
            return littleEndian + bigEndian;
        }
        return value;
    }

    public static String readAssetsFile(Context context, String fileName) {
        String jsonStr = "";
        try {
            InputStream is = context.getResources().getAssets().open(fileName);
            Reader reader = new InputStreamReader(is, StandardCharsets.UTF_8);
            int ch = 0;
            StringBuilder sb = new StringBuilder();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            reader.close();
            is.close();
            jsonStr = sb.toString();
            return jsonStr;
        } catch (IOException e) {
            return null;
        }
    }

    public static HashMap<String, String> getMapFromSP(String key, Context context){
        String defValue = new Gson().toJson(new HashMap<String, String>());
        String json =  PreferenceManager.getDefaultSharedPreferences(context).getString(key, defValue);
        TypeToken<HashMap<String,String>> token = new TypeToken<HashMap<String,String>>() {};
        HashMap<String,String> retrievedMap=new Gson().fromJson(json,token.getType());
        return retrievedMap;
    }

    public static void removeMapKeyFromSP(String key, String mapKey, Context context){
        Map<String, String> map = getMapFromSP(key, context);
        if(map == null){
            return;
        }
        map.remove(mapKey);
        String jsonString = new Gson().toJson(map);
        PreferenceManager.getDefaultSharedPreferences(context).edit().putString(key, jsonString).commit();
    }

    public static String safeSubString(String s,int beginIndex,int endIndex){
        if(s == null || s.isEmpty()){
            return "";
        }
        if (beginIndex < 0) {
            return "";
        }

        int subLen = endIndex - beginIndex;
        if (subLen < 0) {
            return "";
        }

        if (endIndex > s.length()) {
            return s.substring(beginIndex);
        }
        return s.substring(beginIndex,endIndex);

    }
}
