/*
 * Copyright 2010-2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */

package lsaudio.model;

import java.io.Serializable;
import java.util.List;

public class RequestPayloadDataAppAnalytics implements Serializable {
    @com.google.gson.annotations.SerializedName("speakerMode")
    private RequestPayloadDataAppAnalyticsSpeakerMode speakerMode = null;
    @com.google.gson.annotations.SerializedName("appToneToggle")
    private Integer appToneToggle = null;
    @com.google.gson.annotations.SerializedName("appMFBMode")
    private Integer appMFBMode = null;
    @com.google.gson.annotations.SerializedName("appHFPToggle")
    private Integer appHFPToggle = null;
    @com.google.gson.annotations.SerializedName("appEQMode")
    private Integer appEQMode = null;
    @com.google.gson.annotations.SerializedName("otaTriggered")
    private Integer otaTriggered = null;
    @com.google.gson.annotations.SerializedName("otaSuccessful")
    private Integer otaSuccessful = null;
    @com.google.gson.annotations.SerializedName("otaUnsuccessful")
    private Integer otaUnsuccessful = null;
    @com.google.gson.annotations.SerializedName("otaDuration")
    private Integer otaDuration = null;
    @com.google.gson.annotations.SerializedName("appPlatform")
    private String appPlatform = null;
    @com.google.gson.annotations.SerializedName("appPlatVer")
    private String appPlatVer = null;
    @com.google.gson.annotations.SerializedName("mobDevBrand")
    private String mobDevBrand = null;
    @com.google.gson.annotations.SerializedName("mobModel")
    private String mobModel = null;
    @com.google.gson.annotations.SerializedName("appVolume")
    private Integer appVolume = null;
    @com.google.gson.annotations.SerializedName("appDurationJBLConnect")
    private Integer appDurationJBLConnect = null;
    @com.google.gson.annotations.SerializedName("appLightT1")
    private Integer appLightT1 = null;
    @com.google.gson.annotations.SerializedName("appLightT2")
    private Integer appLightT2 = null;
    @com.google.gson.annotations.SerializedName("appLightT3")
    private Integer appLightT3 = null;
    @com.google.gson.annotations.SerializedName("appLightT4")
    private Integer appLightT4 = null;
    @com.google.gson.annotations.SerializedName("appLightT5")
    private Integer appLightT5 = null;
    @com.google.gson.annotations.SerializedName("appLightT6")
    private Integer appLightT6 = null;
    @com.google.gson.annotations.SerializedName("appLightT7")
    private Integer appLightT7 = null;
    @com.google.gson.annotations.SerializedName("appLightT8")
    private Integer appLightT8 = null;
    @com.google.gson.annotations.SerializedName("appLightT9")
    private Integer appLightT9 = null;
    @com.google.gson.annotations.SerializedName("appLightT10")
    private Integer appLightT10 = null;
    @com.google.gson.annotations.SerializedName("appDeviceDisovered")
    private Integer appDeviceDisovered = null;
    @com.google.gson.annotations.SerializedName("appANC")
    private Integer appANC = null;
    @com.google.gson.annotations.SerializedName("appEQ")
    private Integer appEQ = null;
    @com.google.gson.annotations.SerializedName("appEQChange")
    private Integer appEQChange = null;
    @com.google.gson.annotations.SerializedName("appAwareness")
    private Integer appAwareness = null;
    @com.google.gson.annotations.SerializedName("appAutoCalibration")
    private Integer appAutoCalibration = null;
    @com.google.gson.annotations.SerializedName("appSmartButton")
    private Integer appSmartButton = null;
    @com.google.gson.annotations.SerializedName("appSmartAmbient")
    private Integer appSmartAmbient = null;
    @com.google.gson.annotations.SerializedName("otaSuccess")
    private Integer otaSuccess = null;
    @com.google.gson.annotations.SerializedName("otaSuccessSize")
    private Integer otaSuccessSize = null;
    @com.google.gson.annotations.SerializedName("otaSuccessDuration")
    private Integer otaSuccessDuration = null;
    @com.google.gson.annotations.SerializedName("otaFail")
    private Integer otaFail = null;
    @com.google.gson.annotations.SerializedName("otaFailSize")
    private Integer otaFailSize = null;
    @com.google.gson.annotations.SerializedName("otaFailDuration")
    private Integer otaFailDuration = null;
    @com.google.gson.annotations.SerializedName("appAutoOff")
    private Integer appAutoOff = null;
    @com.google.gson.annotations.SerializedName("appVoicePrompt")
    private Integer appVoicePrompt = null;
    @com.google.gson.annotations.SerializedName("appAmbientAware")
    private Integer appAmbientAware = null;
    @com.google.gson.annotations.SerializedName("appTalkThru")
    private Integer appTalkThru = null;
    @com.google.gson.annotations.SerializedName("appSmartAssistant")
    private Integer appSmartAssistant = null;
    @com.google.gson.annotations.SerializedName("appFindMyBuds")
    private Integer appFindMyBuds = null;
    @com.google.gson.annotations.SerializedName("appDJStage")
    private Integer appDJStage = null;
    @com.google.gson.annotations.SerializedName("appDJStageChange")
    private Integer appDJStageChange = null;
    @com.google.gson.annotations.SerializedName("appPersoniFi")
    private Integer appPersoniFi = null;
    @com.google.gson.annotations.SerializedName("appPersoniFiSetup")
    private Integer appPersoniFiSetup = null;
    @com.google.gson.annotations.SerializedName("toneTriggered")
    private Integer toneTriggered = null;
    @com.google.gson.annotations.SerializedName("toneSuccess")
    private Integer toneSuccess = null;
    @com.google.gson.annotations.SerializedName("toneSuccessSize")
    private Integer toneSuccessSize = null;
    @com.google.gson.annotations.SerializedName("toneSuccessDuration")
    private Integer toneSuccessDuration = null;
    @com.google.gson.annotations.SerializedName("toneFail")
    private Integer toneFail = null;
    @com.google.gson.annotations.SerializedName("personifiStartSetup")
    private Integer personifiStartSetup = null;
    @com.google.gson.annotations.SerializedName("profileStarted")
    private Integer profileStarted = null;
    @com.google.gson.annotations.SerializedName("profileCompleted")
    private Integer profileCompleted = null;
    @com.google.gson.annotations.SerializedName("profileCanceled")
    private Integer profileCanceled = null;
    @com.google.gson.annotations.SerializedName("hearingTestStarted")
    private Integer hearingTestStarted = null;
    @com.google.gson.annotations.SerializedName("hearingTestCompleted")
    private Integer hearingTestCompleted = null;
    @com.google.gson.annotations.SerializedName("hearingTestCanceled")
    private Integer hearingTestCanceled = null;
    @com.google.gson.annotations.SerializedName("customizeEQ")
    private Integer customizeEQ = null;
    @com.google.gson.annotations.SerializedName("appAnalyticsCustomParams")
    private List<String> appAnalyticsCustomParams = null;

    /**
     * Gets speakerMode
     *
     * @return speakerMode
     **/
    public RequestPayloadDataAppAnalyticsSpeakerMode getSpeakerMode() {
        return speakerMode;
    }

    /**
     * Sets the value of speakerMode.
     *
     * @param speakerMode the new value
     */
    public void setSpeakerMode(RequestPayloadDataAppAnalyticsSpeakerMode speakerMode) {
        this.speakerMode = speakerMode;
    }

    /**
     * Gets appToneToggle
     *
     * @return appToneToggle
     **/
    public Integer getAppToneToggle() {
        return appToneToggle;
    }

    /**
     * Sets the value of appToneToggle.
     *
     * @param appToneToggle the new value
     */
    public void setAppToneToggle(Integer appToneToggle) {
        this.appToneToggle = appToneToggle;
    }

    /**
     * Gets appMFBMode
     *
     * @return appMFBMode
     **/
    public Integer getAppMFBMode() {
        return appMFBMode;
    }

    /**
     * Sets the value of appMFBMode.
     *
     * @param appMFBMode the new value
     */
    public void setAppMFBMode(Integer appMFBMode) {
        this.appMFBMode = appMFBMode;
    }

    /**
     * Gets appHFPToggle
     *
     * @return appHFPToggle
     **/
    public Integer getAppHFPToggle() {
        return appHFPToggle;
    }

    /**
     * Sets the value of appHFPToggle.
     *
     * @param appHFPToggle the new value
     */
    public void setAppHFPToggle(Integer appHFPToggle) {
        this.appHFPToggle = appHFPToggle;
    }

    /**
     * Gets appEQMode
     *
     * @return appEQMode
     **/
    public Integer getAppEQMode() {
        return appEQMode;
    }

    /**
     * Sets the value of appEQMode.
     *
     * @param appEQMode the new value
     */
    public void setAppEQMode(Integer appEQMode) {
        this.appEQMode = appEQMode;
    }

    /**
     * Gets otaTriggered
     *
     * @return otaTriggered
     **/
    public Integer getOtaTriggered() {
        return otaTriggered;
    }

    /**
     * Sets the value of otaTriggered.
     *
     * @param otaTriggered the new value
     */
    public void setOtaTriggered(Integer otaTriggered) {
        this.otaTriggered = otaTriggered;
    }

    /**
     * Gets otaSuccessful
     *
     * @return otaSuccessful
     **/
    public Integer getOtaSuccessful() {
        return otaSuccessful;
    }

    /**
     * Sets the value of otaSuccessful.
     *
     * @param otaSuccessful the new value
     */
    public void setOtaSuccessful(Integer otaSuccessful) {
        this.otaSuccessful = otaSuccessful;
    }

    /**
     * Gets otaUnsuccessful
     *
     * @return otaUnsuccessful
     **/
    public Integer getOtaUnsuccessful() {
        return otaUnsuccessful;
    }

    /**
     * Sets the value of otaUnsuccessful.
     *
     * @param otaUnsuccessful the new value
     */
    public void setOtaUnsuccessful(Integer otaUnsuccessful) {
        this.otaUnsuccessful = otaUnsuccessful;
    }

    /**
     * Gets otaDuration
     *
     * @return otaDuration
     **/
    public Integer getOtaDuration() {
        return otaDuration;
    }

    /**
     * Sets the value of otaDuration.
     *
     * @param otaDuration the new value
     */
    public void setOtaDuration(Integer otaDuration) {
        this.otaDuration = otaDuration;
    }

    /**
     * Gets appPlatform
     *
     * @return appPlatform
     **/
    public String getAppPlatform() {
        return appPlatform;
    }

    /**
     * Sets the value of appPlatform.
     *
     * @param appPlatform the new value
     */
    public void setAppPlatform(String appPlatform) {
        this.appPlatform = appPlatform;
    }

    /**
     * Gets appPlatVer
     *
     * @return appPlatVer
     **/
    public String getAppPlatVer() {
        return appPlatVer;
    }

    /**
     * Sets the value of appPlatVer.
     *
     * @param appPlatVer the new value
     */
    public void setAppPlatVer(String appPlatVer) {
        this.appPlatVer = appPlatVer;
    }

    /**
     * Gets mobDevBrand
     *
     * @return mobDevBrand
     **/
    public String getMobDevBrand() {
        return mobDevBrand;
    }

    /**
     * Sets the value of mobDevBrand.
     *
     * @param mobDevBrand the new value
     */
    public void setMobDevBrand(String mobDevBrand) {
        this.mobDevBrand = mobDevBrand;
    }

    /**
     * Gets mobModel
     *
     * @return mobModel
     **/
    public String getMobModel() {
        return mobModel;
    }

    /**
     * Sets the value of mobModel.
     *
     * @param mobModel the new value
     */
    public void setMobModel(String mobModel) {
        this.mobModel = mobModel;
    }

    /**
     * Gets appVolume
     *
     * @return appVolume
     **/
    public Integer getAppVolume() {
        return appVolume;
    }

    /**
     * Sets the value of appVolume.
     *
     * @param appVolume the new value
     */
    public void setAppVolume(Integer appVolume) {
        this.appVolume = appVolume;
    }

    /**
     * Gets appDurationJBLConnect
     *
     * @return appDurationJBLConnect
     **/
    public Integer getAppDurationJBLConnect() {
        return appDurationJBLConnect;
    }

    /**
     * Sets the value of appDurationJBLConnect.
     *
     * @param appDurationJBLConnect the new value
     */
    public void setAppDurationJBLConnect(Integer appDurationJBLConnect) {
        this.appDurationJBLConnect = appDurationJBLConnect;
    }

    /**
     * Gets appLightT1
     *
     * @return appLightT1
     **/
    public Integer getAppLightT1() {
        return appLightT1;
    }

    /**
     * Sets the value of appLightT1.
     *
     * @param appLightT1 the new value
     */
    public void setAppLightT1(Integer appLightT1) {
        this.appLightT1 = appLightT1;
    }

    /**
     * Gets appLightT2
     *
     * @return appLightT2
     **/
    public Integer getAppLightT2() {
        return appLightT2;
    }

    /**
     * Sets the value of appLightT2.
     *
     * @param appLightT2 the new value
     */
    public void setAppLightT2(Integer appLightT2) {
        this.appLightT2 = appLightT2;
    }

    /**
     * Gets appLightT3
     *
     * @return appLightT3
     **/
    public Integer getAppLightT3() {
        return appLightT3;
    }

    /**
     * Sets the value of appLightT3.
     *
     * @param appLightT3 the new value
     */
    public void setAppLightT3(Integer appLightT3) {
        this.appLightT3 = appLightT3;
    }

    /**
     * Gets appLightT4
     *
     * @return appLightT4
     **/
    public Integer getAppLightT4() {
        return appLightT4;
    }

    /**
     * Sets the value of appLightT4.
     *
     * @param appLightT4 the new value
     */
    public void setAppLightT4(Integer appLightT4) {
        this.appLightT4 = appLightT4;
    }

    /**
     * Gets appLightT5
     *
     * @return appLightT5
     **/
    public Integer getAppLightT5() {
        return appLightT5;
    }

    /**
     * Sets the value of appLightT5.
     *
     * @param appLightT5 the new value
     */
    public void setAppLightT5(Integer appLightT5) {
        this.appLightT5 = appLightT5;
    }

    /**
     * Gets appLightT6
     *
     * @return appLightT6
     **/
    public Integer getAppLightT6() {
        return appLightT6;
    }

    /**
     * Sets the value of appLightT6.
     *
     * @param appLightT6 the new value
     */
    public void setAppLightT6(Integer appLightT6) {
        this.appLightT6 = appLightT6;
    }

    /**
     * Gets appLightT7
     *
     * @return appLightT7
     **/
    public Integer getAppLightT7() {
        return appLightT7;
    }

    /**
     * Sets the value of appLightT7.
     *
     * @param appLightT7 the new value
     */
    public void setAppLightT7(Integer appLightT7) {
        this.appLightT7 = appLightT7;
    }

    /**
     * Gets appLightT8
     *
     * @return appLightT8
     **/
    public Integer getAppLightT8() {
        return appLightT8;
    }

    /**
     * Sets the value of appLightT8.
     *
     * @param appLightT8 the new value
     */
    public void setAppLightT8(Integer appLightT8) {
        this.appLightT8 = appLightT8;
    }

    /**
     * Gets appLightT9
     *
     * @return appLightT9
     **/
    public Integer getAppLightT9() {
        return appLightT9;
    }

    /**
     * Sets the value of appLightT9.
     *
     * @param appLightT9 the new value
     */
    public void setAppLightT9(Integer appLightT9) {
        this.appLightT9 = appLightT9;
    }

    /**
     * Gets appLightT10
     *
     * @return appLightT10
     **/
    public Integer getAppLightT10() {
        return appLightT10;
    }

    /**
     * Sets the value of appLightT10.
     *
     * @param appLightT10 the new value
     */
    public void setAppLightT10(Integer appLightT10) {
        this.appLightT10 = appLightT10;
    }

    /**
     * Gets appDeviceDisovered
     *
     * @return appDeviceDisovered
     **/
    public Integer getAppDeviceDisovered() {
        return appDeviceDisovered;
    }

    /**
     * Sets the value of appDeviceDisovered.
     *
     * @param appDeviceDisovered the new value
     */
    public void setAppDeviceDisovered(Integer appDeviceDisovered) {
        this.appDeviceDisovered = appDeviceDisovered;
    }

    /**
     * Gets appANC
     *
     * @return appANC
     **/
    public Integer getAppANC() {
        return appANC;
    }

    /**
     * Sets the value of appANC.
     *
     * @param appANC the new value
     */
    public void setAppANC(Integer appANC) {
        this.appANC = appANC;
    }

    /**
     * Gets appEQ
     *
     * @return appEQ
     **/
    public Integer getAppEQ() {
        return appEQ;
    }

    /**
     * Sets the value of appEQ.
     *
     * @param appEQ the new value
     */
    public void setAppEQ(Integer appEQ) {
        this.appEQ = appEQ;
    }

    /**
     * Gets appEQChange
     *
     * @return appEQChange
     **/
    public Integer getAppEQChange() {
        return appEQChange;
    }

    /**
     * Sets the value of appEQChange.
     *
     * @param appEQChange the new value
     */
    public void setAppEQChange(Integer appEQChange) {
        this.appEQChange = appEQChange;
    }

    /**
     * Gets appAwareness
     *
     * @return appAwareness
     **/
    public Integer getAppAwareness() {
        return appAwareness;
    }

    /**
     * Sets the value of appAwareness.
     *
     * @param appAwareness the new value
     */
    public void setAppAwareness(Integer appAwareness) {
        this.appAwareness = appAwareness;
    }

    /**
     * Gets appAutoCalibration
     *
     * @return appAutoCalibration
     **/
    public Integer getAppAutoCalibration() {
        return appAutoCalibration;
    }

    /**
     * Sets the value of appAutoCalibration.
     *
     * @param appAutoCalibration the new value
     */
    public void setAppAutoCalibration(Integer appAutoCalibration) {
        this.appAutoCalibration = appAutoCalibration;
    }

    /**
     * Gets appSmartButton
     *
     * @return appSmartButton
     **/
    public Integer getAppSmartButton() {
        return appSmartButton;
    }

    /**
     * Sets the value of appSmartButton.
     *
     * @param appSmartButton the new value
     */
    public void setAppSmartButton(Integer appSmartButton) {
        this.appSmartButton = appSmartButton;
    }

    /**
     * Gets appSmartAmbient
     *
     * @return appSmartAmbient
     **/
    public Integer getAppSmartAmbient() {
        return appSmartAmbient;
    }

    /**
     * Sets the value of appSmartAmbient.
     *
     * @param appSmartAmbient the new value
     */
    public void setAppSmartAmbient(Integer appSmartAmbient) {
        this.appSmartAmbient = appSmartAmbient;
    }

    /**
     * Gets otaSuccess
     *
     * @return otaSuccess
     **/
    public Integer getOtaSuccess() {
        return otaSuccess;
    }

    /**
     * Sets the value of otaSuccess.
     *
     * @param otaSuccess the new value
     */
    public void setOtaSuccess(Integer otaSuccess) {
        this.otaSuccess = otaSuccess;
    }

    /**
     * Gets otaSuccessSize
     *
     * @return otaSuccessSize
     **/
    public Integer getOtaSuccessSize() {
        return otaSuccessSize;
    }

    /**
     * Sets the value of otaSuccessSize.
     *
     * @param otaSuccessSize the new value
     */
    public void setOtaSuccessSize(Integer otaSuccessSize) {
        this.otaSuccessSize = otaSuccessSize;
    }

    /**
     * Gets otaSuccessDuration
     *
     * @return otaSuccessDuration
     **/
    public Integer getOtaSuccessDuration() {
        return otaSuccessDuration;
    }

    /**
     * Sets the value of otaSuccessDuration.
     *
     * @param otaSuccessDuration the new value
     */
    public void setOtaSuccessDuration(Integer otaSuccessDuration) {
        this.otaSuccessDuration = otaSuccessDuration;
    }

    /**
     * Gets otaFail
     *
     * @return otaFail
     **/
    public Integer getOtaFail() {
        return otaFail;
    }

    /**
     * Sets the value of otaFail.
     *
     * @param otaFail the new value
     */
    public void setOtaFail(Integer otaFail) {
        this.otaFail = otaFail;
    }

    /**
     * Gets otaFailSize
     *
     * @return otaFailSize
     **/
    public Integer getOtaFailSize() {
        return otaFailSize;
    }

    /**
     * Sets the value of otaFailSize.
     *
     * @param otaFailSize the new value
     */
    public void setOtaFailSize(Integer otaFailSize) {
        this.otaFailSize = otaFailSize;
    }

    /**
     * Gets otaFailDuration
     *
     * @return otaFailDuration
     **/
    public Integer getOtaFailDuration() {
        return otaFailDuration;
    }

    /**
     * Sets the value of otaFailDuration.
     *
     * @param otaFailDuration the new value
     */
    public void setOtaFailDuration(Integer otaFailDuration) {
        this.otaFailDuration = otaFailDuration;
    }

    /**
     * Gets appAutoOff
     *
     * @return appAutoOff
     **/
    public Integer getAppAutoOff() {
        return appAutoOff;
    }

    /**
     * Sets the value of appAutoOff.
     *
     * @param appAutoOff the new value
     */
    public void setAppAutoOff(Integer appAutoOff) {
        this.appAutoOff = appAutoOff;
    }

    /**
     * Gets appVoicePrompt
     *
     * @return appVoicePrompt
     **/
    public Integer getAppVoicePrompt() {
        return appVoicePrompt;
    }

    /**
     * Sets the value of appVoicePrompt.
     *
     * @param appVoicePrompt the new value
     */
    public void setAppVoicePrompt(Integer appVoicePrompt) {
        this.appVoicePrompt = appVoicePrompt;
    }

    /**
     * Gets appAmbientAware
     *
     * @return appAmbientAware
     **/
    public Integer getAppAmbientAware() {
        return appAmbientAware;
    }

    /**
     * Sets the value of appAmbientAware.
     *
     * @param appAmbientAware the new value
     */
    public void setAppAmbientAware(Integer appAmbientAware) {
        this.appAmbientAware = appAmbientAware;
    }

    /**
     * Gets appTalkThru
     *
     * @return appTalkThru
     **/
    public Integer getAppTalkThru() {
        return appTalkThru;
    }

    /**
     * Sets the value of appTalkThru.
     *
     * @param appTalkThru the new value
     */
    public void setAppTalkThru(Integer appTalkThru) {
        this.appTalkThru = appTalkThru;
    }

    /**
     * Gets appSmartAssistant
     *
     * @return appSmartAssistant
     **/
    public Integer getAppSmartAssistant() {
        return appSmartAssistant;
    }

    /**
     * Sets the value of appSmartAssistant.
     *
     * @param appSmartAssistant the new value
     */
    public void setAppSmartAssistant(Integer appSmartAssistant) {
        this.appSmartAssistant = appSmartAssistant;
    }

    /**
     * Gets appFindMyBuds
     *
     * @return appFindMyBuds
     **/
    public Integer getAppFindMyBuds() {
        return appFindMyBuds;
    }

    /**
     * Sets the value of appFindMyBuds.
     *
     * @param appFindMyBuds the new value
     */
    public void setAppFindMyBuds(Integer appFindMyBuds) {
        this.appFindMyBuds = appFindMyBuds;
    }

    /**
     * Gets appDJStage
     *
     * @return appDJStage
     **/
    public Integer getAppDJStage() {
        return appDJStage;
    }

    /**
     * Sets the value of appDJStage.
     *
     * @param appDJStage the new value
     */
    public void setAppDJStage(Integer appDJStage) {
        this.appDJStage = appDJStage;
    }

    /**
     * Gets appDJStageChange
     *
     * @return appDJStageChange
     **/
    public Integer getAppDJStageChange() {
        return appDJStageChange;
    }

    /**
     * Sets the value of appDJStageChange.
     *
     * @param appDJStageChange the new value
     */
    public void setAppDJStageChange(Integer appDJStageChange) {
        this.appDJStageChange = appDJStageChange;
    }

    /**
     * Gets appPersoniFi
     *
     * @return appPersoniFi
     **/
    public Integer getAppPersoniFi() {
        return appPersoniFi;
    }

    /**
     * Sets the value of appPersoniFi.
     *
     * @param appPersoniFi the new value
     */
    public void setAppPersoniFi(Integer appPersoniFi) {
        this.appPersoniFi = appPersoniFi;
    }

    /**
     * Gets appPersoniFiSetup
     *
     * @return appPersoniFiSetup
     **/
    public Integer getAppPersoniFiSetup() {
        return appPersoniFiSetup;
    }

    /**
     * Sets the value of appPersoniFiSetup.
     *
     * @param appPersoniFiSetup the new value
     */
    public void setAppPersoniFiSetup(Integer appPersoniFiSetup) {
        this.appPersoniFiSetup = appPersoniFiSetup;
    }

    /**
     * Gets toneTriggered
     *
     * @return toneTriggered
     **/
    public Integer getToneTriggered() {
        return toneTriggered;
    }

    /**
     * Sets the value of toneTriggered.
     *
     * @param toneTriggered the new value
     */
    public void setToneTriggered(Integer toneTriggered) {
        this.toneTriggered = toneTriggered;
    }

    /**
     * Gets toneSuccess
     *
     * @return toneSuccess
     **/
    public Integer getToneSuccess() {
        return toneSuccess;
    }

    /**
     * Sets the value of toneSuccess.
     *
     * @param toneSuccess the new value
     */
    public void setToneSuccess(Integer toneSuccess) {
        this.toneSuccess = toneSuccess;
    }

    /**
     * Gets toneSuccessSize
     *
     * @return toneSuccessSize
     **/
    public Integer getToneSuccessSize() {
        return toneSuccessSize;
    }

    /**
     * Sets the value of toneSuccessSize.
     *
     * @param toneSuccessSize the new value
     */
    public void setToneSuccessSize(Integer toneSuccessSize) {
        this.toneSuccessSize = toneSuccessSize;
    }

    /**
     * Gets toneSuccessDuration
     *
     * @return toneSuccessDuration
     **/
    public Integer getToneSuccessDuration() {
        return toneSuccessDuration;
    }

    /**
     * Sets the value of toneSuccessDuration.
     *
     * @param toneSuccessDuration the new value
     */
    public void setToneSuccessDuration(Integer toneSuccessDuration) {
        this.toneSuccessDuration = toneSuccessDuration;
    }

    /**
     * Gets toneFail
     *
     * @return toneFail
     **/
    public Integer getToneFail() {
        return toneFail;
    }

    /**
     * Sets the value of toneFail.
     *
     * @param toneFail the new value
     */
    public void setToneFail(Integer toneFail) {
        this.toneFail = toneFail;
    }

    /**
     * Gets personifiStartSetup
     *
     * @return personifiStartSetup
     **/
    public Integer getPersonifiStartSetup() {
        return personifiStartSetup;
    }

    /**
     * Sets the value of personifiStartSetup.
     *
     * @param personifiStartSetup the new value
     */
    public void setPersonifiStartSetup(Integer personifiStartSetup) {
        this.personifiStartSetup = personifiStartSetup;
    }

    /**
     * Gets profileStarted
     *
     * @return profileStarted
     **/
    public Integer getProfileStarted() {
        return profileStarted;
    }

    /**
     * Sets the value of profileStarted.
     *
     * @param profileStarted the new value
     */
    public void setProfileStarted(Integer profileStarted) {
        this.profileStarted = profileStarted;
    }

    /**
     * Gets profileCompleted
     *
     * @return profileCompleted
     **/
    public Integer getProfileCompleted() {
        return profileCompleted;
    }

    /**
     * Sets the value of profileCompleted.
     *
     * @param profileCompleted the new value
     */
    public void setProfileCompleted(Integer profileCompleted) {
        this.profileCompleted = profileCompleted;
    }

    /**
     * Gets profileCanceled
     *
     * @return profileCanceled
     **/
    public Integer getProfileCanceled() {
        return profileCanceled;
    }

    /**
     * Sets the value of profileCanceled.
     *
     * @param profileCanceled the new value
     */
    public void setProfileCanceled(Integer profileCanceled) {
        this.profileCanceled = profileCanceled;
    }

    /**
     * Gets hearingTestStarted
     *
     * @return hearingTestStarted
     **/
    public Integer getHearingTestStarted() {
        return hearingTestStarted;
    }

    /**
     * Sets the value of hearingTestStarted.
     *
     * @param hearingTestStarted the new value
     */
    public void setHearingTestStarted(Integer hearingTestStarted) {
        this.hearingTestStarted = hearingTestStarted;
    }

    /**
     * Gets hearingTestCompleted
     *
     * @return hearingTestCompleted
     **/
    public Integer getHearingTestCompleted() {
        return hearingTestCompleted;
    }

    /**
     * Sets the value of hearingTestCompleted.
     *
     * @param hearingTestCompleted the new value
     */
    public void setHearingTestCompleted(Integer hearingTestCompleted) {
        this.hearingTestCompleted = hearingTestCompleted;
    }

    /**
     * Gets hearingTestCanceled
     *
     * @return hearingTestCanceled
     **/
    public Integer getHearingTestCanceled() {
        return hearingTestCanceled;
    }

    /**
     * Sets the value of hearingTestCanceled.
     *
     * @param hearingTestCanceled the new value
     */
    public void setHearingTestCanceled(Integer hearingTestCanceled) {
        this.hearingTestCanceled = hearingTestCanceled;
    }

    /**
     * Gets customizeEQ
     *
     * @return customizeEQ
     **/
    public Integer getCustomizeEQ() {
        return customizeEQ;
    }

    /**
     * Sets the value of customizeEQ.
     *
     * @param customizeEQ the new value
     */
    public void setCustomizeEQ(Integer customizeEQ) {
        this.customizeEQ = customizeEQ;
    }

    /**
     * Gets appAnalyticsCustomParams
     *
     * @return appAnalyticsCustomParams
     **/
    public List<String> getAppAnalyticsCustomParams() {
        return appAnalyticsCustomParams;
    }

    /**
     * Sets the value of appAnalyticsCustomParams.
     *
     * @param appAnalyticsCustomParams the new value
     */
    public void setAppAnalyticsCustomParams(List<String> appAnalyticsCustomParams) {
        this.appAnalyticsCustomParams = appAnalyticsCustomParams;
    }

}
