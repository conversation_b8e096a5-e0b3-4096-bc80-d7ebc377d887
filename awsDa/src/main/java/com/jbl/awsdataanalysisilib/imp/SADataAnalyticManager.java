package com.jbl.awsdataanalysisilib.imp;

import android.content.Context;

import androidx.appcompat.widget.AppCompatImageView;

import com.google.gson.Gson;
import com.harman.log.Logger;
import com.jbl.awsdataanalysisilib.BuildConfig;
import com.jbl.awsdataanalysisilib.api.ISADataAnalytics;

import lsaudio.model.RequestPayload;
import lsaudio.model.RequestPayloadData;
import lsaudio.model.RequestPayloadDataAppAnalytics;
import lsaudio.model.RequestPayloadDataAppAnalyticsSpeakerMode;
import lsaudio.model.RequestPayloadDataDeviceAnalytics;
import lsaudio.model.RequestPayloadDataHarmanDevice;

public class SADataAnalyticManager extends BaseDataAnalyticManager implements ISADataAnalytics {
    private static final String TAG = "BaseDataAnalyticManager";

    private static volatile SADataAnalyticManager instance;

    public enum APPNormalCount {
        ToneToggle,
        MFBMode,
        HFPToggle,
        EQMode,
        OTATriggered,
        OTASuccessful,
        OTAUnsuccessful,
        OTADuration,
        AppVolume,
        AppDurationJBLConnect,
        STEREO,
        PARTY,
        SINGLE,
        CustomizeEQ
    }

    public static SADataAnalyticManager getInstance() {
        if (instance == null) {
            synchronized (SADataAnalyticManager.class) {
                if (instance == null) {
                    instance = new SADataAnalyticManager();
                }
            }
        }
        return instance;
    }

    @Override
    public boolean init(Context ctx) {
        eSource = SOURCE.SMART_AUDIO;
        return super.init(ctx);
    }

    @Override
    public RequestPayload getInitializedRequestPayload() {
        RequestPayload requestPayload = new RequestPayload();
        requestPayload.setData(new RequestPayloadData());
        requestPayload.getData().setHarmanDevice(new RequestPayloadDataHarmanDevice());
        RequestPayloadDataAppAnalytics requestPayloadDataAppAnalytics = new RequestPayloadDataAppAnalytics();
        RequestPayloadDataAppAnalyticsSpeakerMode speakerMode = new RequestPayloadDataAppAnalyticsSpeakerMode();
        speakerMode.setSingle(0);
        speakerMode.setParty(0);
        speakerMode.setStereo(0);
        requestPayloadDataAppAnalytics.setSpeakerMode(speakerMode);
        requestPayloadDataAppAnalytics.setAppToneToggle(0);
        requestPayloadDataAppAnalytics.setAppMFBMode(0);
        requestPayloadDataAppAnalytics.setAppHFPToggle(0);
        requestPayloadDataAppAnalytics.setAppEQMode(0);
        requestPayloadDataAppAnalytics.setOtaTriggered(0);
        requestPayloadDataAppAnalytics.setOtaSuccessful(0);
        requestPayloadDataAppAnalytics.setOtaUnsuccessful(0);
        requestPayloadDataAppAnalytics.setOtaDuration(0);
        requestPayloadDataAppAnalytics.setAppPlatform("");
        requestPayloadDataAppAnalytics.setAppPlatVer("");
        requestPayloadDataAppAnalytics.setMobDevBrand("");
        requestPayloadDataAppAnalytics.setMobModel("");
        requestPayloadDataAppAnalytics.setAppVolume(0);
        requestPayloadDataAppAnalytics.setAppDurationJBLConnect(0);
        requestPayloadDataAppAnalytics.setCustomizeEQ(0);
        requestPayloadDataAppAnalytics.setAppLightT1(0);
        requestPayloadDataAppAnalytics.setAppLightT2(0);
        requestPayloadDataAppAnalytics.setAppLightT3(0);
        requestPayloadDataAppAnalytics.setAppLightT4(0);
        requestPayloadDataAppAnalytics.setAppLightT5(0);
        requestPayloadDataAppAnalytics.setAppLightT6(0);
        requestPayloadDataAppAnalytics.setAppLightT7(0);
        requestPayloadDataAppAnalytics.setAppLightT8(0);
        requestPayloadDataAppAnalytics.setAppLightT9(0);
        requestPayloadDataAppAnalytics.setAppLightT10(0);
        requestPayload.getData().setAppAnalytics(requestPayloadDataAppAnalytics);
        return requestPayload;
    }

    @Override
    RequestPayload unInitRequestPayload() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        RequestPayloadDataAppAnalyticsSpeakerMode speakerMode = new RequestPayloadDataAppAnalyticsSpeakerMode();
        speakerMode.setSingle(0);
        speakerMode.setParty(0);
        speakerMode.setStereo(0);
        requestPayload.getData().getAppAnalytics().setSpeakerMode(speakerMode);
        requestPayload.getData().getAppAnalytics().setAppToneToggle(0);
        requestPayload.getData().getAppAnalytics().setAppMFBMode(0);
        requestPayload.getData().getAppAnalytics().setAppHFPToggle(0);
        requestPayload.getData().getAppAnalytics().setAppEQMode(0);
        requestPayload.getData().getAppAnalytics().setOtaTriggered(0);
        requestPayload.getData().getAppAnalytics().setOtaSuccessful(0);
        requestPayload.getData().getAppAnalytics().setOtaUnsuccessful(0);
        requestPayload.getData().getAppAnalytics().setOtaDuration(0);
        requestPayload.getData().getAppAnalytics().setAppPlatform("");
        requestPayload.getData().getAppAnalytics().setAppPlatVer("");
        requestPayload.getData().getAppAnalytics().setMobDevBrand("");
        requestPayload.getData().getAppAnalytics().setMobModel("");
        requestPayload.getData().getAppAnalytics().setAppVolume(0);
        requestPayload.getData().getAppAnalytics().setAppDurationJBLConnect(0);
        requestPayload.getData().getAppAnalytics().setCustomizeEQ(0);
        requestPayload.getData().getAppAnalytics().setAppLightT1(0);
        requestPayload.getData().getAppAnalytics().setAppLightT2(0);
        requestPayload.getData().getAppAnalytics().setAppLightT3(0);
        requestPayload.getData().getAppAnalytics().setAppLightT4(0);
        requestPayload.getData().getAppAnalytics().setAppLightT5(0);
        requestPayload.getData().getAppAnalytics().setAppLightT6(0);
        requestPayload.getData().getAppAnalytics().setAppLightT7(0);
        requestPayload.getData().getAppAnalytics().setAppLightT8(0);
        requestPayload.getData().getAppAnalytics().setAppLightT9(0);
        requestPayload.getData().getAppAnalytics().setAppLightT10(0);
        return requestPayload;
    }

    @Override
    boolean checkPayloadData(RequestPayload requestPayload, boolean isSupportDeviceDa) {
        RequestPayloadDataHarmanDevice harmanDevice = requestPayload.getData().getHarmanDevice();
        RequestPayloadDataDeviceAnalytics deviceAnalytics = requestPayload.getData().getDeviceAnalytics();
        if (harmanDevice == null || !checkHarmanDevice(harmanDevice)) {
            Logger.d(TAG, "checkPayloadData HarmanDevice contains null");
            return false;
        }

        if (isSupportDeviceDa && deviceAnalytics == null) {
            Logger.d(TAG, "checkPayloadData DeviceAnalytics contains null");
            return false;
        }
        if (currentAddress == null) {
            Logger.d(TAG, "checkPayloadData currentAddress is null");
            return false;
        }

        return true;
    }

    public void logDeviceInfo(RequestPayloadDataDeviceAnalytics data) {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logDeviceInfo requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        requestPayload.getData().setDeviceAnalytics(data);
        Logger.i(TAG, "logDeviceInfo deviceAnalytics: " + data);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logSpeakerSingle() {
        logSpeakerMode(APPNormalCount.SINGLE);
    }

    public void logSpeakerStereo() {
        logSpeakerMode(APPNormalCount.STEREO);
    }

    public void logSpeakerParty() {
        logSpeakerMode(APPNormalCount.PARTY);
    }

    public void logAppToneToggle() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppToneToggle requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppToneToggle();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppToneToggle(temp);
        Logger.d(TAG, "logAppToneToggle: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppMfbMode() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppMfbMode requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppMFBMode();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppMFBMode(temp);
        Logger.d(TAG, "logAppMfbMode: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppHfpToggle() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppHfpToggle requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppHFPToggle();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppHFPToggle(temp);
        Logger.d(TAG, "logAppHfpToggle: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppEqMode() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppEqMode requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppEQMode();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppEQMode(temp);
        Logger.d(TAG, "logAppEqMode: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logOtaTriggered() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logOtaTriggered requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Logger.d(TAG, "requestPayload: " + requestPayload);
        Logger.d(TAG, "requestPayload getData: " + requestPayload.getData());
        Logger.d(TAG, "requestPayload getAppAnalytics: " + requestPayload.getData().getAppAnalytics());
        Logger.d(TAG, "requestPayload getOtaTriggered: " + requestPayload.getData().getAppAnalytics().getOtaTriggered());
        Integer temp = requestPayload.getData().getAppAnalytics().getOtaTriggered();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setOtaTriggered(temp);
        Logger.d(TAG, "logOtaTriggered: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logOtaSuccessful() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logOtaSuccessful requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getOtaSuccessful();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setOtaSuccessful(temp);
        Logger.d(TAG, "logOtaSuccessful: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logOtaUnsuccessful() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logOtaUnsuccessful requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getOtaUnsuccessful();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setOtaUnsuccessful(temp);
        Logger.d(TAG, "logOtaUnsuccessful: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logOtaDuration(int duration) {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logOtaDuration requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getOtaDuration();
        if (temp == null) {
            temp = Integer.valueOf(0);
        }
        temp += duration;
        requestPayload.getData().getAppAnalytics().setOtaDuration(temp);
        Logger.d(TAG, "logOtaDuration: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppVolume(int volume) {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppVolume requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        requestPayload.getData().getAppAnalytics().setAppVolume(volume);
        Logger.d(TAG, "logAppVolume: " + volume);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppDurationJblConnect(int seconds) {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppDurationJblConnect requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppDurationJBLConnect();
        if (temp == null) {
            temp = 0;
        }
        temp += seconds;
        requestPayload.getData().getAppAnalytics().setAppDurationJBLConnect(temp);
        Logger.d(TAG, "logAppDurationJblConnect: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logCustomizeEQ() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logCustomizeEQ requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getCustomizeEQ();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setCustomizeEQ(temp);
        Logger.d(TAG, "logCustomizeEQ: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight1() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight1 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT1();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT1(temp);
        Logger.d(TAG, "logAppLight1: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight2() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight2 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT2();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT2(temp);
        Logger.d(TAG, "logAppLight2: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight3() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight3 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT3();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT3(temp);
        Logger.d(TAG, "logAppLight3: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight4() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight4 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT4();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT4(temp);
        Logger.d(TAG, "logAppLight4: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight5() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight5 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT5();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT5(temp);
        Logger.d(TAG, "logAppLight5: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight6() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight6 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT6();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT6(temp);
        Logger.d(TAG, "logAppLight6: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight7() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight7 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT7();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT7(temp);
        Logger.d(TAG, "logAppLight7: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight8() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight8 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT8();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT8(temp);
        Logger.d(TAG, "logAppLight8: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight9() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight9 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT9();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT9(temp);
        Logger.d(TAG, "logAppLight9: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    public void logAppLight10() {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logAppLight10 requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        Integer temp = requestPayload.getData().getAppAnalytics().getAppLightT10();
        if (temp == null) {
            temp = 0;
        }
        temp += 1;
        requestPayload.getData().getAppAnalytics().setAppLightT10(temp);
        Logger.d(TAG, "logAppLight10: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    private void logSpeakerMode(APPNormalCount mode) {
        RequestPayload requestPayload = StorageManager.getInstance().getCurRequestPayload(currentAddress);
        if (requestPayload == null) {
            Logger.e(TAG, "logSpeakerMode requestPayload is null, currentAddress: " + currentAddress);
            return;
        }

        RequestPayloadDataAppAnalyticsSpeakerMode temp = requestPayload.getData().getAppAnalytics().getSpeakerMode();
        if (temp == null) {
            temp = new RequestPayloadDataAppAnalyticsSpeakerMode();
            temp.setStereo(0);
            temp.setParty(0);
            temp.setSingle(0);
        }

        if (mode == APPNormalCount.SINGLE) {
            temp.setSingle(temp.getSingle() + 1);
        } else if (mode == APPNormalCount.PARTY) {
            temp.setParty(temp.getParty() + 1);
        } else if (mode == APPNormalCount.STEREO) {
            temp.setStereo(temp.getStereo() + 1);
        }

        requestPayload.getData().getAppAnalytics().setSpeakerMode(temp);
        Logger.d(TAG, "logSpeakerMode: " + temp);
        StorageManager.getInstance().updateCurRequestPayload(currentAddress, requestPayload);
    }

    private boolean checkHarmanDevice(RequestPayloadDataHarmanDevice harmanDevice) {
        return harmanDevice.getMacAddress() != null && harmanDevice.getFirmwareVersion() != null && harmanDevice.getProductName() != null && harmanDevice.getColorName() != null;
    }

}
