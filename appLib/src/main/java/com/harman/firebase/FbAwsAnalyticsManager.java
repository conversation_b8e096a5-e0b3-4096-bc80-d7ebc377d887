package com.harman.firebase;


import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.harman.log.Logger;
import com.harman.utils.LibUtils;
import com.harman.utils.SharePreferenceHelper;
import com.jbl.awsdataanalysisilib.api.IBaseDataAnalytics;
import com.jbl.awsdataanalysisilib.api.IDataAnalyticsListener;
import com.jbl.awsdataanalysisilib.imp.BaseDataAnalyticManager;
import com.jbl.awsdataanalysisilib.imp.HPDataAnalyticManager;
import com.jbl.awsdataanalysisilib.imp.SADataAnalyticManager;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

import lsaudio.model.RequestPayloadDataDeviceAnalytics;


public class FbAwsAnalyticsManager {
    public static final String TAG = "FbAwsAnalyticsManager";
    public static boolean isShownUpdateAvailable = false;
    public static boolean isFirwareVersionShowned = false;
    public static BaseDataAnalyticManager.SOURCE analyticsSource = BaseDataAnalyticManager.SOURCE.HEADPHONE;

    //for aws
    public static void initAWSLib(Context context) {
        analyticsSource = BaseDataAnalyticManager.SOURCE.HEADPHONE;
        HPDataAnalyticManager.getInstance().init(context);
    }

    public static void initAWSLib(Context context, BaseDataAnalyticManager.SOURCE source) {
        analyticsSource = source;
        if (BaseDataAnalyticManager.SOURCE.HEADPHONE == analyticsSource) {
            HPDataAnalyticManager.getInstance().init(context);
        } else if (BaseDataAnalyticManager.SOURCE.SMART_AUDIO == analyticsSource) {
            SADataAnalyticManager.getInstance().init(context);
        }
    }

    public static String getAwsLastUploadedJson() {
        return HPDataAnalyticManager.getInstance().getLastUploadedJson();
    }

    public static String getSAAwsLastUploadedJson() {
        return SADataAnalyticManager.getInstance().getLastUploadedJson();
    }

    /**
     * More than 1 minute can upload
     * @return true means allow to upload
     *         false means don't allow to upload
     */
    public static boolean checkUploadInterval(Context context) {
        if (context == null) {
            return false;
        }
        if (BaseDataAnalyticManager.SOURCE.SMART_AUDIO == analyticsSource) {
            return SADataAnalyticManager.getInstance().checkUploadInterval(context);
        } else {
            return HPDataAnalyticManager.getInstance().checkUploadInterval(context);
        }
    }

    public static void initCurrentSmartAudioDevice(AwsData awsData) {
        if (!awsData.isSupport) {
            Logger.d(TAG, "initCurrentDevice device is not support");
            return;
        }
        SADataAnalyticManager.getInstance().setSupport(awsData.isSupport);
        Logger.d(TAG, "initCurrentDevice deviceName: " + awsData.deviceName + ",myDevice.pid: " + awsData.pid);
        int pid = Integer.parseInt(awsData.pid, 16);
        Logger.d(TAG, "initCurrentDevice pid: " + pid);
        IBaseDataAnalytics.CUR_DEVICE_STATUS curDeviceStatus = SADataAnalyticManager.getInstance().setCurDevice(awsData.key
                , pid, awsData.deviceName, awsData.mid, awsData.midName, awsData.fwVersion);
    }

    public static void initCurrentDevice(AwsData awsData) {
        HPDataAnalyticManager.getInstance().setSupport(awsData.isSupport);
        if (!awsData.isSupport) {
            Logger.d(TAG, "initCurrentDevice device is not support");
            return;
        }

        //TODO: fetch url

        //TODO: get device data analytics
        Logger.d(TAG, "initCurrentDevice deviceName: " + awsData.deviceName + ",myDevice.pid: " + awsData.pid);
        int pid;
        try {
            pid = Integer.parseInt(awsData.pid, 16);
        } catch (NumberFormatException e) {
            pid = -1;
        }
        if (pid == -1){
            Logger.e(TAG, "initCurrentDevice error: pid is not right");
            return;
        }

        Logger.d(TAG, "initCurrentDevice pid: " + pid);
        IBaseDataAnalytics.CUR_DEVICE_STATUS curDeviceStatus = HPDataAnalyticManager.getInstance().setCurDevice(awsData.key
                , pid, awsData.deviceName, awsData.mid, awsData.midName, awsData.fwVersion);
        HPDataAnalyticManager.getInstance().logAppDeviceDiscovered();
        if (curDeviceStatus == IBaseDataAnalytics.CUR_DEVICE_STATUS.CREATED) {
            Logger.d(TAG, "initCurrentDevice set default values");
            HPDataAnalyticManager.getInstance().logAppANC(awsData.analyticsDefaultList.get(0));
            HPDataAnalyticManager.getInstance().logAppAmbientAware(awsData.analyticsDefaultList.get(1));
            HPDataAnalyticManager.getInstance().logAppTalkThru(awsData.analyticsDefaultList.get(2));
            HPDataAnalyticManager.getInstance().logAppAutoOff(awsData.analyticsDefaultList.get(3));
            HPDataAnalyticManager.getInstance().logAppAwareness(awsData.analyticsDefaultList.get(4));
            HPDataAnalyticManager.getInstance().logAppSmartAssistant(awsData.analyticsDefaultList.get(5));
            HPDataAnalyticManager.getInstance().logAppVoicePrompt(awsData.analyticsDefaultList.get(6));
            HPDataAnalyticManager.getInstance().logAppSmartButton(awsData.analyticsDefaultList.get(7));
            HPDataAnalyticManager.getInstance().logAppSmartAmbient(awsData.analyticsDefaultList.get(8));
            HPDataAnalyticManager.getInstance().logPersoniFi(awsData.analyticsDefaultList.get(9));
        }
    }

    public static void uploadSADataToServer(Context context, final boolean isSupportDeviceDa, final AwsListener awsListener) {
        boolean isDeviceDisconnectedLastTime = SharePreferenceHelper.INSTANCE.getBoolean(context, AwsConst.IS_DEVICE_CONNECTED, true);
        Logger.d(TAG, "uploadToServer isDeviceDisconnectedLastTime: " + isDeviceDisconnectedLastTime);
        if (isDeviceDisconnectedLastTime) {
            SharePreferenceHelper.INSTANCE.setBoolean(context, AwsConst.IS_DEVICE_CONNECTED, false);
            SADataAnalyticManager.getInstance().uploadToServer(context, new IDataAnalyticsListener() {
                @Override
                public void onUploadServer(boolean isSuccess) {
                    if (awsListener != null) {
                        awsListener.onUploadServer(isSuccess);
                    }
                }

                @Override
                public boolean isSupportDeviceDa() {
                    return isSupportDeviceDa;
                }
            });
        }
    }

    public static void uploadToServer(Context context, final boolean isSupportDeviceDa, final AwsListener awsListener) {
        boolean isDeviceDisconnectedLastTime = SharePreferenceHelper.INSTANCE.getBoolean(context, AwsConst.IS_DEVICE_CONNECTED, true);
        Logger.d(TAG, "uploadToServer isDeviceDisconnectedLastTime: " + isDeviceDisconnectedLastTime);
        if (isDeviceDisconnectedLastTime) {
            SharePreferenceHelper.INSTANCE.setBoolean(context, AwsConst.IS_DEVICE_CONNECTED, false);
            HPDataAnalyticManager.getInstance().uploadToServer(context, new IDataAnalyticsListener() {
                @Override
                public void onUploadServer(boolean isSuccess) {
                    if (awsListener != null) {
                        awsListener.onUploadServer(isSuccess);
                    }
                }

                @Override
                public boolean isSupportDeviceDa() {
                    return isSupportDeviceDa;
                }
            });
        }
    }

    public static void logDeviceInfo(String daData) {
        HPDataAnalyticManager.getInstance().logDeviceInfo(daData);
    }

    public static void logHeadphoneDeviceAnalytics(String daData) {
        HPDataAnalyticManager.getInstance().logHeadphoneDeviceAnalytics(daData);
    }

    public static void logSADeviceInfo(RequestPayloadDataDeviceAnalytics requestPayloadDataDeviceAnalytics) {
        SADataAnalyticManager.getInstance().logDeviceInfo(requestPayloadDataDeviceAnalytics);
    }

    // firebase
    //when report device module, device color, volume /awareness_level /club switcher report EQ or Dj at the same time/smart ambient AA/TT
    private static void logEvent(Context context, String eventName, Map<String, String> map) {
        Bundle bundle = new Bundle();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            bundle.putString(entry.getKey(), entry.getValue());
        }
//        FirebaseAnalytics.getInstance(context).logEvent(eventName, bundle);
    }

    public static void logEvent(Context context, String category, String action, Map<String, String> map) {
        Bundle bundle = new Bundle();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            bundle.putString(entry.getKey(), entry.getValue());
        }
//        FirebaseAnalytics.getInstance(context).logEvent(category, bundle);
    }

    public static void logEvent(Context context, String category, String action, Bundle bundle) {
//        FirebaseAnalytics.getInstance(context).logEvent(category, bundle);
    }

    public static void logContentEvent(Context context, String contentType, String itemId) {
        Bundle bundle = new Bundle();
        bundle.putString(FirebaseAnalytics.Param.CONTENT_TYPE, contentType);
        bundle.putString(FirebaseAnalytics.Param.ITEM_ID, itemId);
//        FirebaseAnalytics.getInstance(context).logEvent(FirebaseAnalytics.Event.SELECT_CONTENT, bundle);
    }

    //add EQ by user
    public static void reportNewEQ(Context context, String deviceName, String eqName, Activity activity, String screenName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportNewEQ:" + eqName.toLowerCase() + "deviceName:" + deviceName);
        HashMap<String, String> hm = new HashMap<>();
        //hm.put(LABEL, eqName.toLowerCase(Locale.ENGLISH));
        hm.put(LABEL, eqName.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "add_eq",  hm);
        logContentEvent(context, "add_eq", devNameFiltered);
        HPDataAnalyticManager.getInstance().logAppEQ(5);
        HPDataAnalyticManager.getInstance().logAppEQChange();
    }

    //modify EQ by user
    public static void reportModifyEQ(Context context, String deviceName, String eqName, Activity activity, String screenName) {
        Logger.d(TAG, "reportModifyEQ:" + eqName.toLowerCase());

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, eqName.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "modify_eq",  hm);
        logContentEvent(context, "modify_eq", devNameFiltered);
        HPDataAnalyticManager.getInstance().logAppEQChange();
    }

    private static void logTestEvent(Context context,String deviceName,String contentType, String itemId){
        Bundle bundle = new Bundle();
        Bundle item1 = new Bundle();
        item1.putString(FirebaseAnalytics.Param.ITEM_NAME, deviceName);
        item1.putString(FirebaseAnalytics.Param.ITEM_CATEGORY, contentType);
        item1.putString(FirebaseAnalytics.Param.ITEM_CATEGORY2, contentType);
        item1.putString(FirebaseAnalytics.Param.ITEM_ID, itemId);
        bundle.putParcelableArray(FirebaseAnalytics.Param.ITEMS, new Bundle[] {item1});
//        FirebaseAnalytics.getInstance(context).logEvent(FirebaseAnalytics.Event.SELECT_ITEM, bundle);
    }

    /**
     * select EQ by user. Please make setPreference "PreferenceKeys.CURR_EQ_NAME" after this function,otherwise
     * big data will not function well.
     *
     * @param eqName     eq name
     * @param activity   context
     * @param screenName not use now
     */
    public static void reportSelectedNewEQ(Context context, String deviceName, String eqName, int eqId, Activity activity, String screenName) {
        Logger.d(TAG, "reportSelectedNewEQ" + eqName.toLowerCase());

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        if (eqId== 5){
            hm.put(LABEL, "eq_custom");
            logContentEvent(context, "select_eq", "eq_custom");
            reportEQCustom(context, devNameFiltered, eqName.toLowerCase());
        }else{
            hm.put(LABEL, eqName.toLowerCase());
            logContentEvent(context, "select_eq", eqName.toLowerCase());
            hm.put(DEVICENAME, devNameFiltered);
            logEvent(context, "select_eq",  hm);
            reportEQ(context, eqName, devNameFiltered);
        }
//        logTestEvent(context,devNameFiltered, eqName, String.valueOf(eqId));
        hm.put(LABEL, String.valueOf(eqId));
        logEvent(context, "eq_id", hm);
        HPDataAnalyticManager.getInstance().logAppEQ(eqId);
        HPDataAnalyticManager.getInstance().logAppEQChange();
    }

    //select EQ Off by user
    private static void reportEQ(Context context, String eqName, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "eq_"+ eqName.toLowerCase(),  hm);
    }

    //select Custom EQ by user
    private static void reportEQCustom(Context context, String deviceName, String eqName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, "eq_custom");
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "eq_custom",  hm);
    }

    //distinguish get and push
    public static void reportANCToggle(Context context, String deviceName, boolean isOn, String actionSource) {
        HPDataAnalyticManager.getInstance().logAppANC(isOn ? 2 : 1);
        Logger.d(TAG, "reportANCToggle:" + isOn);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (isOn ? "on" : "off"));
        hm.put(ACTION_SOURCE, actionSource);
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "anc_toggle", hm);
        logContentEvent(context, "anc_toggle", (isOn ? "on" : "off"));
        logContentEvent(context, "anc_toggle_source", actionSource.toLowerCase());
        if (isOn) {
            reportANCOn(context, devNameFiltered);
        } else {
            reportANCOff(context, devNameFiltered);
        }
    }

    private static void reportANCOn(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "anc_on", hm);
    }

    private static void reportANCOff(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "anc_off",  hm);
    }

    public static void reportAwarenessLevelChanged(Context context, String deviceName, int value, boolean isLeft, Activity activity, String screenName) {
        Logger.d(TAG, "reportAwarenessLevelChanged:" + (isLeft ? "left" : "right") + ": " + value);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (isLeft ? "left" : "right") + ": " + value);
        hm.put(DEVICENAME, devNameFiltered);
        logContentEvent(context, "awareness_level_change", (isLeft ? "left" : "right") + ":" + value);
        logEvent(context, "awareness_level_change", hm);
    }

    //distinguish get and push
    public static void reportAwarenessLevel(Context context, String deviceName, String level, String actionSource, Activity activity, String screenName) {
        Logger.d(TAG, "reportAwarenessLevel:" + level);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, level.toLowerCase());
        hm.put(ACTION_SOURCE, actionSource.toLowerCase(Locale.ENGLISH));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "awareness_level",  hm);
        logContentEvent(context, "awareness_level", level.toLowerCase());
        logContentEvent(context, "awareness_level_source", actionSource.toLowerCase());
        boolean isOn = false;
        if (level.equals("off")) {
            reportAwarenessLevelOff(context, devNameFiltered);
            isOn = false;
        } else if (level.equals("low")) {
            reportAwarenessLevelLow(context, devNameFiltered);
            isOn = true;
        } else if (level.equals("medium")) {
            reportAwarenessLevelMedium(context, devNameFiltered);
            isOn = true;
        } else if (level.equals("high")) {
            reportAwarenessLevelHigh(context, devNameFiltered);
            isOn = true;
        }
        HPDataAnalyticManager.getInstance().logAppAwareness(isOn ? 2 : 1);
    }

    private static void reportAwarenessLevelOff(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "awareness_level_off",  hm);
    }

    private static void reportAwarenessLevelLow(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "awareness_level_low",  hm);
    }

    private static void reportAwarenessLevelMedium(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "awareness_level_medium",  hm);
    }

    private static void reportAwarenessLevelHigh(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "awareness_level_high",  hm);
    }

    //distinguish get and push
    public static void reportAmbientAwareMode(Context context, String deviceName, int value, String actionSource) {
        String name = "off";
        if (value == 1) {
            name = "talk_thru";
        } else if (value == 2) {
            name = "ambient_aware";
        }
        Logger.d(TAG, "reportAmbientAwareMode:" + name);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, name.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        hm.put(ACTION_SOURCE, actionSource);
        logEvent(context, "ambient_aware_mode", hm);
        logContentEvent(context, "ambient_aware_mode_source", actionSource.toLowerCase());
        if (name.equals("off")) {
            logContentEvent(context, "ambient_aware_mode", "off");
            reportAAmodeOff(context, devNameFiltered);
            HPDataAnalyticManager.getInstance().logAppAmbientAware(1);
            HPDataAnalyticManager.getInstance().logAppTalkThru(1);
        } else if (name.equals("talk_thru")) {
            logContentEvent(context, "ambient_aware_mode", "talk thru");
            reportAAmodeTalkThru(context, devNameFiltered);
            HPDataAnalyticManager.getInstance().logAppTalkThru(2);
            HPDataAnalyticManager.getInstance().logAppAmbientAware(1);
        } else if (name.equals("ambient_aware")) {
            logContentEvent(context, "ambient_aware_mode", "ambient aware");
            reportAAmodeAmbientAware(context, devNameFiltered);
            HPDataAnalyticManager.getInstance().logAppAmbientAware(2);
            HPDataAnalyticManager.getInstance().logAppTalkThru(1);
        }
    }


    //distinguish get and push
    public static void reportAmbientSoundControl(Context context, String deviceName, int value, String actionSource) {
        String name = "off";
        if (value == 2 ) {
            name = "talk_thru";
        } else if (value == 3) {
            name = "ambient_aware";
        }
        Logger.d(TAG, "reportAmbientAwareMode:" + name);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, name.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        hm.put(ACTION_SOURCE, actionSource);
        logEvent(context, "ambient_aware_mode",  hm);
        logContentEvent(context, "ambient_aware_mode_source", actionSource.toLowerCase());
        switch (name) {
            case "off":
                logContentEvent(context, "ambient_aware_mode", "off");
                reportAAmodeOff(context, devNameFiltered);
                HPDataAnalyticManager.getInstance().logAppAmbientAware(1);
                HPDataAnalyticManager.getInstance().logAppTalkThru(1);
                break;
            case "talk_thru":
                logContentEvent(context, "ambient_aware_mode", "talk thru");
                reportAAmodeTalkThru(context, devNameFiltered);
                HPDataAnalyticManager.getInstance().logAppTalkThru(2);
                HPDataAnalyticManager.getInstance().logAppAmbientAware(1);
                break;
            case "ambient_aware":
                logContentEvent(context, "ambient_aware_mode", "ambient aware");
                reportAAmodeAmbientAware(context, devNameFiltered);
                HPDataAnalyticManager.getInstance().logAppAmbientAware(2);
                HPDataAnalyticManager.getInstance().logAppTalkThru(1);
                break;
        }
    }

    private static void reportAAmodeOff(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "aamode_off",  hm);
    }

    private static void reportAAmodeTalkThru(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "aamode_talk_thru",  hm);
    }

    private static void reportAAmodeAmbientAware(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "aamode_ambient_aware",  hm);
    }

    public static void
    reportSmartAmbient(Context context, String deviceName, String modeName) {
        Logger.d(TAG, "reportSmartAmbient:" + modeName);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, modeName.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "smart_ambient_mode",  hm);
        int smartAa = 0;
        if (modeName.equalsIgnoreCase("talk_thru")) {
            logContentEvent(context, "smart_ambient_mode", "talk thru");
            smartAa = 2;
        } else if (modeName.equals("ambient_aware")) {
            logContentEvent(context, "smart_ambient_mode", "ambient aware");
            smartAa = 3;
        }
        HPDataAnalyticManager.getInstance().logAppSmartAmbient(smartAa);
    }


    public static void reportAutoCal(Context context, String deviceName, String label, Activity activity, String screenName) {
        Logger.d(TAG, "reportAutoCal:" + label);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, label);
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "true_note",  hm);
        if (label.equals("start")) {
            reportTrueNoteStart(context, devNameFiltered);
            HPDataAnalyticManager.getInstance().logAppAutoCalibration();
        } else if (label.equals("success")) {
            reportTrueNoteSuccess(context, devNameFiltered);
        } else if (label.equals("fail")) {
            reportTrueNoteFail(context, devNameFiltered);
        }
    }

    private static void reportTrueNoteStart(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "true_note_start",  hm);
    }

    private static void reportTrueNoteSuccess(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "true_note_success", hm);
    }

    private static void reportTrueNoteFail(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "true_note_fail",  hm);
    }

    public static void reportCoachMarks(Context context, String deviceName, String label, Activity activity, String screenName) {
        Logger.d(TAG, "tutorial:" + label);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, label.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "tutorial",  hm);
        if (label.equals("started")) {
            reportTutorialStart(context, devNameFiltered);
        } else if (label.equals("completed")) {
            reportTutorialComplete(context, devNameFiltered);
        } else if (label.equals("skipped")) {
            reportTutorialSkipped(context, devNameFiltered);
        }
        logContentEvent(context, "tutorial", label.toLowerCase());
    }

    private static void reportTutorialStart(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "tutorial_started",  hm);
    }

    //when tutorial complete
    private static void reportTutorialComplete(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "tutorial_completed", hm);
    }

    private static void reportTutorialSkipped(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "tutorial_skipped", hm);
    }

    public static void reportAutoOffToggle(Context context, String deviceName, boolean isOn, String time) {
        Logger.d(TAG, "reportAutoOffToggle:" + isOn);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (isOn ? "on" : "off"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "auto_off_toggle",  hm);
        logContentEvent(context, "auto_off_toggle", (isOn ? "on" : "off"));
        if (isOn && time != null) {
            reportAutoOffEnable(context, devNameFiltered, time);
        } else {
            reportAutoOffDisable(context, devNameFiltered);
        }
        HPDataAnalyticManager.getInstance().logAppAutoOff(isOn ? 2 : 1);
    }

    private static void reportAutoOffEnable(Context context, String deviceName, String time) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        HashMap<String, String> hmtime = new HashMap<>();
        hmtime.put(TIMER, time);
        logEvent(context, "auto_off_enable",  hm);
        logEvent(context, "auto_off_enable",  hmtime);
    }

    private static void reportAutoOffDisable(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        HashMap<String, String> hmtime = new HashMap<>();
        hmtime.put(TIMER, "never");
        logEvent(context, "auto_off_disable",  hm);
        logEvent(context, "auto_off_disable",  hmtime);
    }

    public static void reportPlayPauseAutomationToggle(Context context, String deviceName, boolean isOn) {
        Logger.d(TAG, "reportPlayPauseAutomationToggle:" + isOn);
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (isOn ? "on" : "off"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "play_pause_automation_toggle",  hm);
        logContentEvent(context, "play_pause_automation_toggle", (isOn ? "on" : "off"));
        if (isOn) {
            reportPlayPauseAutomationEnable(context, devNameFiltered);
        } else {
            reportPlayPauseAutomationDisable(context, devNameFiltered);
        }
    }

    private static void reportPlayPauseAutomationEnable(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "play_pause_automation_on",  hm);
    }

    private static void reportPlayPauseAutomationDisable(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "play_pause_automation_off",  hm);
    }

    public static void reportVoicePromptToggle(Context context, String deviceName, boolean isOn, Activity activity, String screenName) {
        Logger.d(TAG, "reportVoicePromptToggle:" + isOn);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (isOn ? "on" : "off"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "voice_prompt_toggle",  hm);
        logContentEvent(context, "voice_prompt_toggle", (isOn ? "on" : "off"));
        if (isOn) {
            reportVoicePromptOn(context, devNameFiltered);
        } else {
            reportVoicePromptOff(context, devNameFiltered);
        }
        HPDataAnalyticManager.getInstance().logAppVoicePrompt(isOn ? 2 : 1);
    }

    private static void reportVoicePromptOn(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "voice_prompt_on",  hm);
    }

    private static void reportVoicePromptOff(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "voice_prompt_off",  hm);
    }

    public static void reportSmartButtonChange(Context context, String deviceName, String smartButtonTitle, Activity activity, String screenName) {
        Logger.d(TAG, "reportSmartButtonChange:" + smartButtonTitle);
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, smartButtonTitle);
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "smart_button_select",  hm);
        if (smartButtonTitle.equals("ambient_aware")) {
            logContentEvent(context, "smart_button_select", "ambient aware");
            reportSButtonAmbientAware(context, devNameFiltered);
            HPDataAnalyticManager.getInstance().logAppSmartButton(2);
        } else {
            logContentEvent(context, "smart_button_select", "noise cancellation");
            reportSButtonNoiseCancellation(context, devNameFiltered);
            HPDataAnalyticManager.getInstance().logAppSmartButton(3);
        }
    }

    private static void reportSButtonAmbientAware(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "smart_button_ambient_aware",  hm);
    }

    private static void reportSButtonNoiseCancellation(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "smart_button_noise_cancellation",  hm);
    }

    public static void reportAssistantTypeChange(Context context, String deviceName, EnumFBVAEventType enumFBVAEventType, Activity activity, String screenName) {
        Logger.d(TAG, "reportAssistantTypeChange:" + enumFBVAEventType);
        if(enumFBVAEventType == null){return;}
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        String presetName = "";
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, FireBaseSelectContentName.VOICE_ASSISTANT_SELECT_CONTENT,  hm);
        int type = 0;
        if (enumFBVAEventType == EnumFBVAEventType.OFF) {
            presetName = "voice_assistant_off";
            logContentEvent(context, FireBaseSelectContentName.VOICE_ASSISTANT_SELECT_CONTENT, "off");
            type = 1;
        } else if (enumFBVAEventType == EnumFBVAEventType.GA) {
            presetName = "voice_assistant_google";
            logContentEvent(context, FireBaseSelectContentName.VOICE_ASSISTANT_SELECT_CONTENT, "google assistant");
            type = 3;
        } else if (enumFBVAEventType == EnumFBVAEventType.ALEX) {
            presetName = "voice_assistant_alexa";
            logContentEvent(context, FireBaseSelectContentName.VOICE_ASSISTANT_SELECT_CONTENT, "amazon alexa");
            type = 2;
        } else if (enumFBVAEventType == EnumFBVAEventType.XIAOWEI) {
            presetName = "voice_assistant_xiaowei";
            logContentEvent(context, FireBaseSelectContentName.VOICE_ASSISTANT_SELECT_CONTENT, "xiaowei");
            type = 4;
        } else if(enumFBVAEventType == EnumFBVAEventType.NATIVE){
            presetName = "voice_assistant_native";
            logContentEvent(context, FireBaseSelectContentName.VOICE_ASSISTANT_SELECT_CONTENT, "native");
            type = 5;
        }
        reportVoiceAssistant(context, devNameFiltered,presetName,presetName);
        HPDataAnalyticManager.getInstance().logAppSmartAssistant(type);
    }

    private static void reportVoiceAssistant(Context context, String deviceName, String category, String actionID) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, category,  hm);
    }

    public static void reportVoiceAssistantHotword(Context context, String deviceName, String category, String actionID) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, category, hm);
    }

    public static void reportEvent(Context context, String categoryEventName, HashMap<String, String> labelMap) {
        Logger.d(TAG, " reportEvent, categoryEventName: " + categoryEventName + " labelList: " + labelMap);
        if (categoryEventName == null || context == null) return;
        HashMap<String, String> hm = new HashMap<>();
        if(labelMap != null && !labelMap.isEmpty()){
            Iterator<Map.Entry<String, String>> entryIterator = labelMap.entrySet().iterator();
            while (entryIterator.hasNext()){
                Map.Entry<String, String> entry = entryIterator.next();
                String labelName = entry.getKey();
                String labelValue = entry.getValue();
                if(DEVICENAME.equals(labelName)){
                    labelName = LibUtils.getFilterDeviceName(labelName);
                }
                hm.put(labelName, labelValue);
            }
        }
        logEvent(context, categoryEventName, hm);
        Logger.d(TAG, " reportEvent, "+" hm: "+hm.toString());
    }

    public static void reportSelectContent(Context context, String contentKey, String contentValue) {
        Logger.d(TAG, " reportSelectContent, contentKey: " + contentKey + " contentValue: " + contentValue);
        if(context == null || contentKey == null || contentValue == null){
            Logger.e(TAG, " there is null value, fail to report content. ");
            return;
        }
        logContentEvent(context, contentKey, contentValue);
    }

    public static void reportFaqUsage(Context context, String deviceName, String question) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, question);
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "faq_usage hm", hm);
    }


    public static void reportDeviceConnect(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportDeviceConnect:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (devNameFiltered != null ? devNameFiltered : "unknown"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "device_connect",  hm);
    }

    //only after connect can report disconnect, currently old device's disconnect  except 150 zis wrong
    public static void reportDeviceDisconnect(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportDeviceDisconnect:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (devNameFiltered != null ? devNameFiltered : "unknown"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "device_disconnect",  hm);
    }

    //the device module is not the same as device name
    public static void reportDeviceModule(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportDeviceModule:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, devNameFiltered);
        logEvent(context, "device_module",  hm);
        logContentEvent(context, "device_module", devNameFiltered);
    }

    public static void reportTopNClickDevice(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportTopNClickDevice:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(FbAwsAnalyticsManager.DEVICENAME, devNameFiltered);
        logEvent(context, FireBaseEventName.UNSUPPORTED_TOPN_CLICK_DEVICE_EVENT, hm);
    }

    public static void reportTopNClickQSG(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportTopNClickQSG:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(FbAwsAnalyticsManager.DEVICENAME, devNameFiltered);
        logEvent(context, FireBaseEventName.UNSUPPORTED_TOPN_CLICK_QSG_EVENT, hm);
    }

    public static void reportTopNClickSupport(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportTopNClickSupport:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(FbAwsAnalyticsManager.DEVICENAME, devNameFiltered);
        logEvent(context, FireBaseEventName.UNSUPPORTED_TOPN_CLICK_SUPPORT_EVENT, hm);
    }

    public static void reportTopNEnterModel(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportTopNEnterModel:" + devNameFiltered);
        logContentEvent(context, FireBaseSelectContentName.DEVICE_MODULE_CONTENT, devNameFiltered);
    }


    // report times bigger than connecting
    public static void reportVolumn(Context context, String deviceName, String volumn) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportVolumn:" + volumn + "deviceName:" + devNameFiltered);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, volumn.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "device_volume",  hm);
    }

    public static void reportDeviceColor(Context context, String deviceName, String color) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        Logger.d(TAG, "reportDeviceColor:" + color);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, color.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "device_color",  hm);
        logContentEvent(context, "device_color", color.toLowerCase());
    }

    // when report
    public static void reportFirmwareVersion(Context context, String deviceName, String version, Activity activity, String screenName) {
        if (!isFirwareVersionShowned) {
            isFirwareVersionShowned = true;
//            String deviceName = PreferenceUtils.getString(JBLConstant.KEY_DEVICE_NAME, JBLApplication.getJblAppContext(), JBLConstant.DEVICE_150NC);
            String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
            HashMap<String, String> hm = new HashMap<>();
            hm.put(LABEL, (version != null ? version : "unknown"));
            hm.put(DEVICENAME, devNameFiltered);
            logEvent(context, "firmware_version",  hm);
        }

    }

    public static void reportGestureLeft(Context context, String deviceName, String gestureString) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, gestureString.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "gesture_left",  hm);
        if (gestureString.equals("none")) {
            logContentEvent(context, "gesture_left", "none");
        } else if (gestureString.equals("talk_thru")) {
            logContentEvent(context, "gesture_left", "talk thru");
        } else if (gestureString.equals("ambient_aware")) {
            logContentEvent(context, "gesture_left", "ambient aware");
        }

    }

    public static void reportGestureRight(Context context, String deviceName, String gestureString) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, gestureString.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "gesture_right",  hm);
        logContentEvent(context, "gesture_right", gestureString.toLowerCase());
    }

    public static void reportClubStageSwitcherOn(Context context, String deviceName, int eqId) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "club_stage_switcher_on",  hm);
        logContentEvent(context, "club_stage_switcher", "on");

        reportAwsDJStagePreset(eqId);
    }

    public static void reportClubStageSwitcherOff(Context context, String deviceName, int eqId) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "club_stage_switcher_off",  hm);
        logContentEvent(context, "club_stage_switcher", "off");
        reportAwsDJStagePreset(eqId);
    }

    public static void reportDJSignature(Context context, String deviceName, String djSignature, int eqId) {
        Logger.d(TAG, "reportDJSignature:" + eqId +",djSignatureName: "+djSignature);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, djSignature.toLowerCase());
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "club_dj_signature",  hm);
        logContentEvent(context, "club_dj_signature", djSignature.toLowerCase());
        reportAwsDJStagePreset(eqId);
    }

    public static void reportFirmwareUpdateStarted(Context context, String deviceName, String version) {
        Logger.d(TAG, "reportFirmwareUpdateStarted:" + version);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (version != null ? version : "unknown"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "firmware_update_begun",  hm);
        logContentEvent(context, "firmware_update", "update begun");
        HPDataAnalyticManager.getInstance().logOTATriggered();
    }

    public static void reportFirmwareUpgradeSpeedMode(Context context, String deviceName, int speedMode) {
        Logger.d(TAG, "reportFirmwareUpgradeSpeedMode:" + speedMode + " deviceName: "+deviceName);
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        String speedModeName = "";
        if(speedMode == 2){
            speedModeName = "firmware_update_fast";
            logContentEvent(context,FireBaseSelectContentName.OTA_MODE_CONTENT,"fast");
        } else {
            speedModeName = "firmware_update_normal";
            logContentEvent(context,FireBaseSelectContentName.OTA_MODE_CONTENT,"normal");
        }
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, speedModeName, hm);
    }

    public static void reportFirmwareUpdateComplete(Context context, String deviceName, String version) {
        Logger.d(TAG, "reportFirmwareUpdateComplete:" + version);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (version != null ? version : "unknown"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "firmware_update_finished",  hm);
        logContentEvent(context, "firmware_update", "update finished");
    }

    public static void reportFirmwareUpdateFailed(Context context, String deviceName, String reason) {
        Logger.d(TAG, "reportFirmwareUpdateFailed:" + reason);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, reason != null ? reason.toLowerCase() : "reason unknown");
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "firmware_update_failed",  hm);
        logContentEvent(context, "firmware_update", "update failed");
        HPDataAnalyticManager.getInstance().logOTAFail();
    }

    public static void reportOTADuration(Context context, String deviceName, int leftSize, int duration, String version) {
        Logger.d(TAG, "reportOTADuration:" + duration);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, duration + "min");
        hm.put(DEVICENAME, devNameFiltered);
        hm.put("firmware_version", version);
        logEvent(context, "firmware_update_duration",  hm);
        if (duration <= 3) {
            logContentEvent(context, "firmware_update_duration", "less than 3min");
        } else if (duration <= 7) {
            logContentEvent(context, "firmware_update_duration", "about 5min");
        } else if (duration <= 12) {
            logContentEvent(context, "firmware_update_duration", "about 10min");
        } else if (duration <= 17) {
            logContentEvent(context, "firmware_update_duration", "about 15min");
        } else if (duration <= 22) {
            logContentEvent(context, "firmware_update_duration", "about 20min");
        } else if (duration <= 27) {
            logContentEvent(context, "firmware_update_duration", "about 25min");
        } else {
            logContentEvent(context, "firmware_update_duration", "more than 27min");
        }
        HPDataAnalyticManager.getInstance().logOTASuccess(leftSize, duration);
    }


    public static void logToneTriggered(Context context, String deviceName, String version) {
        Logger.d(TAG, "logToneTriggered:" + version);

//        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
//        HashMap<String, String> hm = new HashMap<>();
//        hm.put(LABEL, (version != null ? version : "unknown"));
//        hm.put(DEVICENAME, devNameFiltered);
//        logEvent(context, "firmware_update_begun",  hm);
//        logContentEvent(context, "firmware_update", "update begun");
        HPDataAnalyticManager.getInstance().logToneTriggered();
    }

    public static void logToneSuccess(Context context, String deviceName, int leftSize, int duration, String version) {
        Logger.d(TAG, "logToneSuccess:" + duration);

//        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
//        HashMap<String, String> hm = new HashMap<>();
//        hm.put(LABEL, duration + "min");
//        hm.put(DEVICENAME, devNameFiltered);
//        hm.put("firmware_version", version);
//        logEvent(context, "firmware_update_duration",  hm);
//        if (duration <= 3) {
//            logContentEvent(context, "firmware_update_duration", "less than 3min");
//        } else if (duration <= 7) {
//            logContentEvent(context, "firmware_update_duration", "about 5min");
//        } else if (duration <= 12) {
//            logContentEvent(context, "firmware_update_duration", "about 10min");
//        } else if (duration <= 17) {
//            logContentEvent(context, "firmware_update_duration", "about 15min");
//        } else if (duration <= 22) {
//            logContentEvent(context, "firmware_update_duration", "about 20min");
//        } else if (duration <= 27) {
//            logContentEvent(context, "firmware_update_duration", "about 25min");
//        } else {
//            logContentEvent(context, "firmware_update_duration", "more than 27min");
//        }
        HPDataAnalyticManager.getInstance().logToneSuccess(leftSize, duration);
    }

    public static void logToneFail(Context context, String deviceName, String reason) {
        Logger.d(TAG, "logToneFail:" + reason);

//        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
//        HashMap<String, String> hm = new HashMap<>();
//        hm.put(LABEL, reason != null ? reason.toLowerCase() : "reason unknown");
//        hm.put(DEVICENAME, devNameFiltered);
//        logEvent(context, "firmware_update_failed",  hm);
//        logContentEvent(context, "firmware_update", "update failed");
        HPDataAnalyticManager.getInstance().logToneFail();
    }

    public static void reportLanguageSelected(Context context, String deviceName, String language) {
        Logger.d(TAG, "reportLanguageSelected:" + language);

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, language != null ? language : "language unknown");
        if (!TextUtils.isEmpty(devNameFiltered)) {
            hm.put(DEVICENAME, devNameFiltered);
        }
        logEvent(context, "language_select",  hm);
        logContentEvent(context, "language_select", language);
    }

    /*public static void reportUsbUpdateAlert(String reason, Activity activity, String screenName) {
        String deviceName = PreferenceUtils.getString(JBLConstant.KEY_DEVICE_NAME, JBLApplication.getJblAppContext(), JBLConstant.DEVICE_150NC);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(LABEL, (reason != null ? reason : "reason unknown"));
        hm.put(DEVICENAME, devNameFiltered);
        logEvent("alerted_user_of_USB_update",  hm);
    }*/


    /**
     * Only for big data
     *
     * @param eqId eq ID
     */
    private static void reportAwsDJStagePreset(int eqId) {
//        HPDataAnalyticManager.getInstance().logAppDJStage(position);
        HPDataAnalyticManager.getInstance().logAppEQ(eqId);
//        int preIdx = DjClubParse.getFocusedIndex();
        Logger.d(TAG, "reportAwsDJStagePreset eqId: " + eqId);
        HPDataAnalyticManager.getInstance().logAppDJStageChange();
    }

    /**
     * Only for big data
     */
    public static void reportTriggerFindMyBuds(Context context, String deviceName) {

        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "find_my_buds",  hm);
        HPDataAnalyticManager.getInstance().logAppFindMyBuds();
    }

    public static void reportAppRatingPopup(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "app_rating_popup",  hm);
    }

    public static void reportAppRatingSelected(Context context, String action) {
        logContentEvent(context, "app_rating", action);
    }

    /**
     * report screen name
     *
     * @param screenName
     */
    public static void reportScreenName(Activity activity, String screenName) {
//        Bundle params = new Bundle();
//        params.putString(FirebaseAnalytics.Param.ITEM_CATEGORY, "JBL_HeadPhones");
//        params.putString(FirebaseAnalytics.Param.ITEM_NAME, screenName);
        FirebaseAnalytics.getInstance(activity).setCurrentScreen(activity, screenName, activity.getLocalClassName());
    }

    /**
     * report personifi ON/OFF
     *
     * @param personiFi -1/0/1/2 - UnSupported/Default/OFF/ON
     */
    public static void reportPersoniFiStatus(Context context, int personiFi) {
        HPDataAnalyticManager.getInstance().logPersoniFi(personiFi);
        if(personiFi == 2) {
            logContentEvent(context, "personifi", "on");
        } else if(personiFi == 1) {
            logContentEvent(context, "personifi", "off");
        }
        Logger.d(TAG, "reportPersoniFiStatus: " + personiFi);
        HPDataAnalyticManager.getInstance().logPersoniFi(personiFi);
    }

    public static void reportPeroniFiStartSetup(Context context, String deviceName) {
        String devNameFiltered = LibUtils.getFilterDeviceName(deviceName);
        HashMap<String, String> hm = new HashMap<>();
        hm.put(DEVICENAME, devNameFiltered);
        logEvent(context, "personifi_start_setup",  hm);

        HPDataAnalyticManager.getInstance().logPersonifiStartSetup();
    }

    /**
     * report PersoniFi setup completed
     * Auto-increase by one time
     */
    public static void reportPersoniFiSetup() {
        HPDataAnalyticManager.getInstance().logAppPersoniFiSetup();
        Logger.d(TAG, "reportPersoniFiSetup completed");
    }

    public static void reportPersoniFiProfileStatus(Context context, String status) {
        Logger.d(TAG, "reportPersoniFiProfileStatus: " + status);
        logContentEvent(context, "personifi_profile", status.toLowerCase());
        switch (status){
            case "started":{
                HPDataAnalyticManager.getInstance().logProfileStarted();
                break;
            }
            case "cancelled":{
                HPDataAnalyticManager.getInstance().logProfileCanceled();
                break;
            }
            case "completed":{
                HPDataAnalyticManager.getInstance().logProfileCompleted();
                break;
            }
        }
    }

    public static void reportPersoniFiHTStatus(Context context, String status) {
        Logger.d(TAG, "reportPersoniFiHTStatus: " + status);
        logContentEvent(context, "personifi_hearing_test", status.toLowerCase());
        switch (status){
            case "started":{
                HPDataAnalyticManager.getInstance().logHearingTestStarted();
                break;
            }
            case "cancelled":{
                HPDataAnalyticManager.getInstance().logHearingTestCanceled();
                break;
            }
            case "completed":{
                HPDataAnalyticManager.getInstance().logHearingTestCompleted();
                break;
            }
        }
    }

    private final static String CATEGORY = "category";
    private final static String ACTION = "action";
    private final static String LABEL = "action_label";
    private final static String ACTION_SOURCE = "action_source";
    public final static String DEVICENAME = "device_name";
    public final static String TIMER = "timer";
    public final static String MODE = "mode";
    public final static String MUSIC_ON_TIME = "music_on_time";
    public final static String SILENT_TIME = "silent_time";

    public static final String SCREEN_INFO = "Information";
    public static final String SCREEN_JBL_DOT_COM = "JBL.com";
    public static final String SCREEN_OPEN_SOURCE = "Open Source licenses";
    public static final String SCREEN_EULA = "EULA";
    public static final String SCREEN_PRIVACY_STATEMENT = "Harman privacy statement";
    public static final String SCREEN_MY_PRODUCTS = "My products";
    public static final String SCREEN_DEVICE_SEARCH = "Device searching";
    public static final String SCREEN_PRODUCTS_GUIDE = "Products guide";
    public static final String SCREEN_HOW_TO_PAIR = "How to pair";
    public static final String SCREEN_HOW_TO_PAIR_NEXT = "How to pair next";
    public static final String SCREEN_BT_SETTINGS = "Phone's Bt settings";
    public static final String SCREEN_APP_SETTINGS = "App settings";
    public static final String SCREEN_PROGRAMMABLE_SMART_BUTTON = "Programmable smart button";
    public static final String SCREEN_AUTO_OFF = "Auto off";
    public static final String SCREEN_PRODUCT_HELP = "Product help";
    public static final String SCREEN_FAQ = "FAQ";
    public static final String SCREEN_UPDATE_DEVICE = "Update device";
    public static final String SCREEN_VOICE_ASSISTANT = "Voice assistant";
    public static final String SCREEN_VOICE_ASSISTANT_TUTORIAL = "Voice assistant tutorial";
    public static final String SCREEN_DO_IT_LATER = "Do it later";
    public static final String SCREEN_EQ_SETTINGS = "EQ settings";
    public static final String SCREEN_EDIT_EQ = "Edit EQ";
    public static final String SCREEN_EQ_MORE = "EQ more";
    public static final String SCREEN_CONTROL_PANEL = "Control Panel";
    public static final String SCREEN_LAUNCH = "Launch";
    public static final String SCREEN_LICENCE_AGREE = "Licence agree";
    public static final String SCREEN_TUTORIAL = "Tutorial";
    public static final String SCREEN_TRUNOTE = "TruNote";
    public static final String SCREEN_AMBIENT_AWARENESS = "Ambient awareness";
    public static final String SCREEN_LANGUAGE_SELECT = "Language select";
    public static final String SCREEN_SOUNDX_READY = "Soundx ready";
    public static final String SCREEN_SOUNDX_REAL_TIME_NOISE_LEVEL = "Soundx real-time noise level";
    public static final String SCREEN_SOUNDX_QUESTION = "Soundx question";
    public static final String SCREEN_SOUNDX_SWITCH_ON_OFF = "Soundx switch on-off";
    public static final String SCREEN_SOUNDX_ADVANCED_TUNING = "Soundx advanced tuning";
    public static final String SCREEN_STANDBY= "Standby";

    //for AKG
    public static final String SCREEN_WALK_THROUGH = "WalkThrough";
    public static final String SCREEN_DASHBOARD = "Control Panel";
    public static final String SCREEN_CONNECT = "Connect";
    public static final String SCREEN_EQ_START = "EQ Start";
    public static final String SCREEN_EQ_Edit = "EQ Edit";
    public static final String SCREEN_EQ_MANAGER = "EQ Manager";
    public static final String SCREEN_EQ_SETTING = "EQ settings";
    public static final String SCREEN_SETTINGS = "App settings";
    public static final String SCREEN_UPGRADE = "Upgrade";
    public static final String SCREEN_PRIVACY_POLICY = "Harman privacy statement";
    public static final String SCREEN_TIPS = "Tips";

    // for in-app rating
    public static final String RATING_POPUP = "popup";
    public static final String RATING_CANCEL = "cancel";
    public static final String RATING_NEVER = "never";
    public static final String RATING_GO_STORE = "go_store";

    public static final String EVENT_NAME_DASHBOARD_CONNECTED = "dashboard_connected";
    public static final String EVENT_NAME_DASHBOARD_CONNECTING = "dashboard_connecting";
    public static final String EVENT_NAME_DASHBOARD_OTA_CARD = "dashboard_ota";
    public static final String EVENT_NAME_DASHBOARD_PARTY_BOOST = "dashboard_partyboost";
    public static final String EVENT_NAME_DASHBOARD_RESET_COLOR = "dashboard_reset_color";
    public static final String EVENT_NAME_DASHBOARD_SELECT_COLOR = "dashboard_select_color";
    public static final String EVENT_NAME_DASHBOARD_LIGHT_OFF = "dashboard_light_off";
    public static final String EVENT_NAME_DASHBOARD_ADJUST_LIGHT = "dashboard_adjust_light";
    public static final String EVENT_NAME_DASHBOARD_LIGHT_SHOW_SELECTION = "dashboard_lightshow_selection";
    public static final String EVENT_NAME_DASHBOARD_FEEDBACK_TONE = "dashboard_feedback_tone";
    public static final String EVENT_NAME_DASHBOARD_USER_GUIDE = "dashboard_user_guide";
    public static final String EVENT_NAME_DASHBOARD_RENAME = "dashboard_rename";
    public static final String EVENT_NAME_DASHBOARD_EQ = "dashboard_eq";
    public static final String EVENT_NAME_DASHBOARD_HAMBURGER_MENU = "dashboard_hamburger_menu";
    public static final String EVENT_NAME_SOUND_MODE_INDOOR = "sound_mode_indoor";
    public static final String EVENT_NAME_SOUND_MODE_OUTDOOR = "sound_mode_outdoor";
    public static final String EVENT_NAME_SPEAKER_PHONE_ON = "speaker_phone_on";
    public static final String EVENT_NAME_SPEAKER_PHONE_OFF = "speaker_phone_off";
    public static final String EVENT_NAME_AUDIO_FEEDBACK_ON = "audio_feedback_on";
    public static final String EVENT_NAME_AUDIO_FEEDBACK_OFF = "audio_feedback_off";
    public static final String EVENT_NAME_NEARBY_SPEAKER_DISCOVERIED = "nearby_speaker_discoveried";
    public static final String EVENT_NAME_NEARBY_SPEAKER_CONNECTING = "nearby_speaker_connecting";
    public static final String EVENT_NAME_NEARBY_SPEAKER_CONNECTED = "nearby_speaker_connected";
    public static final String EVENT_NAME_PARTY_BOOST_REFRESH_STARTED = "partyboost_refresh_started";
    public static final String EVENT_NAME_PARTY_BOOST_REFRESH_ACTION = "partyboost_refresh_action";
    public static final String EVENT_NAME_PARTY_BOOST_REFRESH_SUCCESSFUL = "partyboost_refresh_successful";
    public static final String EVENT_NAME_PARTY_BOOST_SWITCH_CHANNEL = "partyboost_switch_channel";
    public static final String EVENT_NAME_PARTY_BOOST_PARTY = "partyboost_party";
    public static final String EVENT_NAME_PARTY_BOOST_STEREO = "partyboost_stereo";
    public static final String EVENT_NAME_PARTY_BOOST_HELP = "partyboost_help";
    public static final String EVENT_NAME_PARTY_BOOST_SPEAKER_COUNT = "partyboost_speaker_count";
    public static final String EVENT_NAME_MFB_FUNCTION_PLAY_PAUSE = "mfb_function_play_pause";
    public static final String EVENT_NAME_MFB_FUNCTION_VOICE_ASSISTANCE = "mfb_function_voice_assistance";
    public static final String EVENT_NAME_RENAME_SUCCESS = "rename_success";
    public static final String EVENT_NAME_RENAME_FAILED = "rename_failed";
    public static final String EVENT_NAME_OTA_CANCELLED = "ota_cancelled";
    public static final String EVENT_NAME_OTA_FAILED = "ota_failed";
    public static final String EVENT_NAME_OTA_SUCCESS = "ota_success";
    public static final String EVENT_NAME_OTA_STARTED = "ota_started";

    public static final String CONTENT_TYPE_SPEAKER_MODEL = "speaker_model";
    public static final String CONTENT_TYPE_PARTY_BOOST_TOTAL_DEVICE_COUNT = "partyboost_total_device_count";
    public static final String CONTENT_TYPE_BRIGHTNESS = "brightness";
    public static final String CONTENT_TYPE_OTA_STATUS = "ota";
    public static final String CONTENT_TYPE_RENAME_STATUS = "rename";
    public static final String CONTENT_TYPE_MFB_FUNCTION_STATUS = "mfb_function";
    public static final String CONTENT_TYPE_AUDIO_MODE_STATUS = "audio_mode";
    public static final String CONTENT_TYPE_AUDIO_FEED_BACK_STATUS = "audio_feedback";
    public static final String CONTENT_TYPE_SPEAKER_PHONE_STATUS = "speaker_phone";
    public static final String CONTENT_TYPE_SOUND_MODE_STATUS = "sound_mode";
    public static final String CONTENT_TYPE_LANGUAGE_SELECTION = "language_selection";
    public static final String CONTENT_TYPE_ACTIVE_LIGHT_THEME = "active_light_theme";
    public static final String CONTENT_TYPE_OTA_DURATION = "ota_duration";

    // for feedback
    public static final String EVENT_KEY_PRODUCT_NAME = "product_name";
    public static final String EVENT_KEY_APP_FEEDBACK = "app_feedback";
    public static final String EVENT_KEY_FEEDBACK_NAME = "feedback_name";

    public static final String EVENT_KEY_SELECTED_THEME_NAME = "selected_theme_name";
    public static final String EVENT_KEY_TONE_STATUS = "tone_status";
    public static final String EVENT_KEY_EQ_STATUS = "eq_status";
    public static final String EVENT_KEY_TOTAL_DEVICE_COUNT = "total_device_count";
    public static final String EVENT_KEY_FIRMWARE_VERSION = "firmware_version";
    public static final String EVENT_KEY_SERVER_VERSION = "server_version";
}
